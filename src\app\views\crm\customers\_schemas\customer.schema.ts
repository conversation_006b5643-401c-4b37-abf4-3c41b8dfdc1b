import { isValidPhoneNumber } from "libphonenumber-js";
import { z } from "zod";

export type DocumentType = "CPF" | "CNPJ" | "RG" | "CNH";

export const customerSchema = z.object({
  id: z.string().optional(),
  name: z.string().refine((value) => value.trim().length, "Nome é obrigatório"),
  documentType: z.union([
    z.literal("CPF"),
    z.literal("CNPJ"),
    z.literal("RG"),
    z.literal("CNH"),
  ]) as z.ZodType<DocumentType>,
  document: z.string().refine((value) => {
    const cpfRegex = /^\d{3}\.\d{3}\.\d{3}-\d{2}$/;
    const cnpjRegex = /^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/;
    const rgRegex = /^\d{2}\.\d{3}\.\d{3}-\d$/;
    const cnhRegex = /^\d{11}$/;
    return (
      cpfRegex.test(value) ||
      cnpjRegex.test(value) ||
      cnhRegex.test(value) ||
      rgRegex.test(value)
    );
  }, "Documento inválido"),
  phone: z
    .string()
    .optional()
    .refine(
      (value) => (value ? isValidPhoneNumber(value, "BR") : true),
      "Número de telefone inválido"
    ),
  email: z.string().email("Email inválido"),
  observation: z.string().optional(),
  cep: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
});

export type CustomerSchema = z.infer<typeof customerSchema>;
