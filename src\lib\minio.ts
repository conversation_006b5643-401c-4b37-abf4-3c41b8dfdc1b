import { DeleteObjectCommand, GetObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { Readable } from "stream";
import { streamToBuffer } from "../helpers/buffer";
const BUCKET_NAME = `${process.env.MINIO_BUCKET_NAME}`

export const minioClient = new S3Client({
  endpoint: `${process.env.MINIO_ENDPOINT}`,
  region: "us-east-1", // MinIO requires a region, but it can be any value
  credentials: {
    accessKeyId: `${process.env.MINIO_ROOT_USER}`,
    secretAccessKey: `${process.env.MINIO_ROOT_PASSWORD}`,
  },
  forcePathStyle: true, // Required for MinIO
  tls: process.env.MINIO_USE_SSL === "true",
});

export const bucketName = process.env.MINIO_BUCKET_NAME;


export async function deleteImageFromBucketAndKey(bucket: string, key: string): Promise<boolean> {
  try {
    await minioClient.send(
      new DeleteObjectCommand({
        Bucket: bucket,
        Key: key,
      })
    );
    return true;
  } catch (error) {
    console.error("Error deleting image from bucket:", error);
    return false;
  }
}

export async function deleteImageFromUrl(url: string): Promise<boolean> {
  try {
    const key = getKeyFromUrl(url);
    if (!key) return false;

    await minioClient.send(
      new DeleteObjectCommand({
        Bucket: BUCKET_NAME,
        Key: key,
      })
    );
    return true;
  } catch (error) {
    console.error("Error deleting image from bucket:", error);
    return false;
  }
}

export function getKeyFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url);
    const path = urlObj.pathname.startsWith('/') ? urlObj.pathname.slice(1) : urlObj.pathname;
    const parts = path.split('/');
    if (parts[0] === BUCKET_NAME) {
      return parts.slice(1).join('/');
    }
    return path;
  } catch (error) {
    console.error("Error parsing image URL:", error);
    return null;
  }
}


export async function getBufferFromFileKey(fileKey: string) {
  const bucketFIle = await minioClient.send(
    new GetObjectCommand({
      Bucket: `${BUCKET_NAME}`,
      Key: fileKey,
    })
  );

  const templateFile = bucketFIle.Body

  if (!templateFile) {
    throw new Error('Failed to retrieve template file from MinIO');
  }

  // Converter stream em buffer
  const bufferFile = await streamToBuffer(templateFile as Readable);
  return bufferFile
}