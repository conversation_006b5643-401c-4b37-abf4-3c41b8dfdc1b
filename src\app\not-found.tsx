"use client";

import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Home, MoveLeft } from "lucide-react";
import { useRouter } from "next/navigation";

export default function NotFound() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-lg w-full text-center">

        {/* Error Content */}
        <div className="bg-white p-8 rounded-2xl shadow-lg">
          {/* Error Code */}
          <h1 className="text-9xl font-bold text-green-500 mb-4">404</h1>

          {/* Error Message */}
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            Página não encontrada
          </h2>
          <p className="text-gray-600 mb-8">
            <PERSON>cul<PERSON>, a página que você está procurando não existe ou foi movida.
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={() => router.back()}
              variant="outline"
              className="border-green-500 text-green-500 hover:bg-green-50"
            >
              <MoveLeft className="mr-2 h-4 w-4" />
              Voltar
            </Button>
            <Button
              onClick={() => router.push("/views/control-panel")}
              className="bg-green-500 hover:bg-green-600"
            >
              <Home className="mr-2 h-4 w-4" />
              Ir para o início
            </Button>
          </div>
        </div>

        {/* Additional Help Text */}
        <p className="mt-8 text-gray-500 text-sm">
          Se você acredita que isso é um erro, por favor entre em contato com o
          suporte.
        </p>
      </div>
    </div>
  );
}