"use client";

import {
  generateReportWheaterImpact,
  getWeatherImpactFromProposalId,
} from "@/src/actions/wheater-impact";
import ContentWrapper from "@/src/components/content-wrapper";
import DownloadReportDialog from "@/src/components/download-report-dialog";
import { Button } from "@/src/components/ui/button";
import { formatDate } from "@/src/lib/utils";
import { WeatherImpactItem } from "@/src/types/core/wheather-impact";
import { useEffect, useState } from "react";
import WheatherImpactReportChart from "./_components/wheather-impact-report-chart";

type WeatherImpactPageProps = {
  params: {
    id: string;
  };
};

const WeatherImpactPage = ({ params }: WeatherImpactPageProps) => {
  const [loading, setLoading] = useState<boolean>();
  const [data, setData] = useState<WeatherImpactItem[]>();
  const [openUploadDialog, setOpenUploadDialog] = useState(false);

  const fetchChartData = async () => {
    try {
      setLoading(true);
      const chartData = await getWeatherImpactFromProposalId(params.id);
      const parsedData = chartData.map((item) => {
        return {
          ...item,
          data: formatDate(item.data),
          volume: item.volume || 0,
        };
      });
      setData(parsedData);
      console.log(parsedData);
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchChartData();
  }, []);

  return (
    <div className="relative">
      <Button
        className="absolute right-4 top-4 bg-green-500 hover:bg-green-600 self-end"
        onClick={() => setOpenUploadDialog(true)}
      >
        Gerar relatório
      </Button>

      <ContentWrapper
        id="wheather-impact-chart"
        title="Relatórios de Impacto Climático"
        loading={loading}
      >
        {data && <WheatherImpactReportChart chartData={data} />}
      </ContentWrapper>
      <DownloadReportDialog
        reportMergeAction={generateReportWheaterImpact}
        reportType="CLIMATE_IMPACT"
        additionalParamsOptional={{ proposalId: params.id }}
        openUploadDialog={openUploadDialog}
        setOpenUploadDialog={setOpenUploadDialog}
      />
    </div>
  );
};

export default WeatherImpactPage;
