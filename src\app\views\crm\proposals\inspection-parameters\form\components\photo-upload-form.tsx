import { CustomInput } from "@/src/components/app-input";
import <PERSON>U<PERSON>load<PERSON>ield from "@/src/components/file-upload-field";
import { FormProvider, UseFormReturn } from "react-hook-form";
import { PhotoSchema } from "../schemas/photo.schema";

interface PhotoUploadFormProps {
	methods: UseFormReturn<PhotoSchema>;
	onFileInputChange: (files: File[] | null) => void; // Aceita múltiplos arquivos
}

export default function PhotoUploadForm({
	methods,
	onFileInputChange,
}: PhotoUploadFormProps) {
	return (
		<FormProvider {...methods}>
			<form className="flex flex-col gap-6">
				<div className="grid grid-cols-1">
					<CustomInput
						label="Legenda"
						name="description"
						placeholder="Descrição"
					/>
				</div>
				<FileUploadField
					onFileChange={onFileInputChange}
					accept="image/*"
					label="Selecione uma ou mais imagens"
					multiple={true}
				/>
			</form>
		</FormProvider>
	);
}
