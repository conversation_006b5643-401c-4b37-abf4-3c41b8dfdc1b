"use client";

import { useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { checkRouteAccess } from "@/src/actions/route-permissions";
import { Shield, AlertTriangle } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";

interface RouteGuardProps {
  children: React.ReactNode;
}

export function RouteGuard({ children }: RouteGuardProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAccess = async () => {
      try {
        setLoading(true);
        
        // Rotas públicas que não precisam de verificação
        const publicRoutes = ["/auth", "/welcome", "/api"];
        const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));
        
        if (isPublicRoute) {
          setHasAccess(true);
          return;
        }

        // Verificar acesso à rota atual
        const access = await checkRouteAccess(pathname);
        setHasAccess(access);
      } catch (error) {
        console.error("Error checking route access:", error);
        setHasAccess(false);
      } finally {
        setLoading(false);
      }
    };

    checkAccess();
  }, [pathname]);

  // Mostrar loading enquanto verifica permissões
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Verificando permissões...</p>
        </div>
      </div>
    );
  }

  // Se não tem acesso, mostrar página de acesso negado
  if (hasAccess === false) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <Shield className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900">
              Acesso Negado
            </CardTitle>
            <CardDescription>
              Você não tem permissão para acessar esta área do sistema.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <div className="flex items-center justify-center gap-2 text-amber-600 bg-amber-50 p-3 rounded-lg">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm">
                Entre em contato com o administrador para solicitar acesso.
              </span>
            </div>
            <div className="flex gap-2 justify-center">
              <Button
                variant="outline"
                onClick={() => router.back()}
              >
                Voltar
              </Button>
              <Button
                onClick={() => router.push("/views/control-panel")}
              >
                Ir para o Painel
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Se tem acesso, renderizar o conteúdo
  return <>{children}</>;
}
