"use client";

import {
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from "@/components/ui/form";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn, formatDate } from "@/src/lib/utils";
import { CalendarIcon, Eye, EyeOff } from "lucide-react";
import { InputHTMLAttributes, useEffect, useState } from "react";
import "react-datepicker/dist/react-datepicker.css";
import {
	Controller,
	FieldPath,
	FieldValues,
	useFormContext,
} from "react-hook-form";
import { IMaskInput } from "react-imask";
import { Button } from "./ui/button";
import { Calendar } from "./ui/calendar";
import { Checkbox } from "./ui/checkbox";
import { MultiSelect } from "./ui/multi-select";
import { Popover, PopoverContent, PopoverTrigger } from "./ui/popover";
import { RadioGroup, RadioGroupItem } from "./ui/radio-group";
import { Textarea } from "./ui/textarea";

interface CustomInputProps<TFieldValues extends FieldValues>
	extends Omit<InputHTMLAttributes<HTMLInputElement>, "name"> {
	label?: string;
	type?:
	| "text"
	| "email"
	| "password"
	| "tel"
	| "number"
	| "mask"
	| "date"
	| "select"
	| "textarea"
	| "checkbox-group"
	| "radio-group"
	| "multi-select"
	| "currency"
	| "tag-input";
	mask?:
	| string
	| string[]
	| RegExp
	| RegExp[]
	| { mask: string; maxLength: number }[];
	className?: string;
	labelClassName?: string;
	inputClassName?: string;
	errorClassName?: string;
	placeholder?: string;
	name: FieldPath<TFieldValues>;
	items?: Item[];
	hideErrorMessage?: boolean;
	initialValue?: string;
	suffix?: string;
	prefix?: string;
	defaultValue?: any;
	required?: boolean;
	value?: any;
}

interface Item {
	label: string;
	value: string;
	title?: string; // Para tooltip
}

export function CustomInput<TFieldValues extends FieldValues = FieldValues>({
	label,
	type = "text",
	mask,
	name,
	className,
	labelClassName,
	inputClassName,
	errorClassName,
	placeholder,
	items,
	suffix,
	prefix = "R$",
	defaultValue,
	hideErrorMessage = false,
	required,
	...props
}: CustomInputProps<TFieldValues>) {
	const { control } = useFormContext<TFieldValues>();
	const [showPassword, setShowPassword] = useState(false);
	const [inputType, setInputType] = useState(type);

	useEffect(() => {
		if (type === "password") {
			setInputType(showPassword ? "text" : "password");
		} else {
			setInputType(type);
		}
	}, [type, showPassword]);

	const togglePasswordVisibility = () => {
		setShowPassword(!showPassword);
	};

	const renderInput = (field: any, error: boolean) => {
		const inputValue = field.value ?? "";
		const className = cn(
			"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",
			error && "border border-red-500 text-red-500",
			inputClassName
		);

		switch (type) {
			case "mask":
				return (
					<IMaskInput
						mask={mask}
						{...field}
						{...props}
						value={inputValue}
						onAccept={(value) => field.onChange(value)}
						className={className}
						placeholder={placeholder}
					/>
				);

			case "tel":
				return (
					<IMaskInput
						mask="(00) 90000-0000"
						{...field}
						{...props}
						value={inputValue}
						onAccept={(value) => field.onChange(value)}
						className={className}
						placeholder={placeholder || "(99) 9 9999-9999"}
					/>
				);

			case "select":
				// CRITICAL FIX: Adicionar logs para depuração do componente select
				console.log('CRITICAL FIX: Rendering select component for field:', name);
				console.log('CRITICAL FIX: Items:', items);
				console.log('CRITICAL FIX: Default value:', defaultValue);
				console.log('CRITICAL FIX: Props value:', props.value);

				return (
					<FormField
						name={name}
						render={({ field }) => {
							// CRITICAL FIX: Adicionar logs para depuração do campo
							console.log('CRITICAL FIX: Field value for', name, ':', field.value);

							return (
								<FormItem>
									<Select
										onValueChange={(value) => {
											console.log('CRITICAL FIX: Select value changed to:', value);
											field.onChange(value);
										}}
										defaultValue={defaultValue || field.value}
										value={props.value || field.value}
									>
										<FormControl>
											<SelectTrigger
												className={`${className} ${required && !field.value ? 'border-red-500' : ''}`}
												disabled={props.disabled}
											>
												<SelectValue placeholder={placeholder}>
													{/* CRITICAL FIX: Adicionar log para o valor exibido */}
													{(() => {
														const selectedItem = items?.find(item => item.value === (props.value || field.value));
														console.log('CRITICAL FIX: Selected item for', name, ':', selectedItem);
														return selectedItem?.label || placeholder;
													})()}
												</SelectValue>
											</SelectTrigger>
										</FormControl>

										<SelectContent>
											{items?.map(({ label, value }, index) => (
												<SelectItem value={value} key={index}>
													{label}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									{/* <FormMessage /> */}
								</FormItem>
							);
						}}
					/>
				);

			case "multi-select":
				return (
					<FormField
						name={name}
						render={({ field }) => (
							<FormItem>
								<FormControl>
									<MultiSelect
										options={items || []}
										onValueChange={field.onChange}
										value={field.value}
										defaultValue={defaultValue || field.value}
										placeholder={placeholder}
										variant="primary"
										animation={2}
										maxCount={1}
										modalPopover={true}
									/>
								</FormControl>
							</FormItem>
						)}
					/>
				);

			case "date":
				return (
					<FormField
						name={name}
						render={({ field }) => (
							<FormItem className="flex flex-col">
								<Popover modal={true}>
									<PopoverTrigger asChild>
										<FormControl>
											<Button
												variant="outline"
												className={cn("min-h-10 h-auto", className)}
												disabled={props.disabled}
											>
												{field.value ? (
													formatDate(field.value, "DATE")
												) : (
													<span className="text-muted-foreground">
														{placeholder || "Selecione uma data"}
													</span>
												)}
												<CalendarIcon className="ml-auto h-4 w-4 text-gray-500" />
											</Button>
										</FormControl>
									</PopoverTrigger>
									<PopoverContent
										className="w-auto p-0 max-w-[95vw] md:max-w-[380px]"
										align="start"
										sideOffset={8}
									>
										<Calendar
											mode="single"
											selected={field.value}
											onSelect={field.onChange}
											initialFocus
											disabled={props.disabled}
											className="w-full"
										/>
									</PopoverContent>
								</Popover>
								<FormMessage />
							</FormItem>
						)}
					/>
				);

			case "textarea":
				return (
					<FormField
						name={name}
						render={({ field }) => (
							<FormItem>
								<FormControl>
									<Textarea
										placeholder={placeholder}
										className={className}
										{...field}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				);

			case "checkbox-group":
				return (
					<FormField
						name={name}
						render={() => (
							<FormItem className="mt-2">
								<div className="grid grid-cols-1 md:grid-cols-2 gap-2 border rounded-md p-3 max-h-60 overflow-y-auto">
									{items?.map((item) => (
										<FormField
											key={item.value}
											name={name}
											render={({ field }) => {
												return (
													<FormItem className="flex flex-row items-start space-x-3 space-y-0">
														<FormControl>
															<div className="flex items-center space-x-2">
																<Checkbox
																	id={`${field.name}-${item.value}`}
																	checked={field.value?.includes(item.value)}
																	onCheckedChange={(checked) => {
																		const updatedValue = checked
																			? [...(field.value || []), item.value]
																			: (field.value || []).filter(
																				(value: string) => value !== item.value
																			);
																		field.onChange(updatedValue);
																	}}
																/>
																<Label
																	htmlFor={`${field.name}-${item.value}`}
																	className="font-normal cursor-pointer hover:text-green-500"
																	title={item.title || item.label}
																>
																	{item.label}
																</Label>
															</div>
														</FormControl>
													</FormItem>
												);
											}}
										/>
									))}
								</div>
								<FormMessage />
							</FormItem>
						)}
					/>
				);

			case "radio-group":
				return (
					<FormField
						name={name}
						render={({ field }) => (
							<FormItem className="mt-2">
								<FormControl>
									<RadioGroup
										onValueChange={field.onChange}
										className="flex flex-row gap-3"
									>
										{items?.map((item) => (
											<FormItem
												key={item.value}
												className="flex items-center space-x-2 space-y-0"
											>
												<FormControl>
													<RadioGroupItem
														value={item.value}
														checked={field.value == item.value}
														id={`${field.name}-${item.value}`}
														className="text-green-500 focus:ring-green-500"
													/>
												</FormControl>
												<Label
													htmlFor={`${field.name}-${item.value}`}
													className="font-normal cursor-pointer hover:text-green-500"
												>
													{item.label}
												</Label>
											</FormItem>
										))}
									</RadioGroup>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				);
			case "currency":
				return (
					<div className="relative">
						{prefix && (
							<span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
								{prefix}
							</span>
						)}
						<IMaskInput
							mask={Number}
							scale={2}
							signed={false}
							thousandsSeparator="."
							padFractionalZeros={true}
							normalizeZeros={true}
							radix=","
							mapToRadix={["."]}
							{...field}
							{...props}
							value={inputValue}
							onAccept={(value: number) => field.onChange(value)}
							className={`${className} ${prefix ? 'pl-10' : ''}`}
							placeholder={placeholder}
						/>

					</div>
				);

			case "tag-input":
				return (
					<FormField
						name={name}
						render={({ field }) => {
							const [tags, setTags] = useState<string[]>(field.value || []);

							// Atualiza o estado tags caso o field.value mude (exemplo: ao carregar dados para edição)
							useEffect(() => {
								if (Array.isArray(field.value)) {
									setTags(field.value);
								}
							}, [field.value]);

							const addTag = (event: React.KeyboardEvent<HTMLInputElement>) => {
								if (props.disabled) return;
								if (event.key === "Enter" && event.currentTarget.value.trim()) {
									event.preventDefault();
									const newTag = event.currentTarget.value.trim();
									if (!tags.includes(newTag)) {
										const updatedTags = [...tags, newTag];
										setTags(updatedTags);
										field.onChange(updatedTags);
										event.currentTarget.value = "";
									}
								}
							};

							const removeTag = (tagToRemove: string) => {
								if (props.disabled) return;
								const updatedTags = tags.filter((tag) => tag !== tagToRemove);
								setTags(updatedTags);
								field.onChange(updatedTags);
							};

							return (
								<FormItem>
									<FormControl>
										<div className={`flex flex-wrap gap-2 border rounded-md p-2 ${props.disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}>
											{tags.map((tag) => (
												<span
													key={tag}
													className="flex items-center bg-gray-200 text-gray-800 text-sm px-2 py-1 rounded-md"
												>
													{tag}
													{!props.disabled && (
														<button
															type="button"
															onClick={() => removeTag(tag)}
															className="ml-2 text-red-500 hover:text-red-700"
														>
															&times;
														</button>
													)}
												</span>
											))}
											<input
												type="text"
												className="border-none outline-none flex-1 bg-transparent"
												placeholder={placeholder || "Digite e pressione Enter"}
												onKeyDown={addTag}
												disabled={props.disabled}
											/>
										</div>
									</FormControl>
									<FormMessage />
								</FormItem>
							);
						}}
					/>
				);


			default:
				return (
					<div className="relative">
						<Input
							type={inputType}
							className={`${className} ${suffix ? 'hide-arrows' : ''}`}
							{...field}
							{...props}
							value={inputValue}
							placeholder={placeholder}
						/>
						{
							suffix && (
								<span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
									{suffix}
								</span>
							)
						}
					</div>
				);
		}
	};

	return (
		<Controller
			name={name}
			control={control}
			render={({ field, fieldState: { error } }) => (
				<div className={className}>
					{label && (
						<div className="flex mb-2">
							<Label
								className={cn("font-bold text-gray-700", labelClassName)}
								htmlFor={field.name}
							>
								{label}
							</Label>
						</div>
					)}
					<div className="relative">
						{renderInput(field, !!error)}
						{type === "password" && (
							<button
								type="button"
								onClick={togglePasswordVisibility}
								className="absolute right-3 top-1/2 -translate-y-1/2"
							>
								{showPassword ? (
									<EyeOff className="h-4 w-4 text-gray-500" />
								) : (
									<Eye className="h-4 w-4 text-gray-500" />
								)}
							</button>
						)}
					</div>
					{error && !hideErrorMessage && (
						<small
							className={cn(
								"text-xs font-semibold text-destructive",
								errorClassName
							)}
						>
							{error.message}
						</small>
					)}
				</div>
			)}
		/>
	);
}
