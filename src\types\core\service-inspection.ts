import { Periodicity } from "../common";
import { ResourceControlInterface } from "../utils";

export interface ServiceInspectionParameterInterface {
	name: string;
	inspectionDate: Date;
	periodicity: Periodicity;
	gravity: number;
	urgency: number;
	tendency: number;
	gut: number;
	serviceCost: number;
	totalCost: number;
	financialWeight: number;
	igrf: number;
	inspectionParameterId: string;
}

export type ServiceInspectionParameter = ResourceControlInterface &
	ServiceInspectionParameterInterface;
