import nodemailer from "nodemailer";
import { Attachment } from "nodemailer/lib/mailer";

// Tipos para os emails
export interface EmailOptions {
  to: string | string[];
  subject: string;
  html: string;
  text?: string;
  cc?: string | string[];
  bcc?: string | string[];
  attachments?: Attachment[];
  replyTo?: string;
}

// Configuração do transporte de email
const createTransporter = () => {
  const host = process.env.EMAIL_HOST;
  const port = parseInt(process.env.EMAIL_PORT || "587", 10);
  const user = process.env.EMAIL_USER;
  const pass = process.env.EMAIL_PASS;
  const from = process.env.EMAIL_FROM;

  if (!host || !user || !pass || !from) {
    throw new Error("Configurações de email incompletas no arquivo .env");
  }

  return nodemailer.createTransport({
    host,
    port,
    secure: port === 465, // true para 465, false para outras portas
    auth: {
      user,
      pass,
    },
  });
};

// Função principal para enviar emails
export const sendEmail = async (options: EmailOptions): Promise<boolean> => {
  try {
    const { to, subject, html, text, cc, bcc, attachments, replyTo } = options;

    const transporter = createTransporter();

    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to,
      subject,
      html,
      text: text || html.replace(/<[^>]*>/g, ""), // Remove tags HTML se text não for fornecido
      cc,
      bcc,
      attachments,
      replyTo,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log("Email enviado com sucesso:", info.messageId);
    return true;
  } catch (error: unknown) {
    console.error(
      "Erro ao enviar email:",
      error instanceof Error ? error.message : String(error)
    );
    return false;
  }
};

// Templates de email específicos
export const sendWelcomeEmail = async (
  to: string,
  name: string
): Promise<boolean> => {
  const subject = "Bem-vindo à Ageu Engenharia";
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #333;">Bem-vindo à Ageu Engenharia</h1>
      </div>
      <div style="color: #555; line-height: 1.5;">
        <p>Olá, <strong>${name}</strong>!</p>
        <p>É com grande satisfação que damos as boas-vindas a você em nosso sistema.</p>
        <p>A partir de agora, você terá acesso a todas as funcionalidades da nossa plataforma, podendo acompanhar seus projetos, contratos e muito mais.</p>
        <p>Se tiver qualquer dúvida, não hesite em entrar em contato conosco.</p>
        <p>Atenciosamente,</p>
        <p><strong>Equipe Ageu Engenharia</strong></p>
      </div>
      <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
        <p>Este é um email automático, por favor não responda.</p>
        <p>© ${new Date().getFullYear()} Ageu Engenharia. Todos os direitos reservados.</p>
      </div>
    </div>
  `;

  return sendEmail({ to, subject, html });
};

export const sendContractEmail = async (
  to: string,
  contractName: string,
  contractLink: string,
  customerName: string
): Promise<boolean> => {
  const subject = `Contrato ${contractName} disponível para assinatura`;
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #333;">Contrato Disponível para Assinatura</h1>
      </div>
      <div style="color: #555; line-height: 1.5;">
        <p>Prezado(a) <strong>${customerName}</strong>,</p>
        <p>Informamos que o contrato <strong>${contractName}</strong> está disponível para sua assinatura.</p>
        <p>Para visualizar e assinar o documento, por favor clique no botão abaixo:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${contractLink}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">Visualizar Contrato</a>
        </div>
        <p>Caso o botão não funcione, copie e cole o link abaixo em seu navegador:</p>
        <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px;">${contractLink}</p>
        <p>Se tiver qualquer dúvida, não hesite em entrar em contato conosco.</p>
        <p>Atenciosamente,</p>
        <p><strong>Equipe Ageu Engenharia</strong></p>
      </div>
      <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
        <p>Este é um email automático, por favor não responda.</p>
        <p>© ${new Date().getFullYear()} Ageu Engenharia. Todos os direitos reservados.</p>
      </div>
    </div>
  `;

  return sendEmail({ to, subject, html });
};

export const sendProposalEmail = async (
  to: string,
  proposalName: string,
  proposalLink: string,
  customerName: string,
  attachments?: Attachment[]
): Promise<boolean> => {
  const subject = `Proposta ${proposalName} disponível para análise`;
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #333;">Proposta Disponível para Análise</h1>
      </div>
      <div style="color: #555; line-height: 1.5;">
        <p>Prezado(a) <strong>${customerName}</strong>,</p>
        <p>Temos o prazer de enviar a proposta <strong>${proposalName}</strong> para sua análise.</p>
        <p>Para visualizar a proposta, por favor clique no botão abaixo:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${proposalLink}" style="background-color: #2196F3; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">Visualizar Proposta</a>
        </div>
        <p>Caso o botão não funcione, copie e cole o link abaixo em seu navegador:</p>
        <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px;">${proposalLink}</p>
        <p>Também anexamos uma cópia da proposta a este email para sua conveniência.</p>
        <p>Estamos à disposição para esclarecer quaisquer dúvidas ou fornecer informações adicionais.</p>
        <p>Atenciosamente,</p>
        <p><strong>Equipe Ageu Engenharia</strong></p>
      </div>
      <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
        <p>Este é um email automático, por favor não responda.</p>
        <p>© ${new Date().getFullYear()} Ageu Engenharia. Todos os direitos reservados.</p>
      </div>
    </div>
  `;

  return sendEmail({ to, subject, html, attachments });
};

export const sendNotificationEmail = async (
  to: string,
  title: string,
  message: string,
  actionLink?: string,
  actionText?: string
): Promise<boolean> => {
  const subject = title;
  let html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #333;">${title}</h1>
      </div>
      <div style="color: #555; line-height: 1.5;">
        <p>${message}</p>
  `;

  if (actionLink && actionText) {
    html += `
        <div style="text-align: center; margin: 30px 0;">
          <a href="${actionLink}" style="background-color: #673AB7; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">${actionText}</a>
        </div>
        <p>Caso o botão não funcione, copie e cole o link abaixo em seu navegador:</p>
        <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px;">${actionLink}</p>
    `;
  }

  html += `
        <p>Atenciosamente,</p>
        <p><strong>Equipe Ageu Engenharia</strong></p>
      </div>
      <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
        <p>Este é um email automático, por favor não responda.</p>
        <p>© ${new Date().getFullYear()} Ageu Engenharia. Todos os direitos reservados.</p>
      </div>
    </div>
  `;

  return sendEmail({ to, subject, html });
};

// Função para testar o serviço de email
export const testEmailService = async (to: string): Promise<boolean> => {
  const subject = "Teste do Serviço de Email";
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #333;">Teste do Serviço de Email</h1>
      </div>
      <div style="color: #555; line-height: 1.5;">
        <p>Este é um email de teste para verificar se o serviço de email está funcionando corretamente.</p>
        <p>Se você está recebendo este email, significa que a configuração foi bem-sucedida!</p>
        <p>Atenciosamente,</p>
        <p><strong>Equipe Ageu Engenharia</strong></p>
      </div>
      <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
        <p>Este é um email automático, por favor não responda.</p>
        <p>© ${new Date().getFullYear()} Ageu Engenharia. Todos os direitos reservados.</p>
      </div>
    </div>
  `;

  return sendEmail({ to, subject, html });
};
