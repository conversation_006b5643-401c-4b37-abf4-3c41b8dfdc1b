"use server";
import { prisma } from "@/src/lib/prisma";
import { auth } from "@/src/providers/auth";

export async function loadOrganizations() {
  try {
    const organizations = await prisma.organization.findMany({
      orderBy: {
        name: 'asc'
      }
    });
    
    return organizations;
  } catch (error) {
    console.error("Error loading organizations:", error);
    throw error;
  }
}

export async function getCurrentOrganization() {
  try {
    const session = await auth();
    if (!session?.membership?.organizationId) {
      return null;
    }
    
    const organization = await prisma.organization.findUnique({
      where: {
        id: session.membership.organizationId
      }
    });
    
    return organization;
  } catch (error) {
    console.error("Error getting current organization:", error);
    throw error;
  }
}