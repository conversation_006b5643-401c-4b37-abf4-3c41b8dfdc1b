interface TokenData {
  id: string;
  type: "inspection" | "project";
}

/**
 * Gera um token seguro para acesso público a um relatório
 * @param id ID do relatório (inspeção ou projeto)
 * @param type Tipo do relatório ('inspection' ou 'project')
 * @returns Token codificado
 */
export function generateReportToken(
  id: string,
  type: "inspection" | "project"
): string {
  // Método simplificado para gerar um token
  // Codificar diretamente o ID e o tipo em base64
  const tokenData = { id, type };

  // Adicionar um timestamp para evitar colisões
  const timestamp = Date.now();

  // Criar um payload com os dados e o timestamp
  const payload = JSON.stringify({ data: tokenData, timestamp });

  // Codificar em base64
  return Buffer.from(payload).toString("base64");
}

/**
 * Verifica e decodifica um token de relatório
 * @param token Token codificado
 * @returns Dados do token ou null se inválido
 */
export function verifyReportToken(token: string): TokenData | null {
  try {
    console.log("Verificando token:", token.substring(0, 20) + "...");

    // Decodificar o token
    const decodedString = Buffer.from(token, "base64").toString();
    console.log("Token decodificado:", decodedString.substring(0, 100) + "...");

    const decoded = JSON.parse(decodedString);
    console.log("Token parseado:", decoded);

    // Extrair os dados
    const { data, timestamp } = decoded;
    console.log("Dados extraídos:", { data, timestamp });

    // Verificar se todos os campos necessários estão presentes
    if (!data || !data.id || !data.type) {
      console.error("Campos obrigatórios ausentes:", {
        hasData: !!data,
        hasId: data?.id,
        hasType: data?.type,
      });
      return null;
    }

    return data as TokenData;
  } catch (error) {
    console.error("Erro ao verificar token de relatório:", error);
    return null;
  }
}

/**
 * Gera uma URL pública para visualização do relatório
 * @param id ID do relatório
 * @param type Tipo do relatório
 * @returns URL completa para acesso público
 */
export function generatePublicReportUrl(
  id: string,
  type: "inspection" | "project"
): string {
  const token = generateReportToken(id, type);
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || window.location.origin;
  return `${baseUrl}/public/report/${encodeURIComponent(token)}`;
}
