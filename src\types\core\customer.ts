import { ResourceControlInterface } from "../utils";
import { Contact } from "./contact";
import { Project } from "./project";

export interface CustomerInterface {
  name: string;
  email: string;
  document: string;
  phone: string;
  observation?: string | null;
  cep?: string;
  address?: string;
  city?: string;
  state?: string;
  contacts?: Contact[];
  projects?: Project[];
  documentType: string;
  organizationId: string;
}

export interface CustomerRelationInterface {
  customer: Customer;
  customerId: string;
}

export type Customer = CustomerInterface & ResourceControlInterface;
