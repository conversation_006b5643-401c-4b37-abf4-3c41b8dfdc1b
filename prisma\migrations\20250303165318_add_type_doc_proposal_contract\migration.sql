/*
  Warnings:

  - A unique constraint covering the columns `[proposalId]` on the table `Contract` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `type` to the `ProposalTemplate` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "typeProposalContract" AS ENUM ('CONTRACT', 'PROPOSAL');

-- AlterTable
ALTER TABLE "ProposalTemplate" ADD COLUMN     "type" "typeProposalContract" NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "Contract_proposalId_key" ON "Contract"("proposalId");
