"use server";

import { prisma } from "@/src/lib/prisma";
import { parseObject } from "@/src/lib/utils";

/**
 * Gera um relatório Excel com os dados do histograma
 * @param proposalId ID da proposta
 */
export async function generateHistogramExcel(proposalId: string) {
  try {
    console.log("Iniciando geração de Excel para o histograma, proposalId:", proposalId);

    if (!proposalId) {
      throw new Error("ID da proposta é obrigatório");
    }

    // Buscar todos os dados de produtividade para esta proposta
    console.log("Buscando dados de produtividade para a proposta:", proposalId);

    const productivityData = await prisma.productivity.findMany({
      where: {
        proposalId: proposalId,
      },
      include: {
        service: true,
        periodicity: true,
        repairBudget: {
          include: {
            serviceScope: true,
          },
        },
      },
      orderBy: [
        {
          repairBudgetId: "asc", // Ordenar pelo ID do repairBudget diretamente
        },
        {
          periodicity: {
            order: "asc",
          },
        },
      ],
    });

    console.log(`Encontrados ${productivityData.length} registros de produtividade`);

    // Buscar a proposta para obter informações adicionais
    const proposal = await prisma.proposal.findUnique({
      where: {
        id: proposalId,
      },
      include: {
        customer: true,
        // Incluir mais informações que possam ser úteis
        proposalTemplate: true,
        repairBudgets: true,
      },
    });

    if (!proposal) {
      throw new Error("Proposta não encontrada");
    }

    // Obter o nome correto da proposta
    let proposalTitle = proposal.name || "Proposta sem título";

    // Se a proposta tiver um número, incluí-lo no título (verificar se a propriedade existe)
    if ('number' in proposal && proposal.number) {
      proposalTitle = `Proposta ${proposal.number} - ${proposalTitle}`;
    }

    // Tentar obter informações adicionais do template da proposta
    if (proposal.proposalTemplate && proposal.proposalTemplate.title) {
      console.log("Template da proposta:", proposal.proposalTemplate.title);
    }

    // Obter o nome do cliente
    const customerName = proposal.customer?.name || "Cliente não especificado";

    console.log("Informações da proposta:", {
      id: proposal.id,
      title: proposalTitle,
      customerName: customerName,
      repairBudgetsCount: proposal.repairBudgets?.length || 0
    });

    // Verificar os campos disponíveis na proposta para depuração
    console.log("Campos disponíveis na proposta:", Object.keys(proposal));

    // Extrair todos os períodos únicos
    const uniquePeriods = new Map();
    productivityData.forEach((item) => {
      if (item.periodicity && !uniquePeriods.has(item.periodicity.id)) {
        // Verificar se temos os dados necessários para o período
        console.log("Dados do período:", {
          id: item.periodicity.id,
          label: item.periodicity.label,
          content: item.periodicity.content,
          order: item.periodicity.order
        });

        // Formatar o período no padrão "Mês / Ano"
        let periodLabel = "Período sem nome";

        try {
          // Tentar extrair mês e ano do content ou de outras propriedades
          let month, year;

          // Verificar se temos content no formato "month=X;year=Y"
          if (item.periodicity.content) {
            const contentMatch = item.periodicity.content.match(/month=(\d+);year=(\d+)/);
            if (contentMatch) {
              month = parseInt(contentMatch[1]);
              year = parseInt(contentMatch[2]);
            }
          }

          // Se não conseguimos extrair do content, tentar outras propriedades
          if (!month || !year) {
            // Não tentar extrair de outras propriedades, pois o modelo não tem month e year
            // Usar apenas o content para extrair mês e ano
          }

          // Se temos mês e ano, formatar corretamente
          if (month && year) {
            const monthNames = [
              "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho",
              "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"
            ];

            // Ajustar o índice do mês (se baseado em 0)
            const monthIndex = month > 0 && month <= 12 ? month - 1 : 0;

            periodLabel = `${monthNames[monthIndex]} / ${year}`;
          } else if (item.periodicity.content) {
            // Se não conseguimos extrair mês e ano, usar o content como está
            periodLabel = item.periodicity.content;
          } else if (item.periodicity.label) {
            // Ou usar o label se disponível
            periodLabel = item.periodicity.label;
          }
        } catch (error) {
          console.error("Erro ao formatar período:", error);
          // Em caso de erro, usar o content ou label como fallback
          periodLabel = item.periodicity.content || item.periodicity.label || "Período sem nome";
        }

        // Extrair mês e ano para ordenação
        let month = 0;
        let year = 0;

        // Tentar extrair mês e ano para ordenação
        if (item.periodicity.content) {
          const contentMatch = item.periodicity.content.match(/month=(\d+);year=(\d+)/);
          if (contentMatch) {
            month = parseInt(contentMatch[1]);
            year = parseInt(contentMatch[2]);
          }
        }

        // Se não conseguimos extrair do content, não temos outras propriedades para tentar
        // O modelo não tem month e year diretamente
        if (!month || !year) {
          // Usar valores padrão ou tentar extrair de outras fontes se necessário
          // Por exemplo, tentar extrair do label ou order
        }

        // Calcular uma ordem baseada no ano e mês para garantir ordenação cronológica
        // Ano * 100 + mês garante que janeiro/2024 (202401) vem antes de fevereiro/2024 (202402)
        const chronologicalOrder = year * 100 + month;

        uniquePeriods.set(item.periodicity.id, {
          id: item.periodicity.id,
          label: periodLabel,
          order: item.periodicity.order || 0,
          chronologicalOrder: chronologicalOrder || 0,
          month: month || 0,
          year: year || 0
        });
      }
    });

    // Converter para array e ordenar cronologicamente (do mais antigo para o mais recente)
    const periods = Array.from(uniquePeriods.values()).sort((a, b) => a.chronologicalOrder - b.chronologicalOrder);

    // Verificar os períodos após ordenação
    console.log("Períodos ordenados cronologicamente:", periods.map(p => ({
      id: p.id,
      label: p.label,
      chronologicalOrder: p.chronologicalOrder,
      month: p.month,
      year: p.year
    })));

    // Extrair todas as atividades únicas
    const uniqueActivities = new Map();
    productivityData.forEach((item) => {
      if (item.repairBudget && !uniqueActivities.has(item.repairBudget.id)) {
        // Tentar obter um nome para a atividade a partir de várias fontes possíveis
        let activityName = "Atividade sem nome";

        if (item.repairBudget.description) {
          activityName = item.repairBudget.description;
        } else if (item.service && item.service.name) {
          activityName = item.service.name;
        } else if (item.repairBudget.serviceScope && item.repairBudget.serviceScope.name) {
          activityName = item.repairBudget.serviceScope.name;
        }

        uniqueActivities.set(item.repairBudget.id, {
          id: item.repairBudget.id,
          name: activityName,
          serviceScopeName: item.repairBudget.serviceScope?.name || "",
          buildingPercentage: item.repairBudget.buildingPercentage || 0,
        });
      }
    });

    // Converter para array
    const activities = Array.from(uniqueActivities.values());

    // Organizar os dados para o Excel
    const formattedData = {
      proposal: {
        id: proposal.id,
        name: proposalTitle, // Usar o nome formatado com número
        customerName: customerName, // Usar o nome do cliente obtido anteriormente
      },
      periods,
      activities,
      // Incluir os dados completos de repairBudget para acesso ao buildingPercentage
      repairBudgets: productivityData
        .filter(item => item.repairBudget)
        .map(item => ({
          id: item.repairBudgetId,
          buildingPercentage: item.repairBudget.buildingPercentage || 0,
        }))
        .filter((v, i, a) => a.findIndex(t => t.id === v.id) === i), // Remover duplicados
      measurements: productivityData.map((item) => ({
        activityId: item.repairBudgetId,
        periodId: item.periodicityId,
        predictedPercentage: Number(item.predictedPeriodPercentage) || 0,
        realPercentage: Number(item.realPeriodPercentage) || 0,
      })),
    };

    console.log("Dados formatados com sucesso:", {
      periodos: periods.length,
      atividades: activities.length,
      medicoes: formattedData.measurements.length
    });

    return parseObject(formattedData);
  } catch (error) {
    console.error("Erro ao gerar dados para o Excel:", error);

    // Fornecer uma mensagem de erro mais detalhada
    if (error instanceof Error) {
      console.error("Detalhes do erro:", error.message);
      console.error("Stack trace:", error.stack);
    }

    // Retornar um objeto de erro formatado
    throw new Error(`Falha ao gerar relatório Excel: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
  }
}
