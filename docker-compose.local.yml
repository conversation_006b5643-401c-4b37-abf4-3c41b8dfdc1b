version: "3.9"

services:
  postgres:
    image: postgres:14
    container_name: postgres_ageu
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - "${POSTGRES_PORT}:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=ageu
    networks:
      - ageu-network

  minio:
    image: minio/minio:latest
    container_name: minio_ageu
    command: server /data --console-address ":9001"
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    environment:
      - MINIO_ROOT_USER=${MINIO_ROOT_USER}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD}
    networks:
      - ageu-network

volumes:
  pgdata:
    driver: local
  minio_data:
    driver: local

networks:
  ageu-network:
    driver: bridge
