"use server";

import { prisma } from "@/src/lib/prisma";
import { parseObject } from "@/src/lib/utils";
import { ControlPanelData } from "@/src/types/core/control-panel";
import { auth } from "@/src/providers/auth";

/**
 * Calcula a taxa de conversão do mês anterior
 * @param organizationId ID da organização
 * @returns Objeto com total de propostas e propostas aceitas do mês anterior
 */
// async function calculatePreviousMonthConversionRate(organizationId: string) {
//   try {
//     // Obter o primeiro dia do mês atual e do mês anterior
//     const today = new Date();
//     const startOfCurrentMonth = new Date(
//       today.getFullYear(),
//       today.getMonth(),
//       1
//     );
//     const startOfPreviousMonth = new Date(
//       today.getFullYear(),
//       today.getMonth() - 1,
//       1
//     );

//     // Filtro base para o mês anterior
//     const baseFilter = {
//       customer: {
//         organizationId,
//       },
//       createdAt: {
//         lt: startOfCurrentMonth,
//         gte: startOfPreviousMonth,
//       },
//     };

//     // Contar total de propostas do mês anterior
//     const totalProposals = await prisma.proposal.count({
//       where: baseFilter,
//     });

//     // Contar propostas aceitas, assinadas, em andamento e concluídas do mês anterior
//     const acceptedProposals = await prisma.proposal.count({
//       where: {
//         ...baseFilter,
//         situation: {
//           in: [
//             "PROPOSAL_ACCEPTED",
//             "SIGNED",
//             "PROJECT_IN_PROGRESS",
//             "PROJECT_FINISHED",
//           ],
//         },
//       },
//     });

//     return {
//       totalProposals,
//       acceptedProposals,
//     };
//   } catch (error) {
//     console.error("Erro ao calcular taxa de conversão do mês anterior:", error);
//     return {
//       totalProposals: 0,
//       acceptedProposals: 0,
//     };
//   }
// }

/**
 * Calcula a tendência (% de crescimento ou queda) comparando o valor atual com o valor do mês anterior
 * @param model Nome do modelo no Prisma (customer, proposal, etc.)
 * @param filter Filtros adicionais para a consulta
 * @param currentCount Contagem atual dos registros
 * @returns Percentual de crescimento/queda
 */
async function calculateTrend(
  model: string,
  filter: any = {},
  currentCount: number
): Promise<number> {
  try {
    // Obter o primeiro dia do mês atual e do mês anterior
    const today = new Date();
    const startOfCurrentMonth = new Date(
      today.getFullYear(),
      today.getMonth(),
      1
    );
    const startOfPreviousMonth = new Date(
      today.getFullYear(),
      today.getMonth() - 1,
      1
    );

    // Adicionar filtro de organização
    const session = await auth();
    const organizationId = session?.membership?.organizationId;

    if (!organizationId) {
      return 0;
    }

    // Construir o filtro completo
    const completeFilter: any = {
      ...filter,
      createdAt: {
        lt: startOfCurrentMonth,
        gte: startOfPreviousMonth,
      },
    };

    // Adicionar filtro de organização dependendo do modelo
    if (model === "customer") {
      completeFilter.organizationId = organizationId;
    } else if (model === "proposal") {
      completeFilter.customer = {
        organizationId,
      };
    }

    // Contar registros do mês anterior
    const previousCount = await prisma[model].count({
      where: completeFilter,
    });

    // Calcular a tendência
    if (previousCount > 0) {
      return Number(
        (((currentCount - previousCount) / previousCount) * 100).toFixed(1)
      );
    }

    return 0;
  } catch (error) {
    console.error("Erro ao calcular tendência:", error);
    return 0;
  }
}

export async function loadControlPanelData() {
  try {
    const session = await auth();
    const organizationId = session?.membership?.organizationId;

    if (!organizationId) {
      throw new Error("No organization found for current user");
    }

    const customerCount = await prisma.customer.count({
      where: { organizationId },
    });

    const totalProposalsCount = await prisma.proposal.count({
      where: {
        customer: {
          organizationId,
        },
      },
    });

    const lostProposalsCount = await prisma.proposal.count({
      where: {
        situation: "LOST",
        customer: {
          organizationId,
        },
      },
    });

    const underAnalysisProposalsCount = await prisma.proposal.count({
      where: {
        situation: "UNDER_ANALYSIS",
        customer: {
          organizationId,
        },
      },
    });

    const acceptedProposalsCount = await prisma.proposal.count({
      where: {
        situation: "PROPOSAL_ACCEPTED",
        customer: {
          organizationId,
        },
      },
    });

    const signedProposalsCount = await prisma.proposal.count({
      where: {
        situation: "SIGNED",
        customer: {
          organizationId,
        },
      },
    });

    const inProgressProjectsCount = await prisma.proposal.count({
      where: {
        situation: "PROJECT_IN_PROGRESS",
        customer: {
          organizationId,
        },
      },
    });

    // Ano atual
    const currentYear = new Date().getFullYear();
    const startOfYear = new Date(currentYear, 0, 1);
    const startOfNextYear = new Date(currentYear + 1, 0, 1);

    // Projetos concluídos apenas do ano atual, considerando o log mais recente de conclusão
    // Busca todos os logs PROJECT_FINISHED do ano atual
    const finishedLogs = await prisma.logProposal.findMany({
      where: {
        newStatus: "PROJECT_FINISHED",
        createdAt: {
          gte: startOfYear,
          lt: startOfNextYear,
        },
        proposal: {
          customer: {
            organizationId,
          },
        },
      },
      orderBy: [
        { proposalId: "asc" },
        { createdAt: "desc" },
      ],
      select: {
        proposalId: true,
        createdAt: true,
      },
    });

    // Mapear para pegar apenas o log mais recente de cada proposta
    const latestLogByProposal = new Map();
    for (const log of finishedLogs) {
      if (!latestLogByProposal.has(log.proposalId)) {
        latestLogByProposal.set(log.proposalId, log.createdAt);
      }
    }
    const completedProjectsCount = latestLogByProposal.size;

    const proposalSentCount = await prisma.proposal.count({
      where: {
        situation: "PROPOSAL_SENT",
        customer: {
          organizationId,
        },
      },
    });

    // Buscar dados de propostas por tipo de contrato (serviceType)
    // Definir os tipos de contrato que queremos contar
    const contractTypes = [
      { id: "INSPECAO", name: "Inspeção" },
      { id: "FISCALIZACAO", name: "Fiscalização" },
      { id: "GERENCIAMENTO", name: "Gerenciamento" },
      { id: "CONSULTANCY", name: "Consultoria" },
      { id: "PROJECT", name: "Projeto" },
    ];

    // Calcular a data de 6 meses atrás
    const today = new Date();
    const sixMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 5, 1); // Pega o primeiro dia do mês de 6 meses atrás

    // Contar propostas por tipo de contrato APENAS dos últimos 6 meses
    const serviceTypeCounts = await Promise.all(
      contractTypes.map(async (type) => {
        const count = await prisma.proposal.count({
          where: {
            serviceType: type.id,
            situation: { in: ["SIGNED", "PROJECT_IN_PROGRESS", "PROJECT_FINISHED", "PROPOSAL_ACCEPTED"] },
            customer: {
              organizationId,
            },
            createdAt: {
              gte: sixMonthsAgo,
            },
          },
        });
        return {
          name: type.name,
          count,
        };
      })
    );

    // Ordenar por contagem (do maior para o menor)
    const topServices = serviceTypeCounts.sort((a, b) => b.count - a.count);

    // Calcular a tendência para clientes ativos usando a função reutilizável
    const customerTrend = await calculateTrend("customer", {}, customerCount);

    // Calcular a taxa de conversão do mês anterior (nova lógica igual ao KPI)
    // Obter o primeiro e último dia do mês anterior
    const startOfCurrentMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const startOfPreviousMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    // const endOfPreviousMonth = new Date(today.getFullYear(), today.getMonth(), 0, 23, 59, 59, 999);

    // Propostas ganhas do mês anterior
    const wonProposalsCount = await prisma.proposal.count({
      where: {
        customer: { organizationId },
        situation: { in: [
          "PROPOSAL_ACCEPTED",
          "SIGNED",
          "PROJECT_IN_PROGRESS",
          "PROJECT_FINISHED"
        ] },
        startDate: {
          gte: startOfPreviousMonth,
          lt: startOfCurrentMonth,
        },
      },
    });

    // Propostas perdidas do mês anterior
    const lostProposalsCountMonth = await prisma.proposal.count({
      where: {
        customer: { organizationId },
        situation: "LOST",
        startDate: {
          gte: startOfPreviousMonth,
          lt: startOfCurrentMonth,
        },
      },
    });

    // Novo denominador: só propostas ganhas + perdidas
    const denominator = wonProposalsCount + lostProposalsCountMonth;
    const conversionRate = denominator > 0
      ? Number(((wonProposalsCount / denominator) * 100).toFixed(2))
      : 0;

    // Calcular taxa de conversão do mês ANTERIOR para tendência
    const previousWonProposalsCount = await prisma.proposal.count({
      where: {
        customer: { organizationId },
        situation: { in: [
          "PROPOSAL_ACCEPTED",
          "SIGNED",
          "PROJECT_IN_PROGRESS",
          "PROJECT_FINISHED"
        ] },
        startDate: {
          gte: new Date(today.getFullYear(), today.getMonth() - 2, 1),
          lt: startOfPreviousMonth,
        },
      },
    });
    const previousLostProposalsCount = await prisma.proposal.count({
      where: {
        customer: { organizationId },
        situation: "LOST",
        startDate: {
          gte: new Date(today.getFullYear(), today.getMonth() - 2, 1),
          lt: startOfPreviousMonth,
        },
      },
    });
    const previousDenominator = previousWonProposalsCount + previousLostProposalsCount;
    const previousConversionRate = previousDenominator > 0
      ? Number(((previousWonProposalsCount / previousDenominator) * 100).toFixed(2))
      : 0;

    // Corrigir tendência para nunca ser NaN ou infinita
    const conversionRateTrend = previousConversionRate > 0
      ? Number((((conversionRate - previousConversionRate) / previousConversionRate) * 100).toFixed(1))
      : 0;

    const data = [
      {
        title: "Clientes ativos",
        count: customerCount,
        trend: customerTrend,
      },
      {
        title: "Total de propostas",
        count: totalProposalsCount,
        trend: await calculateTrend("proposal", {}, totalProposalsCount),
      },
      {
        title: "Taxa de conversão",
        count: `${conversionRate}%`,
        trend: conversionRateTrend,
      },
      {
        title: "Propostas perdidas",
        count: lostProposalsCount,
        trend: await calculateTrend(
          "proposal",
          { situation: "LOST" },
          lostProposalsCount
        ),
      },
      {
        title: "Propostas enviadas",
        count: proposalSentCount,
        trend: await calculateTrend(
          "proposal",
          { situation: "PROPOSAL_SENT" },
          proposalSentCount
        ),
      },
      {
        title: "Propostas em analise",
        count: underAnalysisProposalsCount,
        trend: await calculateTrend(
          "proposal",
          { situation: "UNDER_ANALYSIS" },
          underAnalysisProposalsCount
        ),
      },
      {
        title: "Propostas aceitas",
        count: acceptedProposalsCount,
        trend: await calculateTrend(
          "proposal",
          { situation: "PROPOSAL_ACCEPTED" },
          acceptedProposalsCount
        ),
      },
      {
        title: "Propostas assinadas",
        count: signedProposalsCount,
        trend: await calculateTrend(
          "proposal",
          { situation: "SIGNED" },
          signedProposalsCount
        ),
      },
      {
        title: "Projetos em andamento",
        count: inProgressProjectsCount,
        trend: await calculateTrend(
          "proposal",
          { situation: "PROJECT_IN_PROGRESS" },
          inProgressProjectsCount
        ),
      },
      {
        title: "Projetos concluidos",
        count: completedProjectsCount,
        trend: await calculateTrend(
          "proposal",
          { situation: "PROJECT_FINISHED" },
          completedProjectsCount
        ),
        year: currentYear,
      },
      // Dados dos tipos de contrato
      ...topServices.map((service) => ({
        title: service.name,
        count: service.count,
        type: "CONTRACT_TYPE", // Adicionar um identificador para diferenciar estes dados
      })),
    ];

    return parseObject(data) as ControlPanelData[];
  } catch (error) {
    console.error(error);
  }
}
