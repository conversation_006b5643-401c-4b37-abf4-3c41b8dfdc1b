"use client";
import { loadIMP, saveRepairBudget, loadRepairBudgets } from "@/src/actions/repair-budget";
import { updateProposalWorkTotalCost } from "@/src/actions/update-proposal-work-total-cost";
import { CustomInput } from "@/src/components/app-input";
import { CurrencyInput } from "@/src/components/currency-input";
import { Button } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import { toast } from "@/src/hooks/use-toast";
import { Proposal } from "@/src/types/core/proposal";
import { RepairBudget } from "@/src/types/core/repair-budget";
import { ServicesScope } from "@/src/types/core/services-scope";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { FormProvider, useForm, Controller } from "react-hook-form";
import {
    repairBudgetSchema,
    RepairBudgetSchema,
} from "../schemas/repair-budget.schema";


import { parseCurrencyToNumber, formatCurrency } from "@/src/lib/utils";

interface RepairBudgetFormProps {
    proposal: Proposal;
    onCancelClick: (formChanged?: boolean) => void;
    repairBudget?: RepairBudget;
    services: ServicesScope[];
    onSave?: () => void; // Nova prop opcional
}

export default function RepairBudgetForm({
    proposal,
    onCancelClick,
    repairBudget,
    services,
    onSave
}: RepairBudgetFormProps) {
    const [loading, setLoading] = useState(false);
    const [formChanged, setFormChanged] = useState(false);
    const [initialLoading, setInitialLoading] = useState(true);
    const [existingBudgets, setExistingBudgets] = useState<RepairBudget[]>([]);
    const [remainingBudget, setRemainingBudget] = useState<number>(0);
    const [totalUsedBudget, setTotalUsedBudget] = useState<number>(0);

    const defaultValues = async () => {
        // Validação para determinar o valor de totalCost
        // Se workTotalCost e proposal.budget forem iguais ou se workTotalCost for undefined,
        // usar o valor de proposal.budget, caso contrário usar o valor de workTotalCost
        
        const totalCostValue =
            (proposal.workTotalCost === proposal.budget || proposal.workTotalCost === undefined)
                ? proposal.budget
                : proposal.workTotalCost || 0.0;

        const defaultValues: any = {
            id: "",
            measurementDate: new Date(),
            // gravity: "1",
            // urgency: "1",
            // tendency: "1",
            gut: "1",
            // serviceCost: "1",
            totalCost: totalCostValue.toString(),
            financialWeight: "1",
            igrf: "0",
            serviceScopeId: "",
            proposalId: proposal.id,
            startDate: new Date(),
            endDate: new Date(),
            equipmentAmount: "",
            equipmentDescription: "",
            laborAmount: "",
            imp: "",
        };

        if (repairBudget) {
            defaultValues.id = repairBudget.id;
            defaultValues.equipmentAmount = repairBudget?.equipmentAmount?.toString();
            defaultValues.equipmentDescription =
                repairBudget?.equipmentDescription || "";
            defaultValues.laborAmount = (repairBudget?.laborAmount ?? 0).toString();

            defaultValues.gravity = repairBudget.gravity.toString();
            defaultValues.urgency = repairBudget.urgency.toString();
            defaultValues.tendency = repairBudget.tendency.toString();
            defaultValues.gut = repairBudget.gut.toString();
            defaultValues.serviceCost = formatCurrency(repairBudget?.serviceCost);
            defaultValues.financialWeight = Number(
                repairBudget.financialWeight
            ).toFixed(2);
            defaultValues.igrf = repairBudget.igrf.toString();
            defaultValues.serviceScopeId = repairBudget.serviceScopeId;
            if (repairBudget.startDate) {
                defaultValues.startDate = new Date(repairBudget.startDate);
            }
            if (repairBudget.endDate) {
                defaultValues.endDate = new Date(repairBudget.endDate);
            }
        }

        const imp = await loadIMP(defaultValues.serviceScopeId);

        defaultValues.imp = imp;

        return defaultValues;
    };

    // Validação para determinar o valor de totalCost
    // Se workTotalCost e proposal.budget forem iguais ou se workTotalCost for undefined,
    // usar o valor de proposal.budget, caso contrário usar o valor de workTotalCost
    const totalCostValue =
        (proposal.workTotalCost === proposal.budget || proposal.workTotalCost === undefined)
            ? proposal.budget
            : proposal.workTotalCost || 0.0;

    const methods = useForm<RepairBudgetSchema>({
        mode: "onTouched", // Changed from "onChange" to "onTouched" to prevent validation on initial load
        resolver: zodResolver(repairBudgetSchema),
        defaultValues: {
            id: "",
            measurementDate: new Date(),
            // gravity: "1",
            // urgency: "1",
            // tendency: "1",
            gut: "0",
            // serviceCost: "1",
            totalCost: totalCostValue.toString(),
            financialWeight: "0",
            igrf: "0",
            serviceScopeId: "",
            proposalId: proposal.id,
            startDate: new Date(),
            endDate: new Date(),
            equipmentAmount: "",
            equipmentDescription: "",
            laborAmount: "",
            imp: "",
        },
    });

    // Função para carregar os orçamentos existentes e calcular o valor restante
    const loadExistingBudgets = async () => {
        try {
            // Carregar todos os orçamentos de reparo para esta proposta
            const budgets = await loadRepairBudgets(proposal.id);

            if (budgets && budgets.length > 0) {
                console.log("Orçamentos carregados:", budgets);

                // Filtrar para excluir o orçamento atual se estiver editando
                const otherBudgets = repairBudget
                    ? budgets.filter(budget => budget.id !== repairBudget.id)
                    : budgets;

                console.log("Orçamentos filtrados (excluindo o atual):", otherBudgets);
                setExistingBudgets(otherBudgets);

                // Calcular o total já utilizado em outros serviços
                const totalUsed = otherBudgets.reduce(
                    (sum, budget) => {
                        const serviceCost = Number(budget.serviceCost);
                        console.log(`Orçamento ${budget.id}: ${serviceCost}`);
                        return sum + serviceCost;
                    },
                    0
                );

                console.log("Total utilizado em outros serviços:", totalUsed);
                setTotalUsedBudget(totalUsed);

                // Aplicar a validação: se workTotalCost e proposal.budget forem iguais ou se workTotalCost for undefined,
                // usar o valor de proposal.budget, caso contrário usar o valor de workTotalCost
                const totalCostValue = parseCurrencyToNumber(
                    ((proposal.workTotalCost === proposal.budget || proposal.workTotalCost === undefined)
                        ? proposal.budget
                        : proposal.workTotalCost)?.toString() || "0"
                );

                // Atualizar o valor do totalCost no formulário apenas se for a primeira vez
                // ou se o valor atual for zero ou vazio
                const currentTotalCost = parseCurrencyToNumber(methods.getValues().totalCost?.toString() || "0");
                if (currentTotalCost === 0 || !formChanged) {
                    methods.setValue("totalCost", formatCurrency(totalCostValue), {
                        shouldValidate: false, // Changed from true to false to prevent validation on initial load
                        shouldDirty: false,
                        shouldTouch: false
                    });
                }

                // Calcular o valor restante disponível
                const remaining = totalCostValue - totalUsed;
                console.log("Valor restante disponível:", remaining);
                setRemainingBudget(remaining);
            } else {
                console.log("Nenhum orçamento encontrado ou lista vazia");
                setExistingBudgets([]);
                setTotalUsedBudget(0);

                // Definir o valor restante como o custo total da obra
                // Aplicar a validação: se workTotalCost e proposal.budget forem iguais ou se workTotalCost for undefined,
                // usar o valor de proposal.budget, caso contrário usar o valor de workTotalCost
                const totalCostValue = parseCurrencyToNumber(
                    ((proposal.workTotalCost === proposal.budget || proposal.workTotalCost === undefined)
                        ? proposal.budget
                        : proposal.workTotalCost)?.toString() || "0"
                );

                // Atualizar o valor do totalCost no formulário apenas se for a primeira vez
                // ou se o valor atual for zero ou vazio
                const currentTotalCost = parseCurrencyToNumber(methods.getValues().totalCost?.toString() || "0");
                if (currentTotalCost === 0 || !formChanged) {
                    methods.setValue("totalCost", formatCurrency(totalCostValue), {
                        shouldValidate: false, // Changed from true to false to prevent validation on initial load
                        shouldDirty: false,
                        shouldTouch: false
                    });
                }

                console.log("Custo total da obra (sem orçamentos):", totalCostValue);
                setRemainingBudget(totalCostValue);
            }
        } catch (error) {
            console.error("Erro ao carregar orçamentos existentes:", error);
            // Em caso de erro, definir valores padrão
            setExistingBudgets([]);
            setTotalUsedBudget(0);

            // Aplicar a validação: se workTotalCost e proposal.budget forem iguais ou se workTotalCost for undefined,
            // usar o valor de proposal.budget, caso contrário usar o valor de workTotalCost
            const totalCostValue = parseCurrencyToNumber(
                ((proposal.workTotalCost === proposal.budget || proposal.workTotalCost === undefined)
                    ? proposal.budget
                    : proposal.workTotalCost)?.toString() || "0"
            );

            // Atualizar o valor do totalCost no formulário apenas se for a primeira vez
            // ou se o valor atual for zero ou vazio
            const currentTotalCost = parseCurrencyToNumber(methods.getValues().totalCost?.toString() || "0");
            if (currentTotalCost === 0 || !formChanged) {
                methods.setValue("totalCost", formatCurrency(totalCostValue), {
                    shouldValidate: false, // Changed from true to false to prevent validation on initial load
                    shouldDirty: false,
                    shouldTouch: false
                });
            }

            setRemainingBudget(totalCostValue);
        }
    };

    useEffect(() => {
        async function loadValues() {
            setInitialLoading(true);
            try {
                const values = await defaultValues();
                methods.reset(values, {
                    keepErrors: false,
                    keepDirty: false,
                    keepTouched: false,
                    keepIsSubmitted: false,
                    keepIsValid: false
                });
                setFormChanged(false);

                // Carregar orçamentos existentes após carregar os valores padrão
                await loadExistingBudgets();
            } catch (error) {
                console.error("Erro ao carregar valores iniciais:", error);
            } finally {
                setInitialLoading(false);
            }
        }

        loadValues();
    }, [repairBudget]);

    const { watch, setValue, formState } = methods;

    // Monitorar mudanças no formulário
    useEffect(() => {
        if (formState.isDirty) {
            setFormChanged(true);
        }
    }, [formState.isDirty]);

    // Recarregar orçamentos existentes quando o componente for montado
    useEffect(() => {
        // Removemos a verificação de initialLoading para garantir que os orçamentos sejam carregados
        // mesmo quando o componente está inicializando
        if (!formChanged) {
            // Só recarregar os orçamentos se o formulário não tiver sido alterado
            // para evitar sobrescrever valores que o usuário digitou
            loadExistingBudgets();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [proposal.id, formChanged]);

    const [
        gravity,
        urgency,
        tendency,
        serviceCost,
        totalCost,
        gut,
        financialWeight,
    ] = watch([
        "gravity",
        "urgency",
        "tendency",
        "serviceCost",
        "totalCost",
        "gut",
        "financialWeight",
    ]);



    const calculateGUT = () => {
        const gutValue =
            (Number(gravity) || 0) * (Number(urgency) || 0) * (Number(tendency) || 0);
        setValue("gut", gutValue);
    };

    const calculateFinancialWeight = () => {
        if (Number(totalCost) > 0) {
            const financialWeightValue = parseCurrencyToNumber(serviceCost) / parseCurrencyToNumber(totalCost)

            // Atualizar o valor do financialWeight no formulário
            methods.setValue("financialWeight", financialWeightValue.toFixed(4).toString(), {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true
            });

            console.log("Peso financeiro calculado:", financialWeightValue.toFixed(4));
        }
    };

    const calculateIGRF = () => {
        const igrfValue = Number(gut) * Number(financialWeight);
        // setValue("igrf", igrfValue.toFixed(2) || "0");
        methods.setValue("igrf", igrfValue.toFixed(2) || "0", {
            shouldValidate: true,
            shouldDirty: true,
            shouldTouch: true
        });
    };

    const handleSubmit = async () => {
        setLoading(true);
        try {
            // Validar o formulário antes de prosseguir
            const isValid = await methods.trigger();
            if (!isValid) {
                toast({
                    title: "Erro",
                    description: "Por favor, corrija os erros no formulário antes de salvar",
                    variant: "destructive"
                });
                setLoading(false);
                return;
            }

            const formValues = methods.getValues();

            // Verificar se o totalCost é maior que zero
            const totalCostValue = parseCurrencyToNumber(formValues.totalCost.toString());
            if (totalCostValue <= 0) {
                toast({
                    title: "Erro",
                    description: "Custo total deve ser maior que 0",
                    variant: "destructive"
                });
                setLoading(false);
                return;
            }

            // Verificar se o Custo Total da Obra não é menor que o Custo do serviço
            const serviceCostValue = parseCurrencyToNumber(formValues.serviceCost.toString());
            if (totalCostValue < serviceCostValue) {
                toast({
                    title: "Erro",
                    description: "O valor do Custo do serviço é maior que o Custo Total da Obra, corrija para salvar.",
                    variant: "destructive"
                });
                setLoading(false);
                return;
            }

            
            if (repairBudget) {
                console.log("- Valor original do serviço (edição):", Number(repairBudget.serviceCost));
            }

            // Verificar se o custo do serviço não ultrapassa o valor restante disponível
            console.log("Validação final antes de salvar:");
            console.log("- Orçamentos existentes:", existingBudgets.length);
            console.log("- Total já utilizado:", totalUsedBudget);
            console.log("- Valor restante:", remainingBudget);

            // Valor disponível para este serviço
            let availableForThisService = remainingBudget;

            if (repairBudget) {
                const originalServiceCost = Number(repairBudget.serviceCost);
                console.log("- Valor original do serviço (edição):", originalServiceCost);
                // Ao editar, o valor original já está incluído no totalUsedBudget,
                // então precisamos adicioná-lo de volta ao valor disponível
                availableForThisService += originalServiceCost;
                console.log("- Valor disponível para este serviço (edição):", availableForThisService);
            }

            if (serviceCostValue > availableForThisService) {
                // Mostrar aviso quando o custo do serviço for maior que o valor disponível
                console.log("VALIDAÇÃO FALHOU: Serviço ultrapassa valor disponível");
                toast({
                    title: "Erro",
                    description: `O valor do Custo do serviço (${formatCurrency(serviceCostValue)}) ultrapassa o valor disponível (${formatCurrency(availableForThisService)}). Aumente o Custo Total da Obra ou reduza o Custo do serviço.`,
                    variant: "destructive"
                });
                setLoading(false);
                return;
            }

            console.log("VALIDAÇÃO PASSOU: Orçamento dentro do limite disponível");

            // Verificar se o formulário é válido usando o schema
            const formValid = repairBudgetSchema.safeParse(formValues).success;

            if (formValid) {
                // Atualizar o workTotalCost da proposta antes de salvar o orçamento
                const totalCostValue = formValues.totalCost;
                console.log("Valor de totalCost a ser salvo:", totalCostValue);

                // Sempre atualizar o workTotalCost, mesmo que o valor não tenha mudado
                if (totalCostValue) {
                    try {
                        // Aplicar a validação: se workTotalCost e proposal.budget forem iguais ou se workTotalCost for undefined,
                        // usar o valor de proposal.budget, caso contrário usar o valor de workTotalCost
                        // Ao salvar, atualizar tanto totalCost quanto workTotalCost, mas não atualizar proposal.budget

                        // Determinar o valor a ser usado para workTotalCost
                        // Sempre usar o valor do totalCost do formulário para atualizar o workTotalCost
                        const workTotalCostValue = parseCurrencyToNumber(totalCostValue.toString());

                        console.log("Valor atual de proposal.workTotalCost:", proposal.workTotalCost);
                        console.log("Atualizando workTotalCost da proposta e totalCost de todos os orçamentos para:", workTotalCostValue);
                        const result = await updateProposalWorkTotalCost(proposal.id, workTotalCostValue);

                        if (result.success) {
                            console.log("Custo total da obra atualizado com sucesso:", result.workTotalCost);

                            // Atualizar o workTotalCost na proposta local para refletir a mudança imediatamente
                            proposal.workTotalCost = result.workTotalCost;
                            console.log("Novo valor de proposal.workTotalCost após atualização:", proposal.workTotalCost);

                            // Atualizar o valor no formulário para garantir consistência
                            methods.setValue("totalCost", formatCurrency(result.workTotalCost), {
                                shouldValidate: false, // Changed from true to false to prevent validation on initial load
                                shouldDirty: false,
                                shouldTouch: false
                            });
                        } else {
                            console.error("Erro ao atualizar custo total da obra:", result.error);
                            toast({
                                title: "Erro",
                                description: "Não foi possível atualizar o custo total da obra na proposta",
                                variant: "destructive"
                            });
                            // Continua o salvamento mesmo se falhar a atualização do workTotalCost
                        }
                    } catch (error) {
                        console.error("Erro ao atualizar custo total da obra:", error);
                        toast({
                            title: "Erro",
                            description: "Não foi possível atualizar o custo total da obra na proposta",
                            variant: "destructive"
                        });
                        // Continua o salvamento mesmo se falhar a atualização do workTotalCost
                    }
                }

                // Salvar o orçamento de reparo
                const data = await saveRepairBudget(formValues);
                if (data) {
                    toast({
                        title: "Sucesso",
                        description: formValues.id
                            ? "Atividade atualizada com sucesso!"
                            : "Atividade criada com sucesso!",
                        variant: "default"
                    });
                    onSave?.(); // Chama onSave apenas se existir
                    onCancelClick(true);
                }
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (gravity && urgency && tendency) {
            calculateGUT();
        }
    }, [gravity, urgency, tendency]);

    useEffect(() => {
        if (serviceCost && totalCost) {
            // Calcular o peso financeiro
            calculateFinancialWeight();

            // Verificar se o Custo Total da Obra não é menor que o Custo do serviço
            const totalCostValue = parseCurrencyToNumber(totalCost.toString());
            const serviceCostValue = parseCurrencyToNumber(serviceCost.toString());

            // Recalcular o valor restante disponível quando o custo total mudar
            const newRemainingBudget = totalCostValue - totalUsedBudget;
            setRemainingBudget(newRemainingBudget);

            
            // Valor disponível para este serviço
            let availableForThisService = newRemainingBudget;

            if (repairBudget) {
                const originalServiceCost = Number(repairBudget.serviceCost);
                console.log("- Valor original do serviço (edição):", originalServiceCost);
                // Ao editar, o valor original já está incluído no totalUsedBudget,
                // então precisamos adicioná-lo de volta ao valor disponível
                availableForThisService += originalServiceCost;
                console.log("- Valor disponível para este serviço (edição):", availableForThisService);
            }

            // Verificar se o valor do serviço ultrapassa o valor restante disponível
            if (totalCostValue < serviceCostValue) {
                // Mostrar aviso ao usuário quando o custo do serviço for maior que o custo total da obra
                console.log("ERRO: Custo do serviço maior que custo total da obra");
                methods.setError("serviceCost", {
                    type: "manual",
                    message: "O valor do Custo do serviço é maior que o Custo Total da Obra, corrija para salvar."
                });
            } else if (serviceCostValue > availableForThisService) {
                // Mostrar aviso quando o custo do serviço for maior que o valor disponível
                console.log("ERRO: Serviço ultrapassa valor disponível");
                methods.setError("serviceCost", {
                    type: "manual",
                    message: `O valor do Custo do serviço (${formatCurrency(serviceCostValue)}) ultrapassa o valor disponível (${formatCurrency(availableForThisService)}). Aumente o Custo Total da Obra ou reduza o Custo do serviço.`
                });
            } else {
                // Limpar erro se estiver tudo certo
                console.log("OK: Orçamento dentro do limite disponível");
                methods.clearErrors("serviceCost");
            }
        }
    }, [serviceCost, totalCost, totalUsedBudget, repairBudget, existingBudgets]);



    useEffect(() => {
        if (gut && financialWeight) {
            calculateIGRF();
        }
    }, [gut, financialWeight]);

    return (
        <div className="flex flex-col gap-6 relative">
            {initialLoading && (
                <div className="absolute inset-0 bg-white/80 z-50 flex items-center justify-center">
                    <div className="flex flex-col items-center gap-4">
                        <div className="size-10 border-4 border-green-500 border-t-transparent rounded-full animate-spin" />
                        <p className="text-sm font-medium text-gray-600">Carregando dados...</p>
                    </div>
                </div>
            )}
            <FormProvider {...methods} >
                <form className="flex flex-col gap-6">
                    <div className="grid grid-cols-1 sm:grid-cols-12 gap-4">
                        <div className="sm:col-span-3">
                            <CustomInput
                                name="serviceScopeId"
                                label="Serviços"
                                placeholder="Serviços"
                                type="select"
                                items={services.map((service) => ({
                                    value: service.id,
                                    label: service.name,
                                }))}
                            />
                        </div>
                        {/* <CustomInput
                            type="date"
                            label="Data da medição"
                            name="measurementDate"
                            autoComplete="off"
                        /> */}
                        <div className="sm:col-span-3">
                            <CustomInput
                                type="date"
                                label="Data de início"
                                name="startDate"
                                autoComplete="off"
                            />
                        </div>
                        <div className="sm:col-span-3">
                            <CustomInput
                                type="date"
                                label="Data de término"
                                name="endDate"
                                autoComplete="off"
                            />
                        </div>
                        <div className="sm:col-span-3">
                            <CustomInput
                                label="IMP (Índice de Manutenção Preventiva)"
                                name="imp"
                                placeholder="IMP"
                                type="number"
                                disabled
                            />
                        </div>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
                        <CustomInput
                            label="Gravidade (1 à 5)"
                            name="gravity"
                            placeholder="Gravidade"
                            type="number"
                            min={1}
                            max={5}
                        />
                        <CustomInput
                            label="Urgência (1 à 5)"
                            name="urgency"
                            placeholder="Urgência"
                            type="number"
                            min={1}
                            max={5}
                        />
                        <CustomInput
                            label="Tendência (1 à 5)"
                            name="tendency"
                            placeholder="Tendência"
                            type="number"
                            min={1}
                            max={5}
                        />
                        <CustomInput
                            label="G x U x T"
                            name="gut"
                            placeholder="G x U x T"
                            disabled
                        />

                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-12 gap-4">
                        <div className="sm:col-span-3">
                            <Controller
                                name="totalCost"
                                control={methods.control}
                                render={({ field, fieldState }) => (
                                    <>
                                        <CurrencyInput
                                            label="Custo Total da Obra"
                                            value={field.value ? parseCurrencyToNumber(field.value) : 0}
                                            name="totalCost"
                                            onChange={(value) => {
                                                field.onChange(value);
                                                // Marcar o formulário como alterado
                                                setFormChanged(true);

                                                // Recalcular o valor restante sem recarregar os orçamentos
                                                // para evitar sobrescrever o valor que o usuário acabou de digitar
                                                const newTotalCost = value;
                                                const newRemainingBudget = newTotalCost - totalUsedBudget;
                                                setRemainingBudget(newRemainingBudget);

                                                // Recalcular o peso financeiro se o custo do serviço estiver definido
                                                const serviceCostValue = parseCurrencyToNumber(methods.getValues().serviceCost?.toString() || "0");
                                                if (serviceCostValue > 0 && newTotalCost > 0) {
                                                    const financialWeightValue = serviceCostValue / newTotalCost;
                                                    methods.setValue("financialWeight", financialWeightValue.toFixed(4).toString(), {
                                                        shouldValidate: true,
                                                        shouldDirty: true,
                                                        shouldTouch: true
                                                    });
                                                }
                                            }}
                                            errorMessage={fieldState.error?.message}
                                            isTouched={fieldState.isTouched}
                                            isFormSubmitted={methods.formState.isSubmitted}
                                        />
                                        {/* Exibir informação sobre o valor restante disponível */}
                                        <div className="mt-1 text-sm">
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">Valor utilizado em outros serviços:</span>
                                                <span className="font-medium text-gray-800">{formatCurrency(totalUsedBudget)}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">Valor restante disponível:</span>
                                                <span className={`font-medium ${remainingBudget < 0 ? 'text-red-600' : 'text-green-600'}`}>
                                                    {formatCurrency(remainingBudget)}
                                                </span>
                                            </div>
                                        </div>
                                    </>
                                )}
                            />
                        </div>
                        <div className="sm:col-span-2">
                            <Controller
                                name="serviceCost"
                                control={methods.control}
                                render={({ field, fieldState }) => (
                                    <CurrencyInput
                                        label="Custo do serviço"
                                        value={field.value ? parseCurrencyToNumber(field.value) : 0}
                                        name="serviceCost"
                                        onChange={(value) => {
                                            field.onChange(value);
                                            // Marcar o formulário como alterado
                                            setFormChanged(true);

                                            // Forçar a validação quando o valor mudar
                                            methods.trigger("serviceCost");

                                            // Recalcular o peso financeiro
                                            const totalCostValue = parseCurrencyToNumber(methods.getValues().totalCost?.toString() || "0");
                                            if (totalCostValue > 0) {
                                                const financialWeightValue = value / totalCostValue;
                                                methods.setValue("financialWeight", financialWeightValue.toFixed(4).toString(), {
                                                    shouldValidate: true,
                                                    shouldDirty: true,
                                                    shouldTouch: true
                                                });
                                            }
                                        }}
                                        errorMessage={fieldState.error?.message}
                                        isTouched={fieldState.isTouched} // Mostrar mensagens de erro apenas quando o campo for tocado
                                        isFormSubmitted={methods.formState.isSubmitted}
                                    />
                                )}
                            />
                        </div>
                        <div className="sm:col-span-3">
                            <Controller
                                name="financialWeight"
                                control={methods.control}
                                render={({ field }) => (
                                    <div className="grid gap-2">
                                        <div className="flex mb-2">
                                            <Label htmlFor="financialWeight" className="font-bold text-gray-700 ">
                                                Peso financeiro do serviço (PS)
                                            </Label>
                                        </div>
                                        <div className="relative">
                                            <Input
                                                id="financialWeight"
                                                name="financialWeight"
                                                value={field.value || ""}
                                                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                                                disabled
                                            />
                                        </div>
                                    </div>
                                )}
                            />
                        </div>
                        <div className="sm:col-span-4">
                            <Controller
                                name="igrf"
                                control={methods.control}
                                render={({ field }) => (
                                    <div className="grid gap-2">
                                        <div className="flex mb-2">
                                            <Label htmlFor="igrf" className="font-bold text-gray-700">
                                                IGRf (Índice Gravidade de Reparos Financeiro)
                                            </Label>
                                        </div>
                                        <div className="relative">
                                            <Input
                                                id="igrf"
                                                name="igrf"
                                                value={field.value || ""}
                                                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                                                disabled
                                            />
                                        </div>
                                    </div>
                                )}
                            />
                        </div>
                    </div>
                </form>
                <div className="flex gap-4 justify-end">

                    <Button
                        type="button"
                        variant="outline"
                        className="border-blue-500 text-blue-500 hover:text-blue-400 hover:border-blue-400"
                        onClick={() => onCancelClick(formChanged)}
                        disabled={loading}
                    >
                        Cancelar
                    </Button>

                    <Button
                        type="button"
                        className="bg-green-500 hover:bg-green-400"
                        onClick={handleSubmit}
                        disabled={loading}
                    >
                        {repairBudget ? "Atualizar" : "Salvar"}
                    </Button>
                </div>
            </FormProvider>
            {loading && (
                <div className="absolute inset-0 bg-white/80 z-50 flex items-center justify-center">
                    <div className="flex flex-col items-center gap-4">
                        <div className="size-10 border-4 border-green-500 border-t-transparent rounded-full animate-spin" />
                        <p className="text-sm font-medium text-gray-600">Salvando dados...</p>
                    </div>
                </div>
            )}
        </div>
    );
}
