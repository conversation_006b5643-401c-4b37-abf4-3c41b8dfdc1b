"use client";

import { useState, useEffect } from "react";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import { Card, CardContent } from "@/src/components/ui/card";
import { useToast } from "@/src/hooks/use-toast";
import { CustomerSearch } from "./customer-search";
import { Switch } from "@/src/components/ui/switch";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface ProposalSearchProps {
  onProposalPermissionChange: (proposalId: string, enabled: boolean) => void;
  userId: string;
  loading?: boolean;
  isOwner?: boolean;
  refreshKey?: any;
}

interface ProposalPermission {
  proposalId: string;
  name: string;
  customer: { name: string };
  enabled: boolean;
  situation?: string;
}

const SITUATION_LABELS: Record<string, string> = {
  NEW: "Nova",
  LOST: "Proposta perdida",
  UNDER_ANALYSIS: "Proposta em análise",
  PROPOSAL_ACCEPTED: "Proposta aceita",
  SIGNED: "Proposta assinada",
  PROJECT_IN_PROGRESS: "Projeto em andamento",
  PROJECT_FINISHED: "Projeto concluído",
};

export function ProposalSearch({ onProposalPermissionChange, userId, loading = false, isOwner = false, refreshKey }: ProposalSearchProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [customerId, setCustomerId] = useState("");
  const [proposals, setProposals] = useState<ProposalPermission[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [total, setTotal] = useState(0);
  const { toast } = useToast();
  const [allEnabled, setAllEnabled] = useState(false);
  const [allLoading, setAllLoading] = useState(false);

  useEffect(() => {
    setPage(1); // Sempre que filtro mudar, volta para página 1
  }, [searchTerm, customerId, userId]);

  useEffect(() => {
    if (!userId) {
      setProposals([]);
      setTotal(0);
      return;
    }
    const fetchProposals = async () => {
      try {
        setSearchLoading(true);
        let url = `/api/users/${userId}/proposal-permissions`;
        const params: string[] = [];
        if (searchTerm) params.push(`search=${encodeURIComponent(searchTerm)}`);
        if (customerId) params.push(`customerId=${encodeURIComponent(customerId)}`);
        params.push(`page=${page}`);
        params.push(`pageSize=${pageSize}`);
        if (params.length) url += `?${params.join("&")}`;
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error("Falha ao carregar propostas");
        }
        const result = await response.json();
        setProposals(Array.isArray(result.data) ? result.data : []);
        setTotal(result.total || 0);
      } catch (error) {
        console.error("Erro ao carregar propostas:", error);
        toast({
          title: "Erro",
          description: "Não foi possível carregar as propostas",
          variant: "destructive",
        });
      } finally {
        setSearchLoading(false);
      }
    };
    const debounce = setTimeout(() => {
      fetchProposals();
    }, 500);
    return () => clearTimeout(debounce);
  }, [userId, searchTerm, customerId, page, pageSize, refreshKey]);

  useEffect(() => {
    if (proposals.length > 0) {
      setAllEnabled(proposals.every((p) => p.enabled));
    } else {
      setAllEnabled(false);
    }
  }, [proposals]);

  const handleToggleAll = async (checked: boolean) => {
    if (!userId) return;
    setAllLoading(true);
    try {
      const response = await fetch(`/api/users/${userId}/proposal-permissions`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ enabled: checked }),
      });
      if (!response.ok) {
        throw new Error("Falha ao atualizar todas as permissões de propostas");
      }
      toast({
        title: "Sucesso",
        description: checked ? "Todas as permissões concedidas" : "Todas as permissões removidas",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Não foi possível atualizar todas as permissões" + error,
        variant: "destructive",
      });
    } finally {
      setAllLoading(false);
    }
  };

  const totalPages = Math.ceil(total / pageSize);

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row md:items-end gap-2 md:gap-4">
        <div className="flex-1">
          <Label>Buscar proposta</Label>
          <Input
            placeholder="Digite o nome da proposta ou contrato..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            disabled={loading}
          />
        </div>
        <div className="flex-1">
          <CustomerSearch
            onCustomerChange={setCustomerId}
            disabled={loading}
          />
        </div>
      </div>
      <div className="flex items-center gap-4 mb-2">
        <Switch
          checked={allEnabled}
          onCheckedChange={async (checked) => {
            await handleToggleAll(checked);
            // Força o refresh da lista após o PATCH
            if (typeof window !== "undefined") {
              const event = new CustomEvent("refreshProposalPermissions");
              window.dispatchEvent(event);
            }
          }}
          disabled={loading || allLoading || isOwner}
        />
        <span className="font-medium">Permitir todos</span>
        {allLoading && <span className="text-xs text-gray-500 ml-2">Atualizando...</span>}
      </div>
      {searchLoading ? (
        <div className="flex justify-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 gap-2 max-h-[300px] overflow-y-auto">
            {proposals.map((proposal) => (
              <Card key={proposal.proposalId} className="flex flex-col justify-between">
                <CardContent className="p-3 flex flex-row items-center justify-between gap-4">
                  <div>
                    <div className="font-medium">{proposal.name}</div>
                    <div className="text-sm text-gray-500">
                      Cliente: {proposal.customer?.name}
                    </div>
                    {proposal.situation && (
                      <div className="text-xs text-gray-400 mt-1">
                        {SITUATION_LABELS[proposal.situation] || proposal.situation}
                      </div>
                    )}
                  </div>
                  <Switch
                    checked={isOwner ? true : proposal.enabled}
                    onCheckedChange={(checked) => onProposalPermissionChange(proposal.proposalId, checked)}
                    disabled={isOwner}
                  />
                </CardContent>
              </Card>
            ))}
            {proposals.length === 0 && (searchTerm || customerId) && (
              <div className="text-center py-4 text-gray-500">
                Nenhuma proposta encontrada
              </div>
            )}
          </div>
          <div className="flex items-center justify-end mt-2">
            <span className="text-sm text-gray-600 mr-4">
              {total > 0 ? `Exibindo ${proposals.length} de ${total} propostas` : ""}
            </span>
            <div className="flex gap-2 items-center">
              <button
                className="px-2 py-1 border rounded disabled:opacity-50 flex items-center justify-center"
                onClick={() => setPage((p) => Math.max(1, p - 1))}
                disabled={page === 1 || searchLoading}
                aria-label="Página anterior"
              >
                <ChevronLeft className="w-4 h-4" />
              </button>
              <span className="text-sm min-w-[90px] text-center">Página {page} de {totalPages || 1}</span>
              <button
                className="px-2 py-1 border rounded disabled:opacity-50 flex items-center justify-center"
                onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                disabled={page === totalPages || searchLoading || totalPages === 0}
                aria-label="Próxima página"
              >
                <ChevronRight className="w-4 h-4" />
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
} 