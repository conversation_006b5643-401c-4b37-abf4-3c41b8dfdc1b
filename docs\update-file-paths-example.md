# Atualização de ThumbnailPath

Esta documentação explica como usar a API e action criadas para adicionar o prefixo "matriz/" aos thumbnails que começam apenas com "thumbnails/".

## API Endpoint

### POST `/api/files/update-paths`

Adiciona o prefixo "matriz/" a todos os thumbnails na coluna `thumbnailPath` que começam com "thumbnails/".

**Exemplo de uso:**

```javascript
// Chamada via fetch
const response = await fetch('/api/files/update-paths', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  }
});

const result = await response.json();
console.log(result);
```

**Resposta de sucesso:**
```json
{
  "success": true,
  "message": "Atualização de thumbnailPath concluída com sucesso",
  "totalFiles": 5,
  "updatedFiles": 5,
  "logs": [
    {
      "fileId": "clx123...",
      "fileName": "img-20250722-wa0300.jpg",
      "oldThumbnailPath": "thumbnails/cmd3dk0fn0000any9zb02n8nf",
      "newThumbnailPath": "matriz/thumbnails/cmd3dk0fn0000any9zb02n8nf",
      "status": "updated"
    }
  ]
}
```

## Server Action

### `updateFilePathsWithMatrizPrefix()`

Função server action que pode ser chamada diretamente em componentes React ou outras server actions.

**Exemplo de uso:**

```typescript
import { updateFilePathsWithMatrizPrefix } from '@/src/actions/files';

// Em um componente ou server action
const handleUpdatePaths = async () => {
  const result = await updateFilePathsWithMatrizPrefix();
  
  if (result.success) {
    console.log(`${result.updatedFiles} arquivos atualizados de ${result.totalFiles} encontrados`);
    console.log('Logs:', result.logs);
  } else {
    console.error('Erro:', result.message);
  }
};
```

## Exemplo de Transformação

### Thumbnail sem prefixo "matriz/"

**Antes:**
```text
thumbnails/cmd3dk0fn0000any9zb02n8nf
```

**Depois:**
```text
matriz/thumbnails/cmd3dk0fn0000any9zb02n8nf
```

## Características

- ✅ Busca arquivos com `thumbnailPath` começando com "thumbnails/"
- ✅ Adiciona o prefixo "matriz/" na frente do thumbnailPath
- ✅ Operação simples e direta: `thumbnails/abc` → `matriz/thumbnails/abc`
- ✅ Fornece logs detalhados de cada operação (updated, error)
- ✅ Inclui informações completas de thumbnail nos logs
- ✅ Trata erros individualmente por arquivo
- ✅ Retorna estatísticas completas da operação
- ✅ Disponível tanto como API quanto como server action

## Segurança

- **ATENÇÃO:** A operação NÃO é idempotente - executar múltiplas vezes criará duplicações
- Apenas arquivos com `thumbnailPath` começando com "thumbnails/" são afetados
- Logs detalhados permitem auditoria das mudanças
- Tratamento de erros individual por arquivo
- **Recomendação:** Execute apenas uma vez ou verifique antes de executar novamente
