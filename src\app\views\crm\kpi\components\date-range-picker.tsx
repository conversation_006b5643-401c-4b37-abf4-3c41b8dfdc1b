"use client";

import * as React from "react";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { DateRange } from "react-day-picker";

import { cn } from "@/src/lib/utils";
import { Button } from "@/src/components/ui/button";
import { Calendar } from "@/src/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/src/components/ui/popover";

interface DateRangePickerProps {
  dateRange: DateRange | undefined;
  onDateRangeChange: (range: DateRange | undefined) => void;
  className?: string;
}

export function DateRangePicker({
  dateRange,
  onDateRangeChange,
  className,
}: DateRangePickerProps) {
  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "w-full justify-start text-left font-normal border-2 border-green-100 bg-green-50 hover:bg-green-100 hover:border-green-200 transition-all duration-200 shadow-sm",
              !dateRange && "text-muted-foreground",
              dateRange && "text-green-700 font-medium"
            )}
          >
            <CalendarIcon className="mr-2 h-5 w-5 text-green-600" />
            {dateRange?.from ? (
              dateRange.to ? (
                <div className="flex flex-col">
                  <span className="text-xs text-green-600 font-semibold">Período selecionado</span>
                  <span>
                    {format(dateRange.from, "dd/MM/yyyy", { locale: ptBR })} -{" "}
                    {format(dateRange.to, "dd/MM/yyyy", { locale: ptBR })}
                  </span>
                </div>
              ) : (
                <div className="flex flex-col">
                  <span className="text-xs text-green-600 font-semibold">Data selecionada</span>
                  <span>{format(dateRange.from, "dd/MM/yyyy", { locale: ptBR })}</span>
                </div>
              )
            ) : (
              <span>Selecione um período</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0 border-2 border-green-100 shadow-lg" align="start">
          <div className="bg-green-50 p-3 border-b border-green-100">
            <h3 className="font-medium text-green-800">Selecione um período</h3>
            <p className="text-xs text-green-600">Escolha as datas para filtrar os indicadores</p>
          </div>
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={dateRange?.from}
            selected={dateRange}
            onSelect={onDateRangeChange}
            numberOfMonths={2}
            locale={ptBR}
            className="rounded-md bg-white p-3"
            classNames={{
              day_selected: "bg-green-600 text-white hover:bg-green-500 hover:text-white focus:bg-green-600 focus:text-white",
              day_range_middle: "bg-green-100 text-green-900",
              day_range_end: "bg-green-600 text-white hover:bg-green-500 hover:text-white focus:bg-green-600 focus:text-white",
              day_range_start: "bg-green-600 text-white hover:bg-green-500 hover:text-white focus:bg-green-600 focus:text-white",
            }}
          />
          <div className="flex justify-between p-4 border-t border-green-100 bg-green-50">
            <Button
              variant="ghost"
              onClick={() => onDateRangeChange(undefined)}
              size="sm"
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              Limpar seleção
            </Button>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                className="border-green-200 bg-white text-green-700 hover:bg-green-100 hover:text-green-800"
                onClick={() => {
                  const today = new Date();
                  const from = new Date(today.getFullYear(), today.getMonth(), 1);
                  const to = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                  onDateRangeChange({ from, to });
                }}
              >
                Mês atual
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="border-green-200 bg-white text-green-700 hover:bg-green-100 hover:text-green-800"
                onClick={() => {
                  const today = new Date();
                  const from = new Date(today.getFullYear(), 0, 1);
                  const to = new Date(today.getFullYear(), 11, 31);
                  onDateRangeChange({ from, to });
                }}
              >
                Ano atual
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="border-green-200 bg-white text-green-700 hover:bg-green-100 hover:text-green-800"
                onClick={() => {
                  const today = new Date();
                  const from = new Date(today.getFullYear() - 1, 0, 1);
                  const to = new Date(today.getFullYear() - 1, 11, 31);
                  onDateRangeChange({ from, to });
                }}
              >
                Ano anterior
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
