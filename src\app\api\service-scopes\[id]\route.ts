import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const proposals = await prisma.proposal.findMany({
      where: {
        serviceScopes: {
          some: {
            id: params.id,
            organizationId,
          },
        },
      },
    });

    if (proposals.length > 0) {
      return NextResponse.json(
        {
          error: true,
          title: "Erro ao remover escopo de serviço",
          message: "Existem propostas vinculadas ao escopo de serviço",
        },
        { status: 400 }
      );
    }

    await prisma.repairBudget.deleteMany({
      where: {
        serviceScopeId: params.id,
        serviceScope: {
          organizationId,
        },
      },
    });

    await prisma.productivity.deleteMany({
      where: {
        serviceId: params.id,
        service: {
          organizationId,
        },
      },
    });

    await prisma.serviceScope.delete({
      where: {
        id: params.id,
        organizationId,
      },
    });

    return NextResponse.json({
      error: false,
      message: "Serviço excluído com sucesso!",
    });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      {
        error: true,
        title: "Erro ao remover escopo de serviço",
        message: "Ocorreu um erro ao tentar remover o escopo de serviço",
      },
      { status: 500 }
    );
  }
}
