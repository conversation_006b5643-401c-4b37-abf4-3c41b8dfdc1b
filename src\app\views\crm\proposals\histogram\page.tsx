"use client"
import ContentWrapper from "@/src/components/content-wrapper";
import { useEffect, useState } from "react";
import HistogramChart from "./components/histogram-chart";

export default function Histogram() {
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        setLoading(false);
    }, [])

    return (
        <ContentWrapper title="Histograma" loading={loading}>
            <HistogramChart />
        </ContentWrapper>
    )
}