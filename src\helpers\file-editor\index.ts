"use server";

import { persistFileEditorFromBuffer } from "@/src/actions/file-editor";
import { getBufferFromFile<PERSON>ey } from "@/src/lib/minio";
import { prisma } from "@/src/lib/prisma";
import {
  FileEditorFileType,
  FileEditorMimetypesMap,
} from "@/src/types/core/file-editor";
import { mergeVariablesDocx } from "./replacers/docx-variable-replace";
import { mergeVariablesXlsx } from "./replacers/xlsx-variable-replace";

export async function replaceFromFileEditorIdAndVariables(
  fileEditorId: string,
  variables: Record<string, any>,
  fileName?: string
) {
  const fileEditor = await prisma.fileEditor.findUnique({
    where: { id: fileEditorId },
  });
  if (!fileEditor) {
    throw Error("File not exists");
  }
  if (!variables) {
    throw Error("There is no data to replace");
  }
  if (!fileName) {
    fileName = `${Date.now()}-file-editor-replaced`;
  }

  const keyPath = fileEditor.path ? (fileEditor.path.endsWith('/') ? fileEditor.path + fileEditor.key : fileEditor.path + '/' + fileEditor.key) : fileEditor.key;
  const replacedBuffer = await replaceAndGetBufferFromFileKey(
    keyPath,
    fileEditor.mimetype,
    variables
  );
  return await persistFileEditorFromBuffer(
    replacedBuffer,
    fileName,
    fileEditor.mimetype
  );
}

async function replaceAndGetBufferFromFileKey(
  fileKey: string,
  fileType: string,
  variables: Record<string, any>
) {
  if (FileEditorMimetypesMap.get(FileEditorFileType.DOCX)?.includes(fileType)) {
    const bufferFile = await getBufferFromFileKey(fileKey);
    return mergeVariablesDocx(bufferFile, variables);
  }
  if (FileEditorMimetypesMap.get(FileEditorFileType.XLSX)?.includes(fileType)) {
    const bufferFile = await getBufferFromFileKey(fileKey);
    return mergeVariablesXlsx(bufferFile, variables);
  }

  throw Error("Type not supported:" + fileType);
}
