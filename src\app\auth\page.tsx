import { auth } from "@/src/providers/auth";
import bgLogin from "@public/bg-login.png";
import whiteLogo from "@public/logo-white.svg";
import Image from "next/image";
import Link from "next/link";
import { redirect } from "next/navigation";
import { UserAuthForm } from "./components/user-auth-form";

export default async function Auth() {
	const session = await auth();

	return (
		<>
			{session?.user ? (
				redirect("/views/control-panel")
			) : (
				<div className="fixed inset-0 w-screen h-screen">
					{/* Imagem de fundo */}
					<Image
						src={bgLogin}
						alt="Background"
						className="absolute inset-0 w-full h-full object-cover"
					/>

					{/* Container principal */}
					<div className="relative z-10 flex flex-col lg:flex-row h-screen container mx-auto px-2 sm:px-6 lg:px-10 justify-between pt-6 lg:pt-24 gap-6 overflow-auto">

						{/* Bloco de texto e logo */}
						<div className="lg:max-w-2xl flex-1">
							<Image src={whiteLogo} alt="Logo" height={64} width={218} />
							<div className="flex flex-col gap-5 mt-6 lg:mt-8">
								<h1 className="font-sans text-xl sm:text-2xl lg:text-3xl font-bold text-white">
									Inteligência aplicada na Fiscalização de obras e Inspeções Prediais.
								</h1>
								<p className="font-zenKaku text-justify text-base sm:text-lg text-white leading-7 font-extralight">
									O Ageu revoluciona a engenharia consultiva e diagnóstica ao integrar inteligência artificial e metodologias avançadas, proporcionando um acompanhamento preciso e diagnósticos preditivos personalizados.
									Mais do que um software, é uma plataforma completa para Fiscalização e Inspeção de Obras, que, por meio de métricas inteligentes, transforma dados em decisões estratégicas, reduzindo riscos, otimizando custos e garantindo máxima eficiência.
								</p>
								<div className="border-t-2 border-white w-12 py-2" />
							</div>
						</div>

						{/* Bloco de login */}
						<div className="flex flex-col lg:w-[500px] bg-white backdrop-blur-md rounded-lg px-6 sm:px-8 lg:px-10 py-10 lg:py-14 shadow-lg">
							<div className="flex flex-col items-center justify-center gap-1 mb-2">
								<h1 className="text-xl sm:text-2xl font-semibold tracking-tight text-gray-700">
									Login
								</h1>
								<span className="bg-background px-2 text-muted-foreground">
									Comece usar agora mesmo o Ageu
								</span>
							</div>
							<div className=" inset-0 flex items-center mb-5">
								<span className="w-full border-t" />
							</div>
							<UserAuthForm />
							<p className="px-4 sm:px-6 text-center text-sm text-muted-foreground mt-4">
								Ao clicar em continuar, você concorda com nossos{" "}
								<Link href="#" className="underline hover:text-primary">termos de serviço</Link>{" "}
								e{" "}
								<Link href="#" className="underline hover:text-primary">política de privacidade</Link>.
							</p>
						</div>
					</div>
				</div>

			)}
		</>
	);
}
