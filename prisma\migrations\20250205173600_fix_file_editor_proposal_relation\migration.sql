/*
  Warnings:

  - You are about to drop the column `bucket` on the `ProposalTemplate` table. All the data in the column will be lost.
  - You are about to drop the column `filename` on the `ProposalTemplate` table. All the data in the column will be lost.
  - You are about to drop the column `isLocked` on the `ProposalTemplate` table. All the data in the column will be lost.
  - You are about to drop the column `key` on the `ProposalTemplate` table. All the data in the column will be lost.
  - You are about to drop the column `lockedBy` on the `ProposalTemplate` table. All the data in the column will be lost.
  - You are about to drop the column `mimetype` on the `ProposalTemplate` table. All the data in the column will be lost.
  - You are about to drop the column `size` on the `ProposalTemplate` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `ProposalTemplate` table. All the data in the column will be lost.
  - You are about to drop the column `version` on the `ProposalTemplate` table. All the data in the column will be lost.
  - Added the required column `description` to the `ProposalTemplate` table without a default value. This is not possible if the table is not empty.
  - Added the required column `fileEditorId` to the `ProposalTemplate` table without a default value. This is not possible if the table is not empty.
  - Added the required column `title` to the `ProposalTemplate` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "ProposalTemplate_key_key";

-- AlterTable
ALTER TABLE "ProposalTemplate" DROP COLUMN "bucket",
DROP COLUMN "filename",
DROP COLUMN "isLocked",
DROP COLUMN "key",
DROP COLUMN "lockedBy",
DROP COLUMN "mimetype",
DROP COLUMN "size",
DROP COLUMN "updatedAt",
DROP COLUMN "version",
ADD COLUMN     "description" TEXT NOT NULL,
ADD COLUMN     "fileEditorId" TEXT NOT NULL,
ADD COLUMN     "title" TEXT NOT NULL;

-- AddForeignKey
ALTER TABLE "ProposalTemplate" ADD CONSTRAINT "ProposalTemplate_fileEditorId_fkey" FOREIGN KEY ("fileEditorId") REFERENCES "FileEditor"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
