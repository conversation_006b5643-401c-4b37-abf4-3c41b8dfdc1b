import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";
import { storageProvider } from "@/src/lib/storage";

export async function DELETE(
  req: Request,
  { params }: { params: { inspectionParameterId: string; videoId: string } }
) {
  const { videoId } = params;
  try {
    // Buscar o vídeo e o caminho do arquivo
    const video = await prisma.video.findUnique({
      where: { id: videoId },
      select: { file: { select: { path: true } } },
    });
    if (!video) {
      return NextResponse.json({ error: "Vídeo não encontrado" }, { status: 404 });
    }
    // Remover o registro Video
    await prisma.video.delete({
      where: { id: videoId },
    });
    // Excluir o arquivo do Minio usando o caminho correto
    if (video.file?.path) {
      await storageProvider.delete(video.file.path);
    }
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Erro ao remover vídeo da inspeção:", error);
    return NextResponse.json({ error: "Erro ao remover vídeo da inspeção" }, { status: 500 });
  }
} 