import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";
import { auth } from "@/src/providers/auth";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";
import { getAllSystemRoutes } from "@/src/actions/route-permissions";

// GET - Buscar permissões do usuário
export async function GET(
  request: Request,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new NextResponse("Não autorizado", { status: 401 });
    }

    const organization = await getCurrentOrganization();
    if (!organization) {
      return new NextResponse("Organização não encontrada", { status: 404 });
    }

    // Buscar o papel do usuário na organização
    const membership = await prisma.membership.findFirst({
      where: {
        userId: params.userId,
        organizationId: organization.organizationId,
      },
      select: {
        role: true,
      },
    });
    const userRole = membership?.role || null;

    // Buscar todas as rotas do sistema
    const systemRoutes = await getAllSystemRoutes();

    // Buscar permissões do usuário
    const userPermissions = await prisma.userRoutePermission.findMany({
      where: {
        userId: params.userId,
        organizationId: organization.organizationId,
      },
      select: {
        route: true,
        enabled: true,
      },
    });

    // Buscar todas as permissões de rota cadastradas na organização (incluindo as com query string)
    const allRoutePermissions = await prisma.routePermission.findMany({
      where: {
        organizationId: organization.organizationId,
      },
      select: {
        route: true,
      },
    });

    // Montar lista de todas as rotas únicas (do sistema + cadastradas no banco)
    const allRoutesSet = new Set<string>([...systemRoutes.map(r => r.route), ...allRoutePermissions.map(r => r.route)]);
    const allRoutes = Array.from(allRoutesSet);

    // Mapear permissões existentes
    const permissionsMap = new Map(userPermissions.map(p => [p.route, p.enabled]));

    // Mapear títulos das rotas do sistema
    const titleMap = new Map(systemRoutes.map(r => [r.route, r.title]));

    // Retornar todas as rotas, permitido por padrão
    const allPermissions = allRoutes.map(route => ({
      route,
      enabled: permissionsMap.has(route) ? permissionsMap.get(route) : true,
      title: titleMap.get(route) || route,
    }));

    return NextResponse.json({ permissions: allPermissions, role: userRole });
  } catch (error) {
    console.error("Erro ao buscar permissões:", error);
    return new NextResponse("Erro interno do servidor", { status: 500 });
  }
}

// POST - Adicionar ou atualizar permissão ao usuário
export async function POST(
  request: Request,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new NextResponse("Não autorizado", { status: 401 });
    }

    const organization = await getCurrentOrganization();
    if (!organization) {
      return new NextResponse("Organização não encontrada", { status: 404 });
    }

    const body = await request.json();
    const { route, enabled } = body;

    if (!route) {
      return new NextResponse("Rota é obrigatória", { status: 400 });
    }

    // Garantir que existe o registro de permissão de rota
    await prisma.routePermission.upsert({
      where: {
        route_organizationId: {
          route,
          organizationId: organization.organizationId,
        },
      },
      update: {},
      create: {
        route,
        organizationId: organization.organizationId,
      },
    });

    // Criar ou atualizar permissão do usuário
    const permission = await prisma.userRoutePermission.upsert({
      where: {
        userId_route_organizationId: {
          userId: params.userId,
          route,
          organizationId: organization.organizationId,
        },
      },
      update: {
        enabled: enabled === false ? false : true,
        updatedAt: new Date(),
      },
      create: {
        userId: params.userId,
        organizationId: organization.organizationId,
        route,
        enabled: enabled === false ? false : true,
      },
    });

    return NextResponse.json(permission);
  } catch (error) {
    console.error("Erro ao adicionar permissão:", error);
    return new NextResponse("Erro interno do servidor", { status: 500 });
  }
}

// DELETE - Remover permissão do usuário
export async function DELETE(
  request: Request,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new NextResponse("Não autorizado", { status: 401 });
    }

    const organization = await getCurrentOrganization();
    if (!organization) {
      return new NextResponse("Organização não encontrada", { status: 404 });
    }

    const body = await request.json();
    const { route } = body;

    if (!route) {
      return new NextResponse("Rota é obrigatória", { status: 400 });
    }

    console.log("Removendo permissão:", { userId: params.userId, route });

    // Primeiro, verificar se a permissão existe
    const existingPermission = await prisma.userRoutePermission.findUnique({
      where: {
        userId_route_organizationId: {
          userId: params.userId,
          route,
          organizationId: organization.organizationId,
        },
      },
    });

    if (!existingPermission) {
      return new NextResponse("Permissão não encontrada", { status: 404 });
    }

    // Remover permissão
    await prisma.userRoutePermission.delete({
      where: {
        userId_route_organizationId: {
          userId: params.userId,
          route,
          organizationId: organization.organizationId,
        },
      },
    });

    console.log("Permissão removida com sucesso");

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("Erro ao remover permissão:", error);
    return new NextResponse("Erro interno do servidor", { status: 500 });
  }
}

// PATCH - Atualizar todas as permissões de rotas do usuário em lote
export async function PATCH(
  request: Request,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new NextResponse("Não autorizado", { status: 401 });
    }

    const organization = await getCurrentOrganization();
    if (!organization) {
      return new NextResponse("Organização não encontrada", { status: 404 });
    }

    const body = await request.json();
    const { routes, enabled } = body;
    if (!Array.isArray(routes) || typeof enabled !== "boolean") {
      return new NextResponse("Parâmetros inválidos", { status: 400 });
    }

    // Atualizar todas as permissões em lote
    await Promise.all(
      routes.map(async (route: string) => {
        await prisma.routePermission.upsert({
          where: {
            route_organizationId: {
              route,
              organizationId: organization.organizationId,
            },
          },
          update: {},
          create: {
            route,
            organizationId: organization.organizationId,
          },
        });
        await prisma.userRoutePermission.upsert({
          where: {
            userId_route_organizationId: {
              userId: params.userId,
              route,
              organizationId: organization.organizationId,
            },
          },
          update: {
            enabled,
            updatedAt: new Date(),
          },
          create: {
            userId: params.userId,
            organizationId: organization.organizationId,
            route,
            enabled,
          },
        });
      })
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Erro ao atualizar permissões em lote:", error);
    return new NextResponse("Erro interno do servidor", { status: 500 });
  }
} 