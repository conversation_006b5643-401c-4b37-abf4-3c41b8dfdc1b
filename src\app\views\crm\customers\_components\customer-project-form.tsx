import { CustomInput } from "@/src/components/app-input";
import FileUploadField from "@/src/components/file-upload-field";
import { FormProvider, UseFormReturn, Controller } from "react-hook-form";
import { AreaInput } from "@/src/components/area-input";
import { CurrencyInput } from "@/src/components/currency-input";
import { ProjectSchema } from "../_schemas/project.schema";
import { useEffect, useState } from "react";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { X } from "lucide-react";

interface CustomerProjectFormProps {
	methods: UseFormReturn<ProjectSchema>;
	onFileInputChange: (files: File[] | null) => void; // Alterado para múltiplos arquivos
}

export default function CustomerProjectForm({
	methods,
	onFileInputChange,
}: CustomerProjectFormProps) {
	const [existingFiles, setExistingFiles] = useState<{ id?: string; name?: string }[]>([]);
	const [newFiles, setNewFiles] = useState<File[]>([]);
	// const fileInputRef = useRef<HTMLInputElement>(null);

	// Verificar se há arquivos existentes quando o formulário é carregado
	useEffect(() => {
		const formValues = methods.getValues();
		console.log('Valores do formulário:', formValues);

		// Primeiro, verificar se há arquivos no campo 'files' (array)
		if (formValues.files && Array.isArray(formValues.files)) {
			const files = formValues.files
				.filter((f: any) => f && typeof f === "object" && "id" in f)
				.map((f: any) => ({ id: f.id, name: f.name }));
			setExistingFiles(files);
			console.log('Arquivos existentes carregados (files):', files);
		}
		// Se não houver 'files', verificar se há arquivo único no campo 'file'
		else if (formValues.file && Array.isArray(formValues.file)) {
			const files = formValues.file
				.filter((f: any) => f && typeof f === "object" && "id" in f)
				.map((f: any) => ({ id: f.id, name: f.name }));
			setExistingFiles(files);
			console.log('Arquivos existentes carregados (file array):', files);
		} else if (formValues.file && typeof formValues.file === "object" && "id" in formValues.file) {
			setExistingFiles([{ id: formValues.file.id, name: formValues.file.name }]);
			console.log('Arquivo existente carregado (file único):', formValues.file);
		} else {
			setExistingFiles([]);
			console.log('Nenhum arquivo existente encontrado');
		}
	}, [methods]);

	// Adicionar um useEffect para monitorar mudanças nos valores do formulário
	useEffect(() => {
		const subscription = methods.watch((value, { name }) => {
			if (name === 'files' || name === 'file') {
				console.log('Mudança detectada no campo:', name, 'Valor:', value[name]);
				// Recarregar arquivos existentes quando houver mudança
				const formValues = methods.getValues();
				if (formValues.files && Array.isArray(formValues.files)) {
					const files = formValues.files
						.filter((f: any) => f && typeof f === "object" && "id" in f)
						.map((f: any) => ({ id: f.id, name: f.name }));
					setExistingFiles(files);
				}
			}
		});
		return () => subscription.unsubscribe();
	}, [methods]);

	const handleFileChange = (files: File[] | null) => {
		// Adiciona novos arquivos aos já existentes, evitando duplicatas pelo nome
		const fileArr = files && files.length > 0 ? files : [];
		const existingNames = new Set(newFiles.map(f => f.name));
		const mergedFiles = [
			...newFiles,
			...fileArr.filter(f => !existingNames.has(f.name))
		];
		setNewFiles(mergedFiles);
		onFileInputChange(mergedFiles.length > 0 ? mergedFiles : null);
	};

	const handleRemoveNewFile = (index: number) => {
		const updated = [...newFiles];
		updated.splice(index, 1);
		setNewFiles(updated);
		onFileInputChange(updated);
	};

	const handleRemoveExistingFile = (index: number) => {
		const updated = [...existingFiles];
		updated.splice(index, 1);
		setExistingFiles(updated);

		// Atualizar o formulário para refletir a remoção
		methods.setValue('files', updated);
		console.log('Arquivo existente removido, arquivos restantes:', updated);
	};

	// const handleAddFilesClick = () => {
	// 	fileInputRef.current?.click();
	// };

	return (
		<FormProvider {...methods}>
			<form className="flex flex-col gap-6">
				<div className="grid grid-cols-1 gap-4">
					<CustomInput label="Projeto" name="project" placeholder="Nome do projeto" />
				</div>
				<div className="grid grid-cols-1 gap-4">
					<Controller
						name="serviceType"
						control={methods.control}
						render={({ field }) => (
							<div className="space-y-1">
								<label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
									Tipo de Contrato
								</label>
								<Select
									value={field.value || ""}
									onValueChange={field.onChange}
								>
									<SelectTrigger className="w-full h-10">
										<SelectValue placeholder="Selecione o tipo de contrato" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="INSPECAO">Inspeção</SelectItem>
										<SelectItem value="FISCALIZACAO">Fiscalização</SelectItem>
										<SelectItem value="GERENCIAMENTO">Gerenciamento</SelectItem>
										<SelectItem value="CONSULTORIA">Consultoria</SelectItem>
										<SelectItem value="PROJETO">Projeto</SelectItem>
									</SelectContent>
								</Select>
							</div>
						)}
					/>
				</div>
				<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
					<CustomInput
						type="date"
						label="Data de início"
						name="startDate"
						autoComplete="off"
						hideErrorMessage={true}
					/>
					<CustomInput
						type="date"
						label="Data de término"
						name="endDate"
						autoComplete="off"
						hideErrorMessage={true}
					/>
				</div>
				<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
					<Controller
						name="area"
						control={methods.control}
						render={({ field }) => (
							<AreaInput
								label="Área (m²)"
								value={field.value}
								name="area"
								placeholder="0,00"
								hideErrorMessage={true}
								onChange={field.onChange}
							/>
						)}
					/>
					<Controller
						name="contractValue"
						control={methods.control}
						render={({ field }) => (
							<CurrencyInput
								label="Valor do contrato"
								value={field.value}
								name="contractValue"
								placeholder="R$ 0,00"
								hideErrorMessage={true}
								onChange={field.onChange}
							/>
						)}
					/>
				</div>
				{/* Input antigo para adicionar arquivos */}
				<FileUploadField
					onFileChange={files => {
						handleFileChange(files);
					}}
					label="Arraste e solte um ou mais arquivos PDF ou clique para selecionar"
					existingFiles={existingFiles}
					multiple={true} // Permite múltiplos arquivos
				/>
				{/* Listagem dos arquivos */}
				<div>
					<label className="block mb-2 font-medium">Arquivos adicionados</label>
					<ul className="mb-2">
						{existingFiles.map((file, idx) => (
							<li key={file.id || file.name} className="flex items-center justify-between bg-gray-100 rounded px-2 py-1 mb-1">
								<span className="truncate">{file.name}</span>
								<button
									type="button"
									className="ml-2 text-red-500 hover:underline text-xs"
									onClick={() => handleRemoveExistingFile(idx)}
								>
									<X />
								</button>
							</li>
						))}
						{newFiles.map((file, idx) => (
							<li key={file.name + idx} className="flex items-center justify-between bg-gray-100 rounded px-2 py-1 mb-1">
								<span className="truncate">{file.name}</span>
								<button
									type="button"
									className="ml-2 text-red-500 hover:underline text-xs"
									onClick={() => handleRemoveNewFile(idx)}
								>
									<X />
								</button>
							</li>
						))}
					</ul>
				</div>
			</form>
		</FormProvider>
	);
}
