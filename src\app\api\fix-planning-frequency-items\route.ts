import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";

// Configuração para marcar esta rota como dinâmica
export const dynamic = "force-dynamic";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { proposalId, repairBudgetId, periodicityId } = body;

    if (!proposalId || !repairBudgetId || !periodicityId) {
      return NextResponse.json(
        { error: "proposalId, repairBudgetId e periodicityId são obrigatórios" },
        { status: 400 }
      );
    }

    console.log('Fixing PlanningFrequencyItem:');
    console.log('- proposalId:', proposalId);
    console.log('- repairBudgetId:', repairBudgetId);
    console.log('- periodicityId:', periodicityId);

    // Verificar se o PlanningFrequencyItem existe
    const planningItem = await prisma.planningFrequencyItem.findUnique({
      where: { id: periodicityId },
    });

    if (!planningItem) {
      return NextResponse.json(
        { error: "PlanningFrequencyItem não encontrado" },
        { status: 404 }
      );
    }

    // Verificar se o PlanningFrequencyItem já tem o repairBudgetId definido
    if (planningItem.repairBudgetId === repairBudgetId) {
      console.log(`PlanningFrequencyItem ${periodicityId} already has repairBudgetId: ${repairBudgetId}`);
      return NextResponse.json({
        success: true,
        message: "PlanningFrequencyItem já tem o repairBudgetId definido",
        planningItem,
      });
    }

    // Atualizar o PlanningFrequencyItem para incluir o repairBudgetId
    const updatedPlanningItem = await prisma.planningFrequencyItem.update({
      where: { id: periodicityId },
      data: { repairBudgetId },
    });

    console.log(`Updated PlanningFrequencyItem ${periodicityId} with repairBudgetId: ${repairBudgetId}`);

    // Atualizar a relação no RepairBudget
    await prisma.repairBudget.update({
      where: { id: repairBudgetId },
      data: {
        planningFrequencyItems: {
          connect: { id: periodicityId },
        },
      },
    });

    console.log(`Updated RepairBudget ${repairBudgetId} with PlanningFrequencyItem: ${periodicityId}`);

    return NextResponse.json({
      success: true,
      message: "PlanningFrequencyItem atualizado com sucesso",
      planningItem: updatedPlanningItem,
    });
  } catch (error) {
    console.error("Erro ao atualizar PlanningFrequencyItem:", error);
    return NextResponse.json(
      { error: "Erro ao atualizar PlanningFrequencyItem" },
      { status: 500 }
    );
  }
}
