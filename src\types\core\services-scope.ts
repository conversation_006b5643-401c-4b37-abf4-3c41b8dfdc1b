import { ServiceType } from "../common";
import { ResourceControlInterface } from "../utils";
import { ProposalRelationInterface } from "./proposal";

export interface ServicesScopeInterface {
	name: string;
	description: string;
	types: ServiceType[];
	// productivity: Productivity; create Productivity type
	productivity?: any;
}

export type ServicesScope = ServicesScopeInterface &
	Partial<ProposalRelationInterface> &
	ResourceControlInterface;

export interface ServicesScopeRelationInterface {
	serviceScopeId: string;
	serviceScope: ServicesScope;
}
