import { prisma } from "@/lib/prisma";
import NextAuth from "next-auth";
import Google from "next-auth/providers/google";

export const { handlers, signIn, signOut, auth } = NextAuth({
	providers: [
		Google({
			clientId: process.env.AUTH_GOOGLE_ID,
			clientSecret: process.env.AUTH_GOOGLE_SECRET,
			authorization: {
				params: {
					scope: "openid email profile",
				},
			},
		}),
	],
	callbacks: {
		async signIn({ user, account }) {
			try {
				const existingUser = await prisma.user.findUnique({
					where: { email: user.email! },
				});

				if (!existingUser) {
					await prisma.user.create({
						data: {
							email: user.email!,
							name: user.name!,
							googleId: account?.providerAccountId,
							image: user.image,
						},
					});
				}
				return true;
			} catch (error) {
				console.error(error);
				return false;
			}
		},
		async session({ session }) {
			const email = session.user.email;
			const existingUser = await prisma.user.findUnique({
				where: { email },
			});

			// Add user data to session
			session.user = { ...session.user, ...existingUser };

			// Add membership data to session
			if (existingUser) {
				const membership = await prisma.membership.findFirst({
					where: { 
						userId: existingUser.id,
						enabled: true
					},
					include: {
						organization: true
					}
				});

				if (membership) {
					session.membership = {
						id: membership.id,
						role: membership.role,
						organizationId: membership.organizationId,
						organizationName: membership.organization.name
					};
				}
			}

			return session;
		},
	},
});