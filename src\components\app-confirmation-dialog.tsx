import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from "@src/components/ui/alert-dialog";
import { cn } from "@src/lib/utils";

interface ConfirmationDialogProps
	extends React.ComponentPropsWithoutRef<typeof AlertDialogTrigger> {
	children: React.ReactNode;
	onConfirmCallback: () => void;
	onCancelCallback?: () => void;
	title?: string;
	description?: string;
	dialogCancelClassName?: string;
	dialogActionClassName?: string;
	confirmButtonText?: string;
	open?: boolean;
	onOpenChange?: (open: boolean) => void;
}

export default function AppConfirmationDialog({
	title,
	description,
	children,
	dialogCancelClassName,
	dialogActionClassName,
	confirmButtonText,
	onConfirmCallback,
	open,
	onOpenChange,
	...props
}: ConfirmationDialogProps) {
	return (
		<AlertDialog open={open} onOpenChange={onOpenChange} {...props}>
			<AlertDialogTrigger asChild>{children}</AlertDialogTrigger>
			<AlertDialogContent className="border-destructive/50">
				<AlertDialogHeader>
					<AlertDialogTitle className="text-foreground font-bold text-lg">
						{title || "Atenção"}
					</AlertDialogTitle>
					<AlertDialogDescription className="text-muted-foreground text-base mt-2">
						{description || "Tem certeza que deseja deletar este registro?"}
					</AlertDialogDescription>
				</AlertDialogHeader>
				<AlertDialogFooter>
					<AlertDialogCancel
						className={cn(
							"bg-blue-500 hover:bg-blue-600 text-white hover:text-white",
							dialogCancelClassName
						)}
					>
						Cancelar
					</AlertDialogCancel>
					<AlertDialogAction
						onClick={(e) => {
							e.preventDefault();
							e.stopPropagation();

							// Obter a URL da API salva no localStorage
							if (typeof window !== 'undefined') {
								try {
									// Obter a URL da API salva no localStorage
									const savedApiUrl = localStorage.getItem('lastProposalApiUrl');
									if (savedApiUrl) {
										console.log("AppConfirmationDialog: URL da API obtida do localStorage:", savedApiUrl);

										// Extrair parâmetros da URL da API
										// Adicionar origin apenas se a URL não começar com http ou https
										const fullUrl = savedApiUrl.startsWith('http') ? savedApiUrl : window.location.origin + savedApiUrl;
										const url = new URL(fullUrl);
										console.log("AppConfirmationDialog: URL completa para extração de parâmetros:", fullUrl);

										// Obter a página atual
										const page = url.searchParams.get('page');
										if (page) {
											console.log("AppConfirmationDialog: Página atual antes da exclusão:", page);
											// Não salvar no sessionStorage aqui, será salvo no proposal-list.tsx
										}

										// Obter os filtros de situação
										const situations = url.searchParams.getAll('situation');
										if (situations.length > 0) {
											console.log("AppConfirmationDialog: Filtros de situação antes da exclusão:", situations);
											// Não salvar no sessionStorage aqui, será salvo no proposal-list.tsx
										}

										// Obter o termo de busca
										const search = url.searchParams.get('search');
										if (search) {
											console.log("AppConfirmationDialog: Termo de busca antes da exclusão:", search);
											// Não salvar no sessionStorage aqui, será salvo no proposal-list.tsx
										}

										// Obter o tamanho da página
										const pageSize = url.searchParams.get('pageSize');
										if (pageSize) {
											console.log("AppConfirmationDialog: Tamanho da página antes da exclusão:", pageSize);
											// Não salvar no sessionStorage aqui, será salvo no proposal-list.tsx
										}

										// Não salvar a URL da API no sessionStorage, pois já a temos no localStorage
										// sessionStorage.setItem('lastProposalApiUrl', savedApiUrl);
									}
								} catch (error) {
									console.error("Erro ao extrair parâmetros da URL da API:", error);
								}
							}

							// Executar o callback de confirmação
							onConfirmCallback();
						}}
						className={cn(
							"bg-green-500  hover:bg-green-600 text-white hover:text-white",
							dialogActionClassName
						)}
					>
						{confirmButtonText || "Excluir"}
					</AlertDialogAction>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	);
}
