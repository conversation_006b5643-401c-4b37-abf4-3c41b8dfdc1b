"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card";
import { Switch } from "@/src/components/ui/switch";
import { Badge } from "@/src/components/ui/badge";
import { useToast } from "@/src/hooks/use-toast";
import ContentWrapper from "@/src/components/content-wrapper";
import { Shield, User, FileText } from "lucide-react";
import { UserSearch } from "./components/user-search";
import { ProposalSearch } from "./components/proposal-search";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs";

interface Permission {
  id: string;
  route: string;
  enabled: boolean;
  title?: string;
}

interface UserPermission {
  userId: string;
  route: string;
  enabled: boolean;
}



export default function ManagePermissions() {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<string>("");
  const [userPermissions, setUserPermissions] = useState<UserPermission[]>([]);
  const { toast } = useToast();
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [userRole, setUserRole] = useState<string>("");
  const [tabAtiva, setTabAtiva] = useState("routes");
  const [refreshProposalsKey, setRefreshProposalsKey] = useState(Date.now());
  const [allRoutesLoading, setAllRoutesLoading] = useState(false);

  // Verificar se o usuário é OWNER
  const isOwner = session?.membership?.role === "OWNER";

  // Carregar permissões ao montar o componente
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setInitialLoading(true);
        await fetchPermissions();
      } finally {
        setInitialLoading(false);
      }
    };
    loadInitialData();
  }, []);

  const fetchPermissions = async () => {
    try {
      console.log("Buscando permissões...");
      const response = await fetch("/api/permissions");
      if (!response.ok) {
        throw new Error("Falha ao carregar permissões");
      }
      const data = await response.json();
      console.log("Permissões carregadas:", data);
      setPermissions(data);
    } catch (error) {
      console.error("Erro ao carregar permissões:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar as permissões",
        variant: "destructive",
      });
    }
  };

  const fetchUserPermissions = async (userId: string) => {
    try {
      setLoading(true);
      console.log("Buscando permissões do usuário:", userId);
      const response = await fetch(`/api/users/${userId}/permissions`);
      if (!response.ok) {
        throw new Error("Falha ao carregar permissões do usuário");
      }
      const data = await response.json();
      setUserPermissions(data.permissions);
      setUserRole(data.role || "");
      console.log("Permissões do usuário carregadas:", data);
    } catch (error) {
      console.error("Erro ao carregar permissões do usuário:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar as permissões do usuário",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchProposalPermissions = async (userId: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/users/${userId}/proposal-permissions`);
      if (!response.ok) {
        throw new Error("Falha ao carregar permissões de propostas");
      }
      // Apenas consome a resposta, não faz nada com ela
      await response.json();
    } catch (error) {
      console.error("Erro ao carregar permissões de propostas:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar as permissões de propostas",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePermissionChange = async (route: string, checked: boolean) => {
    if (!selectedUser) return;

    try {
      setLoading(true);
      console.log("Atualizando permissão:", { route, checked, userId: selectedUser });

      const response = await fetch(`/api/users/${selectedUser}/permissions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          route,
          enabled: checked
        }),
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error("Resposta da API:", errorData);
        throw new Error(`Falha ao atualizar permissão: ${errorData}`);
      }

      // Atualizar o estado local
      setUserPermissions(prevPermissions => {
        if (checked) {
          return [...prevPermissions, { userId: selectedUser, route, enabled: true }];
        } else {
          return prevPermissions.filter(p => p.route !== route);
        }
      });

      // Recarregar as permissões do usuário para garantir sincronização
      await fetchUserPermissions(selectedUser);

      toast({
        title: "Sucesso",
        description: `Permissão ${checked ? "adicionada" : "removida"} com sucesso`,
      });
    } catch (error) {
      console.error("Erro ao atualizar permissão:", error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Não foi possível atualizar a permissão",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleProposalPermissionChange = async (proposalId: string, checked: boolean) => {
    if (!selectedUser) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/users/${selectedUser}/proposal-permissions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          proposalId,
          enabled: checked
        }),
      });

      if (!response.ok) {
        throw new Error("Falha ao atualizar permissão da proposta");
      }

      // Recarregar as permissões de propostas
      await fetchProposalPermissions(selectedUser);
      setRefreshProposalsKey(Date.now());

      toast({
        title: "Sucesso",
        description: `Permissão da proposta ${checked ? "adicionada" : "removida"} com sucesso`,
      });
    } catch (error) {
      console.error("Erro ao atualizar permissão da proposta:", error);
      toast({
        title: "Erro",
        description: "Não foi possível atualizar a permissão da proposta",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Buscar permissões do usuário ao trocar de usuário ou tab
  useEffect(() => {
    if (selectedUser) {
      fetchUserPermissions(selectedUser);
      if (tabAtiva === "proposals") {
        fetchProposalPermissions(selectedUser);
      }
    } else {
      setUserPermissions([]);
    }
  }, [selectedUser, tabAtiva]);

  useEffect(() => {
    const handler = () => setRefreshProposalsKey(Date.now());
    window.addEventListener("refreshProposalPermissions", handler);
    return () => window.removeEventListener("refreshProposalPermissions", handler);
  }, []);

  const allRoutesEnabled = permissions.length > 0 && permissions.every(
    (permission) => {
      const userPermission = userPermissions.find(p => p.route === permission.route);
      return userPermission ? userPermission.enabled : false;
    }
  );

  const handleToggleAllRoutes = async (checked: boolean) => {
    if (!selectedUser) return;
    setAllRoutesLoading(true);
    try {
      await fetch(`/api/users/${selectedUser}/permissions`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          routes: permissions.map((permission) => permission.route),
          enabled: checked,
        }),
      });
      await fetchUserPermissions(selectedUser);
      toast({
        title: "Sucesso",
        description: checked
          ? "Todas as permissões de rotas concedidas"
          : "Todas as permissões de rotas removidas",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Não foi possível atualizar todas as permissões de rotas" + error,
        variant: "destructive",
      });
    } finally {
      setAllRoutesLoading(false);
    }
  };

  // Verificar se o usuário tem permissão para acessar esta página
  if (!isOwner) {
    return (
      <ContentWrapper>
        <div className="flex items-center justify-center h-64">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                <Shield className="h-6 w-6 text-red-600" />
              </div>
              <CardTitle className="text-xl font-semibold text-gray-900">
                Acesso Restrito
              </CardTitle>
              <CardDescription>
                Apenas usuários do tipo Administrador Master podem gerenciar permissões de acesso.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </ContentWrapper>
    );
  }

  if (initialLoading) {
    return (
      <ContentWrapper>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Carregando permissões...</p>
          </div>
        </div>
      </ContentWrapper>
    );
  }

  return (
    <ContentWrapper>
      <div className="space-y-6">
        <div className="flex items-center gap-3">
          <Shield className="h-8 w-8 text-green-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Gerenciar Permissões de Acesso
            </h1>
            <p className="text-gray-600">
              Configure quais usuários têm acesso a cada área do sistema
            </p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Permissões por Usuário
            </CardTitle>
            <CardDescription>
              Configure permissões específicas para cada usuário.
              Por padrão, todos os usuários têm acesso a todas as áreas.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4 mb-4">
                <UserSearch onUserChange={setSelectedUser} />
              </div>

              {selectedUser && userRole === "OWNER" && (
                <div className="mb-4 p-3 rounded bg-yellow-50 border border-yellow-200 text-yellow-800 text-sm font-medium flex items-center gap-2">
                  <Shield className="h-4 w-4 text-yellow-600" />
                  Administrador Master – permissões sempre liberadas e não editáveis.
                </div>
              )}

              {selectedUser && (
                <Tabs defaultValue="routes" value={tabAtiva} onValueChange={setTabAtiva} className="w-full">
                  <TabsList className="inline-flex gap-2">
                    <TabsTrigger value="routes" className="flex items-center gap-2 px-3 py-1 text-sm">
                      <Shield className="h-4 w-4" />
                      Rotas
                    </TabsTrigger>
                    <TabsTrigger value="proposals" className="flex items-center gap-2 px-3 py-1 text-sm">
                      <FileText className="h-4 w-4" />
                      Propostas
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="routes" className="mt-4">
                    {loading ? (
                      <div className="flex justify-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
                      </div>
                    ) : (
                      <>
                        <div className="flex items-center gap-4 mb-4">
                          <Switch
                            checked={allRoutesEnabled}
                            onCheckedChange={handleToggleAllRoutes}
                            disabled={loading || allRoutesLoading || userRole === "OWNER"}
                          />
                          <span className="font-medium">Permitir todas as rotas</span>
                          {allRoutesLoading && <span className="text-xs text-gray-500 ml-2">Atualizando...</span>}
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {permissions.map((permission) => {
                            const isOwnerUser = userRole === "OWNER";
                            const userPermission = userPermissions.find(p => p.route === permission.route);
                            const showAsEnabled = isOwnerUser ? true : (userPermission ? userPermission.enabled : false);
                            return (
                              <Card key={permission.id || permission.route} className="flex flex-col justify-between">
                                <CardHeader className="flex flex-row items-center justify-between pb-2">
                                  <div className="flex flex-col">
                                    <div className="font-semibold text-base">
                                      {permission.title || permission.route}
                                    </div>
                                    <div className="text-xs text-gray-500 mt-1">
                                      {permission.route}
                                    </div>
                                  </div>
                                  <Badge variant={showAsEnabled ? "default" : "destructive"}>
                                    {showAsEnabled ? "Permitido" : "Bloqueado"}
                                  </Badge>
                                </CardHeader>
                                <CardContent>
                                  <Switch
                                    checked={showAsEnabled}
                                    onCheckedChange={(checked) => handlePermissionChange(permission.route, checked)}
                                    disabled={isOwnerUser}
                                  />
                                </CardContent>
                              </Card>
                            );
                          })}
                        </div>
                      </>
                    )}
                  </TabsContent>

                  <TabsContent value="proposals" className="mt-4">
                    <div className="space-y-4">
                      <ProposalSearch
                        onProposalPermissionChange={handleProposalPermissionChange}
                        userId={selectedUser}
                        loading={loading}
                        isOwner={userRole === "OWNER"}
                        refreshKey={refreshProposalsKey}
                      />
                    </div>
                  </TabsContent>
                </Tabs>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </ContentWrapper>
  );
}
