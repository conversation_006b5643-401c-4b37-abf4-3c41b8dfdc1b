export interface FileEditorInterface {
	id: string
	filename: string
	mimetype: string
	size: number
	key: string
	bucket: string
	isLocked: boolean
	lockedBy: string[]
	version: any
	createdAt: Date
	updatedAt: Date
	path?: string
}


export enum FileEditorFileType {
	DOCX,
	XLSX
}

export const FileEditorMimetypesMap: Map<FileEditorFileType, string[]> = new Map([
	[FileEditorFileType.DOCX, [
		"application/vnd.openxmlformats-officedocument.wordprocessingml.document"
	]],
	[FileEditorFileType.XLSX, [
		"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
	]]
])
