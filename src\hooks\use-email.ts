import { useState } from 'react';
import { Attachment } from 'nodemailer/lib/mailer';

interface EmailHookState {
  loading: boolean;
  error: string | null;
  success: boolean;
}

interface EmailOptions {
  to: string | string[];
  subject: string;
  html: string;
  text?: string;
  cc?: string | string[];
  bcc?: string | string[];
  attachments?: Attachment[];
  replyTo?: string;
}

interface ContractEmailParams {
  to: string;
  contractName: string;
  contractLink: string;
  customerName: string;
}

interface ProposalEmailParams {
  to: string;
  proposalName: string;
  proposalLink: string;
  customerName: string;
  attachments?: Attachment[];
}

interface NotificationEmailParams {
  to: string;
  title: string;
  message: string;
  actionLink?: string;
  actionText?: string;
}

export function useEmail() {
  const [state, setState] = useState<EmailHookState>({
    loading: false,
    error: null,
    success: false,
  });

  const resetState = () => {
    setState({
      loading: false,
      error: null,
      success: false,
    });
  };

  // Função genérica para enviar requisições de email
  const sendEmailRequest = async (type: string, params: any) => {
    setState({ loading: true, error: null, success: false });

    try {
      const response = await fetch('/api/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          ...params,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Falha ao enviar email');
      }

      setState({ loading: false, error: null, success: true });
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      setState({ loading: false, error: errorMessage, success: false });
      return false;
    }
  };

  // Função para enviar email de teste
  const sendTestEmail = async (to: string) => {
    return sendEmailRequest('test', { to });
  };

  // Função para enviar email de contrato
  const sendContractEmail = async (params: ContractEmailParams) => {
    return sendEmailRequest('contract', params);
  };

  // Função para enviar email de proposta
  const sendProposalEmail = async (params: ProposalEmailParams) => {
    return sendEmailRequest('proposal', params);
  };

  // Função para enviar email de notificação
  const sendNotificationEmail = async (params: NotificationEmailParams) => {
    return sendEmailRequest('notification', params);
  };

  // Função para enviar email personalizado
  const sendCustomEmail = async (params: EmailOptions) => {
    return sendEmailRequest('custom', params);
  };

  return {
    loading: state.loading,
    error: state.error,
    success: state.success,
    resetState,
    sendTestEmail,
    sendContractEmail,
    sendProposalEmail,
    sendNotificationEmail,
    sendCustomEmail,
  };
}
