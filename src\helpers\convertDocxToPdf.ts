import { exec } from "child_process";
import { writeFile, unlink, readFile } from "fs/promises";
import path from "path";
import { randomUUID } from "crypto";

export async function convertDocxToPdfBuffer(docxBuffer: Buffer): Promise<Buffer> {
  const tempDir = process.platform === 'win32' ? process.env.TEMP || 'C:/Temp' : '/tmp';
  const id = randomUUID();
  const docxPath = path.join(tempDir, `${id}.docx`);
  const pdfPath = path.join(tempDir, `${id}.pdf`);

  await writeFile(docxPath, docxBuffer);

  await new Promise((resolve, reject) => {
    const libreOfficeCmd = process.platform === 'win32'
      ? `"C:/Program Files/LibreOffice/program/soffice.exe" --headless --convert-to pdf "${docxPath}" --outdir "${tempDir}"`
      : `libreoffice --headless --convert-to pdf "${docxPath}" --outdir "${tempDir}"`;
    exec(libreOfficeCmd, (err) => {
      if (err) reject(err);
      else resolve(null);
    });
  });

  const pdfBuffer = await readFile(pdfPath);
  await unlink(docxPath);
  await unlink(pdfPath);
  return pdfBuffer;
} 