import { ResourceControlInterface } from "../utils";
import { FileRelationInterface } from "./file";
import { FileEditorInterface } from "./file-editor";
import { ProposalRelationInterface } from "./proposal";

export interface ProposalTemplateInterface extends ResourceControlInterface {
  title: string;
  description: string;
  type: string;
  fileEditorId: string;
  fileEditor?: FileEditorInterface;
}

export interface ProposalTemplateInterface {
  title: string;
  description: string;
  content: string;
  pdf?: string;
}

export interface ProposalTemplateRelationInterface {
  proposalTemplate: ProposalTemplate;
  proposalTemplateId: string;
  fileEditorId: string;
  fileEditor?: FileEditorInterface;
}

export type ProposalTemplate = ProposalTemplateInterface &
  Partial<ProposalRelationInterface> &
  Partial<FileRelationInterface> &
  ResourceControlInterface;
