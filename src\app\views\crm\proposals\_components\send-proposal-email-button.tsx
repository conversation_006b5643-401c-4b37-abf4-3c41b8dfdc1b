"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { useEmail } from "@/src/hooks/use-email";
import { toast } from "@/src/hooks/use-toast";
import { Loader2, Send } from "lucide-react";
import { Proposal } from "@/src/types/core/proposal";
import { Customer } from "@/src/types/core/customer";

interface SendProposalEmailButtonProps {
  proposal: Proposal;
  customer: Customer;
  onSuccess?: () => void;
  variant?: "default" | "outline" | "secondary" | "destructive" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
}

export function SendProposalEmailButton({
  proposal,
  customer,
  onSuccess,
  variant = "default",
  size = "default",
  className,
}: SendProposalEmailButtonProps) {
  const { loading, sendProposalEmail } = useEmail();
  const [isSending, setIsSending] = useState(false);

  const handleSendEmail = async () => {
    if (!customer.email) {
      toast({
        title: "Erro",
        description: "O cliente não possui um email cadastrado.",
        variant: "destructive",
      });
      return;
    }

    setIsSending(true);

    try {
      // Construir o link para a proposta
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || window.location.origin;
      const proposalLink = `${baseUrl}/views/crm/proposals/view/${proposal.id}`;

      // Enviar o email
      const result = await sendProposalEmail({
        to: customer.email,
        proposalName: proposal.name,
        proposalLink,
        customerName: customer.name,
      });

      if (result) {
        toast({
          title: "Sucesso",
          description: "Email enviado com sucesso para o cliente.",
        });
        
        // Chamar o callback de sucesso, se fornecido
        if (onSuccess) {
          onSuccess();
        }
      } else {
        toast({
          title: "Erro",
          description: "Falha ao enviar o email. Tente novamente.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Erro ao enviar email:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao enviar o email.",
        variant: "destructive",
      });
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleSendEmail}
      disabled={loading || isSending || !customer.email}
      className={className}
    >
      {loading || isSending ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Enviando...
        </>
      ) : (
        <>
          <Send className="mr-2 h-4 w-4" />
          Enviar por Email
        </>
      )}
    </Button>
  );
}
