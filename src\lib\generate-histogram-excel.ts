"use client";

/**
 * Função para gerar um arquivo Excel com os dados do histograma
 * @param data Dados do histograma
 * @returns Blob do arquivo Excel
 */
export async function generateHistogramExcel(data: any) {
  // Importar ExcelJS dinamicamente (apenas no cliente)
  const ExcelJS = (await import("exceljs")).default;

  // Criar um novo workbook
  const workbook = new ExcelJS.Workbook();
  workbook.creator = "Ageu Engenharia";
  workbook.lastModifiedBy = "Ageu Engenharia";
  workbook.created = new Date();
  workbook.modified = new Date();

  // Adicionar uma planilha para o histograma
  const sheet = workbook.addWorksheet("Histograma de Evolução");

  // Não adicionar título do relatório no início da planilha

  // Adicionar informações do cliente
  console.log("Dados da proposta recebidos:", data.proposal);
  const customerName = data.proposal.customerName || "Cliente não especificado";
  console.log("Nome do cliente a ser exibido:", customerName);

  const customerRow = sheet.addRow([`Cliente: ${data.proposal.customerName}`]);
  customerRow.font = { bold: true };
  customerRow.alignment = { horizontal: "center" };

  // Adicionar informações da proposta abaixo do cliente
  const titleRow = sheet.addRow([`Proposta: ${data.proposal.name}`]);
  titleRow.font = { bold: true };
  titleRow.alignment = { horizontal: "center" };

  // Adicionar data de geração
  const dateRow = sheet.addRow([
    `Relatório gerado em: ${new Date().toLocaleDateString("pt-BR")}`,
  ]);
  dateRow.alignment = { horizontal: "center" };

  // Adicionar linha em branco para separar os dados da proposta da tabela
  sheet.addRow([]);

  // Configurar as colunas
  // Primeira coluna para as atividades, segunda para o total previsto e terceira para o total realizado
  const columns: any[] = [
    { header: "Histograma de Evolução", key: "activity", width: 40 },
    {
      header: "Total Previsto (%)",
      key: "totalActivity",
      width: 20,
      style: { numFmt: '0.00"%"' },
    },
    {
      header: "Total Realizado (%)",
      key: "totalRealActivity",
      width: 20,
      style: { numFmt: '0.00"%"' },
    },
  ];

  // Agrupar períodos por label para evitar duplicação
  // Vamos adicionar logs para depuração
  console.log("Períodos disponíveis:", data.periods);

  // Criar um Map para agrupar períodos por label
  const uniquePeriodLabels = new Map();

  // Primeiro passo: agrupar períodos por label
  data.periods.forEach((period: any) => {
    const periodLabel = period.label || "Período sem nome";

    if (!uniquePeriodLabels.has(periodLabel)) {
      // Se este label ainda não foi adicionado, criar uma nova entrada
      uniquePeriodLabels.set(periodLabel, {
        label: periodLabel,
        ids: [period.id],
        chronologicalOrder: period.chronologicalOrder || 0,
      });
    } else {
      // Se este label já existe, adicionar o ID à lista de IDs
      uniquePeriodLabels.get(periodLabel).ids.push(period.id);
    }
  });

  // Converter para array e ordenar cronologicamente
  const uniquePeriods = Array.from(uniquePeriodLabels.values()).sort(
    (a, b) => a.chronologicalOrder - b.chronologicalOrder
  );

  console.log("Períodos agrupados:", uniquePeriods);

  // Adicionar colunas para cada período único
  uniquePeriods.forEach((period: any) => {
    console.log("Adicionando período único:", period.label);

    columns.push(
      {
        header: period.label,
        key: `${period.label}_predicted`,
        width: 15,
        style: { numFmt: '0.00"%"' },
      },
      {
        header: period.label,
        key: `${period.label}_real`,
        width: 15,
        style: { numFmt: '0.00"%"' },
      }
    );
  });

  sheet.columns = columns;

  // Estilizar o cabeçalho (agora na linha 6 devido às informações da proposta)
  const headerRowIndex = 6; // Linha 6 (após cliente, proposta, data e linha em branco)
  const headerRow = sheet.getRow(headerRowIndex);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "E8F5E9" }, // Verde claro
  };
  headerRow.alignment = { vertical: "middle", horizontal: "center" };

  // Mesclar células para o título principal (Histograma de Evolução)
  sheet.mergeCells(`A${headerRowIndex}:A${headerRowIndex + 1}`);
  // Definir explicitamente o valor do cabeçalho "Histograma de Evolução"
  headerRow.getCell(1).value = "Histograma de Evolução";

  // Mesclar células para o total previsto da atividade
  sheet.mergeCells(`B${headerRowIndex}:B${headerRowIndex + 1}`);
  // Definir explicitamente o valor do cabeçalho "Total Previsto (%)"
  headerRow.getCell(2).value = "Total Previsto (%)";

  // Mesclar células para o total realizado da atividade
  sheet.mergeCells(`C${headerRowIndex}:C${headerRowIndex + 1}`);
  // Definir explicitamente o valor do cabeçalho "Total Realizado (%)"
  headerRow.getCell(3).value = "Total Realizado (%)";

  // Adicionar uma segunda linha de cabeçalho para agrupar os períodos
  const subHeaderRow = sheet.getRow(headerRowIndex + 1);

  // Estilizar a segunda linha de cabeçalho
  subHeaderRow.font = { bold: true };
  subHeaderRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "F1F8E9" }, // Verde mais claro
  };

  // Para cada período único, mesclar as células do título do período
  let colIndex = 4; // Começar da coluna D (índice 4), já que B é o total previsto e C é o total realizado
  uniquePeriods.forEach((period: any) => {
    // Mesclar as duas colunas (previsto e realizado) para o título do período
    sheet.mergeCells(headerRowIndex, colIndex, headerRowIndex, colIndex + 1);

    // Definir explicitamente o valor do cabeçalho mesclado
    headerRow.getCell(colIndex).value = period.label;

    // Definir os subtítulos na segunda linha
    subHeaderRow.getCell(colIndex).value = "Previsto";
    subHeaderRow.getCell(colIndex + 1).value = "Realizado";

    // Estilizar os subtítulos
    subHeaderRow.getCell(colIndex).font = { bold: true };
    subHeaderRow.getCell(colIndex + 1).font = { bold: true };
    subHeaderRow.getCell(colIndex).alignment = { horizontal: "center" };
    subHeaderRow.getCell(colIndex + 1).alignment = { horizontal: "center" };

    colIndex += 2;
  });

  // Adicionar os dados das atividades
  data.activities.forEach((activity: any) => {
    // Buscar o total da atividade (buildingPercentage) nos dados
    let totalActivity = 0;

    // Tentar encontrar o total da atividade nos dados
    if (activity.buildingPercentage !== undefined) {
      totalActivity = Number(activity.buildingPercentage);
    } else {
      // Se não encontrar diretamente, tentar buscar nos dados originais
      const repairBudget = data.repairBudgets?.find(
        (rb: any) => rb.id === activity.id
      );
      if (repairBudget && repairBudget.buildingPercentage !== undefined) {
        totalActivity = Number(repairBudget.buildingPercentage);
      }
    }

    // Calcular o total realizado para esta atividade
    let totalRealActivity = 0;

    // Somar todos os valores realizados para esta atividade em todos os períodos
    data.measurements.forEach((measurement: any) => {
      if (measurement.activityId === activity.id) {
        totalRealActivity += Number(measurement.realPercentage) || 0;
      }
    });

    const rowData: any = {
      activity: activity.name,
      totalActivity: totalActivity,
      totalRealActivity: totalRealActivity,
    };

    // Adicionar os valores previstos e realizados para cada período único
    uniquePeriods.forEach((uniquePeriod: any) => {
      // Inicializar valores para este período
      let predictedSum = 0;
      let realSum = 0;

      // Para cada ID de período agrupado neste label
      uniquePeriod.ids.forEach((periodId: string) => {
        // Encontrar a medição para esta atividade e período
        const measurement = data.measurements.find(
          (m: any) => m.activityId === activity.id && m.periodId === periodId
        );

        // Somar os valores (se houver medição)
        if (measurement) {
          predictedSum += Number(measurement.predictedPercentage) || 0;
          realSum += Number(measurement.realPercentage) || 0;
        }
      });

      // Adicionar os valores somados para este período
      rowData[`${uniquePeriod.label}_predicted`] = predictedSum;
      rowData[`${uniquePeriod.label}_real`] = realSum;
    });

    // Adicionar a linha à planilha
    sheet.addRow(rowData);
  });

  // Adicionar bordas a todas as células da tabela (começando da linha do cabeçalho)
  for (let i = headerRowIndex; i <= sheet.rowCount; i++) {
    const row = sheet.getRow(i);
    row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
      // Adicionar bordas a todas as células
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };

      // Destacar as colunas de total da atividade (colunas B e C)
      if (colNumber === 2 || colNumber === 3) {
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "F5F5F5" }, // Cinza claro
        };

        // Negrito para os valores de total
        if (i > headerRowIndex + 1) {
          // Pular o cabeçalho
          cell.font = { bold: true };
        }
      }
    });
  }

  // Mesclar células para as informações da proposta no topo
  const lastColumn = String.fromCharCode(65 + columns.length - 1);
  sheet.mergeCells(`A1:${lastColumn}1`); // Nome do cliente
  sheet.mergeCells(`A2:${lastColumn}2`); // Nome do cliente
  sheet.mergeCells(`A3:${lastColumn}3`); // Título da proposta
  sheet.mergeCells(`A4:${lastColumn}4`); // Data de geração

  // Gerar o arquivo como um blob
  const buffer = await workbook.xlsx.writeBuffer();
  return new Blob([buffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });
}
