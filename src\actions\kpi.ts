"use server";

import { replaceFromFileEditorIdAndVariables } from "@/src/helpers/file-editor";
import { prisma } from "@/src/lib/prisma";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

export async function loadKpiData(
  startDate?: Date,
  endDate?: Date,
  serviceType?: string
) {
  try {
    const { organizationId } = await getCurrentOrganization();

    // Construir o filtro de data
    const dateFilter: any = {};
    if (startDate) {
      dateFilter.gte = startDate;
    }
    if (endDate) {
      dateFilter.lte = endDate;
    }

    // Construir o filtro base com organizationId
    const baseFilter: any = {
      customer: {
        organizationId,
      },
    };

    // Adicionar filtro de serviceType se fornecido
    if (serviceType) {
      baseFilter.serviceType = serviceType;
    }

    // LOG: Mostrar todas as propostas da organização antes do groupBy
    const allProposals = await prisma.proposal.findMany({
      where: baseFilter,
    });
    console.log('KPI allProposals:', JSON.stringify(allProposals, null, 2));

    // Filtrar as propostas pelas situações especificadas e agrupar por mês e ano
    const acceptedProposals = await prisma.proposal.groupBy({
      by: ["startDate"],
      where: {
        ...baseFilter,
        situation: {
          in: [
            "PROPOSAL_ACCEPTED",
            "SIGN_REQUESTED",
            "SIGNED",
            "PROJECT_IN_PROGRESS",
            "PROJECT_FINISHED",
          ],
        },
        ...(Object.keys(dateFilter).length > 0
          ? { startDate: dateFilter }
          : {}),
      },
      _sum: {
        budget: true,
        area: true,
      },
      _count: {
        _all: true,
      },
      orderBy: {
        startDate: "asc",
      },
    });

    // Consulta específica para contratos (propostas com status SIGNED, PROJECT_IN_PROGRESS, PROJECT_FINISHED)
    const contractsData = await prisma.proposal.groupBy({
      by: ["startDate"],
      where: {
        ...baseFilter,
        situation: {
          in: ["SIGNED", "PROJECT_IN_PROGRESS", "PROJECT_FINISHED"],
        },
        ...(Object.keys(dateFilter).length > 0
          ? { startDate: dateFilter }
          : {}),
      },
      _count: {
        _all: true,
      },
      orderBy: {
        startDate: "asc",
      },
    });

    // Consulta específica para propostas concluídas (PROJECT_FINISHED)
    const completedProposalsData = await prisma.proposal.groupBy({
      by: ["startDate"],
      where: {
        ...baseFilter,
        situation: "PROJECT_FINISHED",
        ...(Object.keys(dateFilter).length > 0
          ? { startDate: dateFilter }
          : {}),
      },
      _sum: {
        budget: true,
      },
      _count: {
        _all: true,
      },
      orderBy: {
        startDate: "asc",
      },
    });

    // Consulta específica para propostas perdidas (LOST)
    const lostProposals = await prisma.proposal.groupBy({
      by: ["startDate"],
      where: {
        ...baseFilter,
        situation: "LOST",
        ...(Object.keys(dateFilter).length > 0
          ? { startDate: dateFilter }
          : {}),
      },
      _count: {
        _all: true,
      },
      orderBy: {
        startDate: "asc",
      },
    });

    // Agrupar todas as propostas por mês e ano, incluindo a soma total do orçamento
    const monthlyBudgets = await prisma.proposal.groupBy({
      by: ["startDate"],
      where: {
        ...baseFilter,
      },
      _sum: {
        budget: true,
      },
      _count: {
        _all: true,
      },
      orderBy: {
        startDate: "asc",
      },
    });

    // LOG: Mostrar o resultado das queries antes de consolidar os dados
    console.log('KPI acceptedProposals:', JSON.stringify(acceptedProposals, null, 2));
    console.log('KPI contractsData:', JSON.stringify(contractsData, null, 2));
    console.log('KPI completedProposalsData:', JSON.stringify(completedProposalsData, null, 2));
    console.log('KPI lostProposals:', JSON.stringify(lostProposals, null, 2));
    console.log('KPI monthlyBudgets:', JSON.stringify(monthlyBudgets, null, 2));

    // Consolidar os dados por mês e ano
    const consolidatedData: Record<string, any> = {};

    // Inicializar estrutura de dados para os últimos 6 meses
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth(); // 0-11
    const currentYear = currentDate.getFullYear();

    // Inicializar os últimos 6 meses (incluindo o mês atual)
    for (let i = 5; i >= 0; i--) {
      let targetMonth = currentMonth - i;
      let targetYear = currentYear;

      // Ajustar para meses anteriores ao atual ano
      if (targetMonth < 0) {
        targetMonth += 12;
        targetYear -= 1;
      }

      // Converter para formato 1-12
      const month = targetMonth + 1;
      const monthYearKey = `${targetYear}-${month}`;

      consolidatedData[monthYearKey] = {
        month: month,
        year: targetYear,
        totalBudgetAccepted: 0,
        totalBudgetAllProposals: 0,
        totalProposals: 0,
        acceptedProposals: 0,
        lostProposals: 0, // Novo campo para perdidas
        contractsCount: 0,
        totalArea: 0,
        completedProposalsValue: 0, // Valor total das propostas concluídas
      };
    }

    // Processar propostas aceitas
    acceptedProposals.forEach((item) => {
      const date = new Date(item.startDate);
      const monthYearKey = `${date.getFullYear()}-${date.getMonth() + 1}`;

      if (!consolidatedData[monthYearKey]) {
        consolidatedData[monthYearKey] = {
          month: date.getMonth() + 1,
          year: date.getFullYear(),
          totalBudgetAccepted: 0,
          totalBudgetAllProposals: 0,
          totalProposals: 0,
          acceptedProposals: 0,
          lostProposals: 0,
          contractsCount: 0, // Novo campo para contar contratos
          totalArea: 0,
        };
      }

      consolidatedData[monthYearKey].totalBudgetAccepted +=
        Number(item._sum.budget) || 0;
      consolidatedData[monthYearKey].acceptedProposals += item._count._all || 0;
      consolidatedData[monthYearKey].totalArea += Number(item._sum.area) || 0;
    });

    // Processar dados de contratos
    contractsData.forEach((item) => {
      const date = new Date(item.startDate);
      const monthYearKey = `${date.getFullYear()}-${date.getMonth() + 1}`;

      if (!consolidatedData[monthYearKey]) {
        consolidatedData[monthYearKey] = {
          month: date.getMonth() + 1,
          year: date.getFullYear(),
          totalBudgetAccepted: 0,
          totalBudgetAllProposals: 0,
          totalProposals: 0,
          acceptedProposals: 0,
          lostProposals: 0,
          contractsCount: 0,
          totalArea: 0,
          completedProposalsValue: 0, // Novo campo para o valor total de propostas concluídas
        };
      }

      consolidatedData[monthYearKey].contractsCount += item._count._all || 0;
    });

    // Processar dados de propostas concluídas
    completedProposalsData.forEach((item) => {
      const date = new Date(item.startDate);
      const monthYearKey = `${date.getFullYear()}-${date.getMonth() + 1}`;

      if (!consolidatedData[monthYearKey]) {
        consolidatedData[monthYearKey] = {
          month: date.getMonth() + 1,
          year: date.getFullYear(),
          totalBudgetAccepted: 0,
          totalBudgetAllProposals: 0,
          totalProposals: 0,
          acceptedProposals: 0,
          lostProposals: 0,
          contractsCount: 0,
          totalArea: 0,
          completedProposalsValue: 0,
        };
      }

      consolidatedData[monthYearKey].completedProposalsValue +=
        Number(item._sum.budget) || 0;
    });

    // Processar propostas perdidas
    lostProposals.forEach((item) => {
      const date = new Date(item.startDate);
      const monthYearKey = `${date.getFullYear()}-${date.getMonth() + 1}`;

      if (!consolidatedData[monthYearKey]) {
        consolidatedData[monthYearKey] = {
          month: date.getMonth() + 1,
          year: date.getFullYear(),
          totalBudgetAccepted: 0,
          totalBudgetAllProposals: 0,
          totalProposals: 0,
          acceptedProposals: 0,
          lostProposals: 0,
          contractsCount: 0,
          totalArea: 0,
          completedProposalsValue: 0,
        };
      }
      consolidatedData[monthYearKey].lostProposals += item._count._all || 0;
    });

    monthlyBudgets.forEach((item) => {
      const date = new Date(item.startDate);
      const monthYearKey = `${date.getFullYear()}-${date.getMonth() + 1}`;

      if (!consolidatedData[monthYearKey]) {
        consolidatedData[monthYearKey] = {
          month: date.getMonth() + 1,
          year: date.getFullYear(),
          totalBudgetAccepted: 0,
          totalBudgetAllProposals: 0,
          totalProposals: 0,
          acceptedProposals: 0,
          lostProposals: 0,
          totalArea: 0, // Inicialização do campo
        };
      }

      consolidatedData[monthYearKey].totalProposals += item._count._all || 0;
      consolidatedData[monthYearKey].totalBudgetAllProposals +=
        Number(item._sum.budget) || 0;
      // Removemos a atualização do totalArea aqui, pois já está sendo calculado no loop anterior
    });

    // LOG: Mostrar o consolidatedData antes do retorno
    console.log('KPI consolidatedData:', JSON.stringify(consolidatedData, null, 2));

    // Calcular a taxa de conversão e formatar o resultado final
    const result = Object.values(consolidatedData)
      .map((item: any) => {
        // Novo denominador: só propostas ganhas + perdidas
        const denominator = Number(item.acceptedProposals) + Number(item.lostProposals);
        return {
          month: item.month,
          year: item.year,
          totalIncome: Number(item.totalBudgetAccepted) || 0,
          totalBudget: Number(item.totalBudgetAllProposals) || 0,
          totalArea: Number(item.totalArea) || 0,
          acceptedProposals: Number(item.acceptedProposals) || 0,
          lostProposals: Number(item.lostProposals) || 0,
          contractsCount: Number(item.contractsCount) || 0, // Garantir que a contagem de contratos seja sempre um número
          completedProposalsValue: Number(item.completedProposalsValue) || 0, // Valor total das propostas concluídas
          valuePerSquareMeter:
            item.totalArea > 0
              ? (item.totalBudgetAccepted / item.totalArea).toFixed(2)
              : "0.00",
          conversionRate:
            denominator > 0
              ? ((item.acceptedProposals / denominator) * 100).toFixed(2)
              : "0.00",
        };
      })
      // Só considerar meses com pelo menos uma proposta criada (ganha ou perdida)
      .filter((item: any) => (Number(item.acceptedProposals) + Number(item.lostProposals)) > 0)
      .sort((a, b) => {
        // Ordenar por ano e mês para garantir a sequência cronológica
        if (a.year !== b.year) return a.year - b.year;
        return a.month - b.month;
      });

    // LOG: Mostrar o array result antes do retorno
    console.log('KPI result:', JSON.stringify(result, null, 2));

    return formattedKPIDate(result);
  } catch (error) {
    console.error(error);
    throw new Error("Erro ao carregar os dados de KPI.");
  }
}

export async function formattedKPIDate(data: any) {
  try {
    const monthNames = [
      "Jan",
      "Fev",
      "Mar",
      "Abr",
      "Mai",
      "Jun",
      "Jul",
      "Ago",
      "Set",
      "Out",
      "Nov",
      "Dez",
    ];

    const formattedData = data.map((kpi: any) => {
      return {
        ...kpi,
        monthYear: `${monthNames[kpi.month - 1]} / ${kpi.year}`,
      };
    });

    return formattedData;
  } catch (error) {
    console.error(error);
  }
}

export async function loadProposalsByStatus(startDate?: Date, endDate?: Date) {
  try {
    const { organizationId } = await getCurrentOrganization();

    // Construir o filtro de data
    const dateFilter: any = {};
    if (startDate) {
      dateFilter.gte = startDate;
    }
    if (endDate) {
      dateFilter.lte = endDate;
    }

    // Buscar a contagem de propostas por status
    const statusCounts = await prisma.proposal.groupBy({
      by: ["situation"],
      where: {
        customer: {
          organizationId,
        },
        ...(Object.keys(dateFilter).length > 0
          ? { startDate: dateFilter }
          : {}),
      },
      _count: {
        _all: true,
      },
    });

    // Mapear os nomes dos status para exibição
    const statusLabels: Record<string, string> = {
      NEW: "Nova proposta",
      UNDER_ANALYSIS: "Em análise",
      PROPOSAL_SENT: "Proposta enviada",
      PROPOSAL_ACCEPTED: "Proposta aceita",
      SIGN_REQUESTED: "Solicitação de assinatura",
      SIGNED: "Assinado",
      PROJECT_IN_PROGRESS: "Projeto em andamento",
      PROJECT_FINISHED: "Projeto concluído",
      LOST: "Perdido",
    };

    // Formatar os dados para o gráfico
    const formattedData = statusCounts.map((item) => ({
      status: statusLabels[item.situation] || item.situation,
      count: item._count._all,
    }));

    return formattedData;
  } catch (error) {
    console.error(error);
    throw new Error("Erro ao carregar a distribuição de propostas por status.");
  }
}

export async function mergeKpiRepot(reportTemplateId: string) {
  // Mapeamento das variáveis
  const data = await loadKpiData();
  const statusData = await loadProposalsByStatus();

  const variables = {
    items: data,
    statusItems: statusData,
  };

  const reportTemplate = await prisma.reportTemplate.findUnique({
    where: { id: reportTemplateId },
  });

  // Substituir as variáveis no template
  const fileName = `${Date.now()}-kpi-report`;
  const replacedFileEditor = await replaceFromFileEditorIdAndVariables(
    `${reportTemplate?.fileEditorId}`,
    variables,
    fileName
  );

  if (!replacedFileEditor?.id) throw Error("Failed to save file editor");
  const fileEditorId = replacedFileEditor?.id;
  return {
    fileEditorId,
  };
}

interface SelectedReports {
  revenue: boolean;
  squareMeter: boolean;
  conversionRate: boolean;
  contractsCount: boolean;
  statusDistribution: boolean;
}

export async function exportKpiToExcel(
  startDate?: Date,
  endDate?: Date,
  selectedReports?: SelectedReports
) {
  try {
    // Carregar os dados de KPI
    const kpiData = await loadKpiData(startDate, endDate);
    const statusData = await loadProposalsByStatus(startDate, endDate);

    // Formatar os dados para o Excel
    const formattedData = kpiData.map((item) => ({
      "Mês/Ano": `${item.month}/${item.year}`,
      Faturamento: item.totalIncome || 0,
      "Valor por m²": parseFloat(item.valuePerSquareMeter) || 0,
      "Taxa de Conversão": parseFloat(item.conversionRate) || 0,
      Contratos: item.contractsCount || 0, // Garantir que seja 0 quando null
      "Área Total (m²)": item.totalArea || 0,
    }));

    // Formatar os dados de status para o Excel
    const formattedStatusData = statusData.map((item) => ({
      Status: item.status,
      Quantidade: item.count,
      Percentual:
        (item.count / statusData.reduce((sum, s) => sum + s.count, 0)) * 100,
    }));

    // Calcular totais e médias
    const totalRevenue = kpiData.reduce(
      (sum, item) => sum + (item.totalIncome || 0),
      0
    );
    const averageSquareMeterValue = kpiData.length
      ? kpiData.reduce(
          (sum, item) => sum + (parseFloat(item.valuePerSquareMeter) || 0),
          0
        ) / kpiData.length
      : 0;
    const averageConversionRate = kpiData.length
      ? kpiData.reduce(
          (sum, item) => sum + (parseFloat(item.conversionRate) || 0),
          0
        ) / kpiData.length
      : 0;
    const totalContracts = kpiData.reduce(
      (sum, item) => sum + (Number(item.contractsCount) || 0),
      0
    );
    const totalArea = kpiData.reduce(
      (sum, item) => sum + (item.totalArea || 0),
      0
    );

    // Criar um objeto com os dados resumidos
    const summaryData = [
      {
        Indicador: "Faturamento Total",
        Valor: totalRevenue,
      },
      {
        Indicador: "Valor Médio por m²",
        Valor: averageSquareMeterValue,
      },
      {
        Indicador: "Taxa Média de Conversão",
        Valor: averageConversionRate,
      },
      {
        Indicador: "Total de Contratos",
        Valor: totalContracts,
      },
      {
        Indicador: "Área Total",
        Valor: totalArea,
      },
    ];

    // Gerar o arquivo Excel usando xlsx-template
    // Primeiro, criamos um template básico com os dados
    const templateData = {
      summary: summaryData,
      monthlyData: formattedData,
      statusData: formattedStatusData,
      selectedReports,
      dateRange: {
        from: startDate ? startDate.toLocaleDateString("pt-BR") : "Início",
        to: endDate ? endDate.toLocaleDateString("pt-BR") : "Fim",
      },
    };

    // Simular um atraso para mostrar o loading
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // Retornar os dados para serem processados no cliente
    return {
      success: true,
      data: templateData,
    };
  } catch (error) {
    console.error("Erro ao exportar dados para Excel:", error);
    throw new Error("Falha ao exportar dados para Excel.");
  }
}
