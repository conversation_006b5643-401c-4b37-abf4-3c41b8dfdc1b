import { z } from "zod";

export const productivityDataSchema = z.object({
  id: z.string().optional(),
  proposalId: z.string(),
  startDate: z
    .date({ required_error: "Data da medição é obrigatória" })
    .transform((val) => new Date(val)),
  endDate: z
    .date()
    .transform((val) => new Date(val))
    .optional(),
  periodicity: z.string().optional(),
  buildingPercentage: z
    .string()
    .or(z.number())
    .transform((val) => Number(val)),
  predictedPeriodPercentage: z
    .string({ required_error: "Evolução prevista para o período é obrigatória" })
    .or(z.number())
    .transform((val) => Number(val))
    .refine((val) => val >= 0, { message: "Evolução prevista para o período é obrigatória" }),
  realPeriodPercentage: z
    .string({ required_error: "Evolução realizada no período é obrigatória" })
    .or(z.number())
    .transform((val) => Number(val))
    .refine((val) => val >= 0, { message: "Evolução realizada no período é obrigatória" }),
  description: z.string().optional(),
  serviceId: z
    .string()
    .refine((value) => value.trim().length, "Preencha esse campo"),
  toolsQuantity: z
    .string()
    .or(z.number())
    .transform((val) => Number(val)),
  toolsDescription: z.string().optional(),
  workersQuantity: z
    .string()
    .or(z.number())
    .transform((val) => Number(val)),
  repairBudgetId: z.string().optional(),
});

export type ProductivityDataSchema = z.infer<typeof productivityDataSchema>;
