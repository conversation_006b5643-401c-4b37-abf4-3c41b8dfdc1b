"use client"

import * as React from "react"
import { Check, ChevronsUpDown, Search, Loader2, Users } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"

export interface ComboboxOption {
  label: string
  value: string
}

interface ComboboxStyles {
  trigger?: string
  search?: string
  searchIcon?: string
  option?: string
  selectedIcon?: string
  content?: string
  emptyMessage?: string
}

interface ComboboxProps {
  options: ComboboxOption[]
  value: string
  onChange: (value: string) => void
  placeholder?: string
  searchPlaceholder?: string
  emptyMessage?: string
  className?: string
  onSearch?: (search: string) => Promise<ComboboxOption[]>
  disabled?: boolean
  customStyles?: ComboboxStyles
}

export function Combobox({
  options = [],
  value = "",
  onChange,
  placeholder = "Selecione uma opção",
  searchPlaceholder = "Pesquisar...",
  emptyMessage = "Nenhum resultado encontrado.",
  className,
  onSearch,
  disabled = false,
  customStyles = {},
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [searchTerm, setSearchTerm] = React.useState("")
  const [items, setItems] = React.useState<ComboboxOption[]>([])
  const [loading, setLoading] = React.useState(false)
  const searchTimeout = React.useRef<NodeJS.Timeout | null>(null)

  // Inicializa e atualiza os itens quando as opções mudam
  React.useEffect(() => {
    if (Array.isArray(options)) {
      // Verificar se há uma opção "Todos os clientes" (valor vazio)
      const allClientsOption = options.find(opt => opt.value === "" && opt.label.includes("Todos"));

      // Se existir a opção "Todos os clientes", garantir que ela esteja sempre no topo
      if (allClientsOption) {
        const filteredOptions = options.filter(opt => opt.value !== "" || !opt.label.includes("Todos"));
        setItems([allClientsOption, ...filteredOptions]);
      } else {
        setItems(options);
      }
    } else {
      setItems([])
    }
  }, [options])

  // Função para lidar com a pesquisa
  const handleSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const search = e.target.value
    setSearchTerm(search)

    // Limpa o timeout anterior
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current)
    }

    // Se não houver função de pesquisa externa, filtra localmente
    if (!onSearch) {
      if (!Array.isArray(options)) return

      // Filtrar as opções que correspondem à pesquisa
      const filtered = options.filter((option) =>
        option.label.toLowerCase().includes(search.toLowerCase())
      )

      // Verificar se há uma opção "Todos os clientes" (valor vazio)
      const allClientsOption = filtered.find(opt => opt.value === "" && opt.label.includes("Todos"));

      // Se existir a opção "Todos os clientes", garantir que ela esteja sempre no topo
      if (allClientsOption) {
        const filteredOptions = filtered.filter(opt => opt.value !== "" || !opt.label.includes("Todos"));
        setItems([allClientsOption, ...filteredOptions]);
      } else {
        setItems(filtered);
      }
      return
    }

    // Configura um novo timeout para evitar muitas requisições
    searchTimeout.current = setTimeout(async () => {
      if (search.length >= 1) { // Reduzido de 2 para 1 caractere
        setLoading(true)
        try {
          const results = await onSearch(search)
          if (Array.isArray(results)) {
            // Verificar se há uma opção "Todos os clientes" (valor vazio)
            const allClientsOption = results.find(opt => opt.value === "" && opt.label.includes("Todos"));

            // Se existir a opção "Todos os clientes", garantir que ela esteja sempre no topo
            if (allClientsOption) {
              const filteredOptions = results.filter(opt => opt.value !== "" || !opt.label.includes("Todos"));
              setItems([allClientsOption, ...filteredOptions]);
            } else {
              setItems(results);
            }
          } else {
            setItems([])
          }
        } catch (error) {
          console.error("Erro ao pesquisar:", error)
          setItems([])
        } finally {
          setLoading(false)
        }
      } else if (search.length === 0) {
        // Se a pesquisa estiver vazia, restaura as opções originais
        if (Array.isArray(options)) {
          // Verificar se há uma opção "Todos os clientes" (valor vazio)
          const allClientsOption = options.find(opt => opt.value === "" && opt.label.includes("Todos"));

          // Se existir a opção "Todos os clientes", garantir que ela esteja sempre no topo
          if (allClientsOption) {
            const filteredOptions = options.filter(opt => opt.value !== "" || !opt.label.includes("Todos"));
            setItems([allClientsOption, ...filteredOptions]);
          } else {
            setItems(options);
          }
        } else {
          setItems([])
        }
      }
    }, 200) // Reduzido de 300ms para 200ms para busca mais responsiva
  }

  // Encontra a opção selecionada
  const selectedOption = React.useMemo(() => {
    if (!value || !Array.isArray(items)) return null
    return items.find((option) => option.value === value) || null
  }, [items, value])

  // Seleciona uma opção
  const handleSelect = (option: ComboboxOption) => {
    onChange(option.value)
    setOpen(false)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between border-gray-300 bg-white text-left font-normal",
            "hover:bg-gray-50 hover:border-gray-400 transition-all duration-200",
            "focus:ring-2 focus:ring-blue-100 focus:border-blue-300",
            open && "border-blue-300 ring-2 ring-blue-100",
            customStyles.trigger,
            className
          )}
          disabled={disabled}
          onClick={() => setOpen(!open)}
        >
          {selectedOption ? (
            <span className="font-medium text-gray-800">{selectedOption.label}</span>
          ) : (
            <span className="text-gray-500">{placeholder}</span>
          )}
          <ChevronsUpDown className={cn(
            "ml-2 h-4 w-4 shrink-0 transition-transform duration-200",
            open ? (customStyles.selectedIcon || "text-blue-500") + " rotate-180" : "text-gray-400"
          )} />
        </Button>
      </PopoverTrigger>
      <PopoverContent className={cn("w-full min-w-[350px] p-0 overflow-hidden border border-gray-200 shadow-lg rounded-md", customStyles.content)} align="start">
        <div className="flex items-center border-b px-3 py-2 bg-gray-50">
          <Search className={cn("mr-2 h-4 w-4 shrink-0 text-blue-500", customStyles.searchIcon)} />
          <Input
            placeholder={searchPlaceholder}
            value={searchTerm}
            onChange={handleSearch}
            className={cn("flex h-10 w-full rounded-md border-0 bg-transparent py-3 text-sm font-medium outline-none placeholder:text-gray-400 focus:ring-0 disabled:cursor-not-allowed disabled:opacity-50 min-w-[250px]", customStyles.search)}
          />
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-6">
            <Loader2 className={cn("h-6 w-6 animate-spin text-blue-500", customStyles.searchIcon)} />
          </div>
        ) : Array.isArray(items) && items.length > 0 ? (
          <ScrollArea className="max-h-60" maxHeight="240px">
            <div className="p-2">
              {items.map((option, index) => (
                <React.Fragment key={option.value}>
                  {/* Adicionar separador após a opção "Todos os clientes" */}
                  {index === 0 && option.value === "" && option.label.includes("Todos") && items.length > 1 && (
                    <div className="border-b border-green-200 my-2 pb-1 pt-0">
                      <div className="text-xs text-green-600 font-medium px-3 pb-1 flex items-center">
                        <Users className="h-3 w-3 mr-1 text-green-500" />
                        Clientes
                      </div>
                    </div>
                  )}
                  <div
                    className={cn(
                      "relative flex cursor-pointer select-none items-center rounded-md px-3 py-2 text-sm outline-none transition-colors duration-200",
                      // Destacar a opção "Todos os clientes"
                      option.value === "" && option.label.includes("Todos")
                        ? "font-semibold text-green-700 bg-green-50/50"
                        : "",
                      customStyles.option ? undefined : "hover:bg-blue-50 hover:text-blue-700",
                      customStyles.option,
                      value === option.value
                        ? customStyles.option && customStyles.option.includes("green")
                          ? "bg-green-50 text-green-700 font-medium"
                          : "bg-blue-50 text-blue-700 font-medium"
                        : "text-gray-700"
                    )}
                    onClick={() => handleSelect(option)}
                  >
                    {option.value === "" && option.label.includes("Todos") ? (
                      <Users className={cn(
                        "mr-2 h-4 w-4 text-green-600",
                        value === option.value ? "opacity-100" : "opacity-70"
                      )} />
                    ) : (
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4 transition-opacity duration-200",
                          value === option.value ? (customStyles.selectedIcon || "text-blue-500") + " opacity-100" : "opacity-0"
                        )}
                      />
                    )}
                    <span>{option.label}</span>
                  </div>
                </React.Fragment>
              ))}
            </div>
          </ScrollArea>
        ) : (
          <div className="py-8 text-center">
            <Search className={cn("h-10 w-10 mx-auto text-gray-300 mb-2", customStyles.searchIcon)} />
            <p className={cn("text-sm text-gray-500", customStyles.emptyMessage)}>{emptyMessage}</p>
          </div>
        )}
      </PopoverContent>
    </Popover>
  )
}
