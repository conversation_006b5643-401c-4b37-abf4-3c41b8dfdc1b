// types.ts
export interface Cliente {
  id?: number;
  nome: string;
  email: string;
  telefone?: string;
}

export interface Projeto {
  id: number;
  nome: string;
  orcamento: number;
  status:    
   "PRE_PROJETO"
  | "EM_ANALISE"
  | "APROVADO"
  | "AGUAR<PERSON>NDO_ASSINATURA"
  | "AGUAR<PERSON><PERSON>O_INICIO"
  | "EM_ANDAMENTO"
  | "FINALIZADO"
  | "CANCELADO";
  cliente: Cliente;
  dataInicio?: Date;
  ordem?: number;
}

