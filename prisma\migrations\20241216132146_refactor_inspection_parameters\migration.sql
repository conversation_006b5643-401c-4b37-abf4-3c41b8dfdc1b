/*
  Warnings:

  - You are about to drop the column `createdAt` on the `InspectionParameter` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `InspectionParameter` table. All the data in the column will be lost.
  - You are about to drop the column `notes` on the `InspectionParameter` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `InspectionParameter` table. All the data in the column will be lost.
  - You are about to drop the `ServiceInspectionParameter` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_FileToInspectionParameter` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `inspectionDate` to the `InspectionParameter` table without a default value. This is not possible if the table is not empty.
  - Added the required column `observation` to the `InspectionParameter` table without a default value. This is not possible if the table is not empty.
  - Added the required column `proposalId` to the `InspectionParameter` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "ServiceInspectionParameter" DROP CONSTRAINT "ServiceInspectionParameter_inspectionParameterId_fkey";

-- DropForeignKey
ALTER TABLE "_FileToInspectionParameter" DROP CONSTRAINT "_FileToInspectionParameter_A_fkey";

-- DropForeignKey
ALTER TABLE "_FileToInspectionParameter" DROP CONSTRAINT "_FileToInspectionParameter_B_fkey";

-- AlterTable
ALTER TABLE "InspectionParameter" DROP COLUMN "createdAt",
DROP COLUMN "name",
DROP COLUMN "notes",
DROP COLUMN "updatedAt",
ADD COLUMN     "inspectionDate" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "observation" TEXT NOT NULL,
ADD COLUMN     "proposalId" TEXT NOT NULL;

-- DropTable
DROP TABLE "ServiceInspectionParameter";

-- DropTable
DROP TABLE "_FileToInspectionParameter";

-- CreateTable
CREATE TABLE "Photo" (
    "id" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "fileId" TEXT,
    "inspectionParameterId" TEXT,

    CONSTRAINT "Photo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RepairBudget" (
    "id" TEXT NOT NULL,
    "measurementDate" TIMESTAMP(3) NOT NULL,
    "periodicity" TEXT NOT NULL,
    "gravity" INTEGER NOT NULL DEFAULT 0,
    "urgency" INTEGER NOT NULL DEFAULT 0,
    "tendency" INTEGER NOT NULL DEFAULT 0,
    "gut" INTEGER NOT NULL DEFAULT 0,
    "serviceCost" DECIMAL(65,30) NOT NULL DEFAULT 0,
    "totalCost" DECIMAL(65,30) NOT NULL DEFAULT 0,
    "financialWeight" DECIMAL(65,30) NOT NULL DEFAULT 0,
    "igrf" DECIMAL(65,30) NOT NULL DEFAULT 0,
    "proposalId" TEXT NOT NULL,
    "serviceScopeId" TEXT NOT NULL,

    CONSTRAINT "RepairBudget_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "InspectionParameter" ADD CONSTRAINT "InspectionParameter_proposalId_fkey" FOREIGN KEY ("proposalId") REFERENCES "Proposal"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Photo" ADD CONSTRAINT "Photo_fileId_fkey" FOREIGN KEY ("fileId") REFERENCES "File"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Photo" ADD CONSTRAINT "Photo_inspectionParameterId_fkey" FOREIGN KEY ("inspectionParameterId") REFERENCES "InspectionParameter"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RepairBudget" ADD CONSTRAINT "RepairBudget_proposalId_fkey" FOREIGN KEY ("proposalId") REFERENCES "Proposal"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RepairBudget" ADD CONSTRAINT "RepairBudget_serviceScopeId_fkey" FOREIGN KEY ("serviceScopeId") REFERENCES "ServiceScope"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
