import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";
import { parseObject } from "@/src/lib/utils";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const proposalId = searchParams.get("proposalId");
    const page = Number(searchParams.get("page")) || 1;
    const pageSize = Number(searchParams.get("pageSize")) || 10;
    const search = searchParams.get("search") || "";

    if (!proposalId) {
      return NextResponse.json(
        { error: "ProposalId is required" },
        { status: 400 }
      );
    }

    const skip = (page - 1) * pageSize;

    const where = {
      proposalId,
      ...(search
        ? {
            OR: [
              {
                technicalData: { contains: search, mode: "insensitive" as any },
              },
              { observation: { contains: search, mode: "insensitive" as any } },
            ],
          }
        : {}),
    };

    const [total, items] = await Promise.all([
      prisma.inspectionParameter.count({ where }),
      prisma.inspectionParameter.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: { inspectionDate: "desc" },
      }),
    ]);

    const totalPages = Math.ceil(total / pageSize);

    return NextResponse.json({
      items: parseObject(items),
      pagination: {
        page,
        pageSize,
        total,
        totalPages,
      },
    });
  } catch (error) {
    console.error("Error loading inspection parameters:", error);
    return NextResponse.json(
      { error: "Error loading inspection parameters" },
      { status: 500 }
    );
  }
}
