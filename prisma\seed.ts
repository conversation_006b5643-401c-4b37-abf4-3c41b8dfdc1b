import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

async function main() {
  // Criar ou atualizar a organização
  const organization = await prisma.organization.upsert({
    where: { id: 'org_matriz_001' },
    update: {},
    create: {
      id: 'org_matriz_001',
      name: '<PERSON><PERSON>',
    }
  });

  console.log('Organização criada/atualizada:', organization.name);

  const organizationId = organization.id;

  // Criar 20 itens de serviço
  const serviceScopes = [
    { name: 'Aná<PERSON><PERSON> Estrutural', description: 'Avaliação detalhada da integridade estrutural de edificações' },
    { name: 'Projeto Arquitetônico', description: 'Desenvolvimento de projetos arquitetônicos residenciais e comerciais' },
    { name: 'Consultoria em Fundações', description: 'Análise e recomendações para fundações em diferentes tipos de solo' },
    { name: 'Inspeção Predial', description: 'Vistoria técnica para identificação de patologias em edificações' },
    { name: 'Projeto Hidrossanitário', description: 'Elaboração de projetos de instalações hidráulicas e sanitárias' },
    { name: 'Projeto Elétrico', description: 'Desenvolvimento de projetos de instalações elétricas de baixa e média tensão' },
    { name: 'Laudo Técnico', description: 'Elaboração de laudos técnicos para fins judiciais ou administrativos' },
    { name: 'Projeto de Prevenção de Incêndio', description: 'Elaboração de projetos de prevenção e combate a incêndio' },
    { name: 'Avaliação de Imóveis', description: 'Avaliação técnica do valor de mercado de imóveis' },
    { name: 'Gerenciamento de Obras', description: 'Acompanhamento e gestão de obras civis' },
    { name: 'Projeto de Drenagem', description: 'Elaboração de projetos de drenagem pluvial' },
    { name: 'Consultoria em Eficiência Energética', description: 'Análise e recomendações para redução do consumo energético' },
    { name: 'Projeto de Climatização', description: 'Desenvolvimento de projetos de ar condicionado e ventilação' },
    { name: 'Perícia Técnica', description: 'Realização de perícias técnicas em edificações' },
    { name: 'Projeto de Pavimentação', description: 'Elaboração de projetos de pavimentação urbana e rodoviária' },
    { name: 'Consultoria Ambiental', description: 'Assessoria em licenciamento ambiental e estudos de impacto' },
    { name: 'Projeto de Acessibilidade', description: 'Desenvolvimento de projetos de acessibilidade conforme NBR 9050' },
    { name: 'Projeto de Impermeabilização', description: 'Elaboração de projetos de impermeabilização para edificações' },
    { name: 'Consultoria em Patologia das Construções', description: 'Diagnóstico e soluções para problemas construtivos' },
    { name: 'Projeto de Contenção de Encostas', description: 'Elaboração de projetos de contenção de taludes e encostas' }
  ];

  for (const serviceScope of serviceScopes) {
    // Verificar se o serviço já existe
    const existingService = await prisma.serviceScope.findFirst({
      where: {
        name: serviceScope.name,
        organizationId
      }
    });

    if (existingService) {
      // Atualizar serviço existente
      await prisma.serviceScope.update({
        where: { id: existingService.id },
        data: {
          description: serviceScope.description
        }
      });
    } else {
      // Criar novo serviço
      await prisma.serviceScope.create({
        data: {
          id: uuidv4(),
          name: serviceScope.name,
          description: serviceScope.description,
          organizationId,
          types: []
        }
      });
    }
  }

  console.log('Criados 20 itens de serviço');

  // Criar 10 itens de equipamentos
  const equipments = [
    { name: 'Escavadeira Hidráulica', description: 'Escavadeira para movimentação de terra', type: 'EQUIPAMENT', laborType: 'DIRECT' },
    { name: 'Retroescavadeira', description: 'Equipamento para escavação e carregamento', type: 'EQUIPAMENT', laborType: 'DIRECT' },
    { name: 'Betoneira', description: 'Equipamento para mistura de concreto', type: 'EQUIPAMENT', laborType: 'DIRECT' },
    { name: 'Compactador de Solo', description: 'Equipamento para compactação de solo', type: 'EQUIPAMENT', laborType: 'DIRECT' },
    { name: 'Andaime Metálico', description: 'Estrutura para trabalho em altura', type: 'EQUIPAMENT', laborType: 'DIRECT' },
    { name: 'Martelete Demolidor', description: 'Equipamento para demolição de concreto', type: 'EQUIPAMENT', laborType: 'DIRECT' },
    { name: 'Gerador Elétrico', description: 'Equipamento para geração de energia', type: 'EQUIPAMENT', laborType: 'DIRECT' },
    { name: 'Caminhão Basculante', description: 'Veículo para transporte de materiais', type: 'EQUIPAMENT', laborType: 'DIRECT' },
    { name: 'Grua', description: 'Equipamento para içamento de cargas', type: 'EQUIPAMENT', laborType: 'DIRECT' },
    { name: 'Compressor de Ar', description: 'Equipamento para geração de ar comprimido', type: 'EQUIPAMENT', laborType: 'DIRECT' }
  ];

  for (const equipment of equipments) {
    // Verificar se o equipamento já existe
    const existingEquipment = await prisma.labor.findFirst({
      where: {
        name: equipment.name,
        organizationId
      }
    });

    if (existingEquipment) {
      // Atualizar equipamento existente
      await prisma.labor.update({
        where: { id: existingEquipment.id },
        data: {
          description: equipment.description,
          type: equipment.type as any,
          laborType: equipment.laborType as any
        }
      });
    } else {
      // Criar novo equipamento
      await prisma.labor.create({
        data: {
          id: uuidv4(),
          name: equipment.name,
          description: equipment.description,
          type: equipment.type as any,
          laborType: equipment.laborType as any,
          organizationId
        }
      });
    }
  }

  console.log('Criados 10 itens de equipamentos');

  // Criar 10 itens de mão de obra
  const labors = [
    { name: 'Pedreiro', description: 'Profissional para execução de alvenaria', type: 'LABOR', laborType: 'DIRECT' },
    { name: 'Carpinteiro', description: 'Profissional para trabalhos em madeira', type: 'LABOR', laborType: 'DIRECT' },
    { name: 'Eletricista', description: 'Profissional para instalações elétricas', type: 'LABOR', laborType: 'DIRECT' },
    { name: 'Encanador', description: 'Profissional para instalações hidráulicas', type: 'LABOR', laborType: 'DIRECT' },
    { name: 'Pintor', description: 'Profissional para serviços de pintura', type: 'LABOR', laborType: 'DIRECT' },
    { name: 'Armador', description: 'Profissional para montagem de armaduras', type: 'LABOR', laborType: 'DIRECT' },
    { name: 'Servente', description: 'Auxiliar para serviços gerais', type: 'LABOR', laborType: 'DIRECT' },
    { name: 'Gesseiro', description: 'Profissional para trabalhos em gesso', type: 'LABOR', laborType: 'DIRECT' },
    { name: 'Azulejista', description: 'Profissional para assentamento de revestimentos', type: 'LABOR', laborType: 'DIRECT' },
    { name: 'Impermeabilizador', description: 'Profissional para serviços de impermeabilização', type: 'LABOR', laborType: 'DIRECT' }
  ];

  for (const labor of labors) {
    // Verificar se a mão de obra já existe
    const existingLabor = await prisma.labor.findFirst({
      where: {
        name: labor.name,
        organizationId
      }
    });

    if (existingLabor) {
      // Atualizar mão de obra existente
      await prisma.labor.update({
        where: { id: existingLabor.id },
        data: {
          description: labor.description,
          type: labor.type as any,
          laborType: labor.laborType as any
        }
      });
    } else {
      // Criar nova mão de obra
      await prisma.labor.create({
        data: {
          id: uuidv4(),
          name: labor.name,
          description: labor.description,
          type: labor.type as any,
          laborType: labor.laborType as any,
          organizationId
        }
      });
    }
  }

  console.log('Criados 10 itens de mão de obra');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
