// Função para formatar data por extenso
function formatDateLong(date) {
  const parsedDate = new Date(date);
  
  // Verificar se a data é válida
  if (isNaN(parsedDate.getTime())) {
    return "Data inválida";
  }
  
  // Formato por extenso: 22 de novembro de 2023
  const day = parsedDate.getDate();
  const month = parsedDate.toLocaleString("pt-BR", { month: "long" });
  const year = parsedDate.getFullYear();
  return `${day} de ${month} de ${year}`;
}

// Testar a função
const today = new Date();
console.log('Data por extenso:', formatDateLong(today));

// Testar com uma data específica
const specificDate = new Date(2023, 10, 22); // 22 de novembro de 2023 (mês é 0-indexed)
console.log('Data específica por extenso:', formatDateLong(specificDate));
