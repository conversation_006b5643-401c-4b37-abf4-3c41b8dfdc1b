import { Button } from "@/src/components/ui/button";
import { Card } from "@/src/components/ui/card";
import { X } from "lucide-react";
import { useState, useEffect } from "react";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/src/components/ui/alert-dialog";

type CardLaborEquipamentProps = {
    id: string
    title: string;
    description: string;
    nameInput: string;
    removeItem: (id: string) => void;
    type?: "LABOR" | "EQUIPAMENT";
    amount?: number;
    onAmountChange?: (id: string, amount: number) => void;
}

export default function CardLaborEquipament({ id, title, description, nameInput, removeItem, amount = 1, onAmountChange }: CardLaborEquipamentProps) {
    // Usar estado local para controlar o valor do campo
    const [inputValue, setInputValue] = useState<number>(typeof amount === 'number' ? amount : 1);
    // Estado para controlar a abertura do modal de confirmação
    const [isConfirmOpen, setIsConfirmOpen] = useState<boolean>(false);

    // Atualizar o estado quando a prop amount mudar
    useEffect(() => {
        if (typeof amount === 'number' && amount !== inputValue) {
            setInputValue(amount);
            console.log(`Atualizando valor do campo ${nameInput} para ${amount}`);
        }
    }, [amount, nameInput]);
    return (
        <Card className="p-3 w-full hover:shadow-md transition-shadow border-gray-200 hover:border-gray-300">
            {/* Primeira linha: Título */}
            <div className="mb-2">
                <p className="font-semibold truncate">{title}</p>
            </div>

            {/* Segunda linha: Descrição, Input de Quantidade e Botão de Remover */}
            <div className="flex justify-between items-center">
                {/* Descrição à esquerda */}
                <div className="flex-1 mr-2">
                    {description ? (
                        <p className="text-sm text-gray-500 line-clamp-2">{description}</p>
                    ) : (
                        <p className="text-sm text-gray-400 italic">Sem descrição</p>
                    )}
                </div>

                {/* Controles à direita */}
                <div className="flex items-center gap-2 shrink-0">
                    <div className="flex items-center">
                        <label htmlFor={nameInput} className="text-xs text-gray-500 mr-1">Qtd:</label>
                        <input
                            type="number"
                            name={nameInput}
                            min={1}
                            value={inputValue}
                            onChange={(e) => {
                                const newValue = parseInt(e.target.value) || 1;
                                setInputValue(newValue);
                                // Notificar o componente pai sobre a mudança de quantidade
                                if (onAmountChange) {
                                    onAmountChange(id, newValue);
                                }
                                console.log(`Valor alterado para ${newValue} para o item ${id}`);
                            }}
                            className="max-w-[50px] h-7 rounded-md border border-input bg-background px-2 py-0 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
                        />
                    </div>

                    <AlertDialog open={isConfirmOpen} onOpenChange={setIsConfirmOpen}>
                        <AlertDialogTrigger asChild>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="text-red-500 hover:bg-red-50 h-7 w-7 p-0"
                            >
                                <X className="w-4 h-4" />
                            </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="border-destructive/50">
                            <AlertDialogHeader>
                                <AlertDialogTitle className="text-foreground font-bold text-lg">
                                    Excluir {title}
                                </AlertDialogTitle>
                                <AlertDialogDescription className="text-muted-foreground text-base mt-2">
                                    Tem certeza que deseja deletar o item &quot;{title}&quot;? A ação não poderá ser desfeita após clicar em salvar!
                                </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                                <AlertDialogCancel
                                    className="border-destructive/30 hover:border-destructive/50 hover:bg-destructive/5"
                                    onClick={() => setIsConfirmOpen(false)}
                                >
                                    Cancelar
                                </AlertDialogCancel>
                                <AlertDialogAction
                                    className="bg-destructive hover:bg-destructive/90"
                                    onClick={() => {
                                        setIsConfirmOpen(false);
                                        setTimeout(() => {
                                            removeItem(id);
                                        }, 100);
                                    }}
                                >
                                    Excluir
                                </AlertDialogAction>
                            </AlertDialogFooter>
                        </AlertDialogContent>
                    </AlertDialog>
                </div>
            </div>
        </Card>
    );
}
