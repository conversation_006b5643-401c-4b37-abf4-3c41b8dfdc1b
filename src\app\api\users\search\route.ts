import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";
import { auth } from "@/src/providers/auth";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

export const dynamic = "force-dynamic";

export async function GET(request: Request) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new NextResponse("Não autorizado", { status: 401 });
    }

    const { organizationId } = await getCurrentOrganization();

    // Obter o termo de busca da URL
    const { searchParams } = new URL(request.url);
    const search = searchParams.get("search")?.trim();

    // Buscar usuários da organização
    const users = await prisma.user.findMany({
      where: {
        Membership: {
          some: {
            organizationId,
            enabled: true,
          },
        },
        ...(search
          ? {
              OR: [
                { name: { contains: search, mode: "insensitive" } },
                { email: { contains: search, mode: "insensitive" } },
              ],
            }
          : {}),
      },
      select: {
        id: true,
        name: true,
        email: true,
      },
      orderBy: {
        name: "asc",
      },
      take: 20,
    });

    // Formatar os resultados para o combobox
    const formattedUsers = users.map((user) => ({
      label: `${user.name} (${user.email})`,
      value: user.id,
    }));

    return NextResponse.json(formattedUsers);
  } catch (error) {
    console.error("Erro ao buscar usuários:", error);
    return new NextResponse("Erro interno do servidor", { status: 500 });
  }
} 