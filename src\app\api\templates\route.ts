import { NextResponse } from "next/server";
import { loadProposalTemplates } from "@/src/actions/proposal-templates";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const getFilesParam = searchParams.get("getFiles");
    const getFiles = getFilesParam === null ? true : getFilesParam === "true";
    const page = Number(searchParams.get("page")) || 1;
    const pageSize = Number(searchParams.get("pageSize")) || 10;
    const search = searchParams.get("search") || "";
    // Obter o tipo da URL
    const originalType = searchParams.get("type") || "";

    // Verificar se o tipo é válido para o Prisma
    // Se não for, converter para um tipo válido e fazer a filtragem depois
    let type = originalType;
    if (
      type !== "PROPOSAL" &&
      type !== "CONTRACT" &&
      type !== "PROJECT" &&
      type !== ""
    ) {
      console.log(
        `Tipo não suportado pelo Prisma: ${type}. Será filtrado após a consulta.`
      );
      // Para INSPECTION e SUPERVISION, não passar o tipo para o Prisma
      // Faremos a filtragem depois de obter os resultados
      type = "";
    }

    // Passar o tipo original para a função, mas com um parâmetro adicional para indicar o tipo real
    const data = await loadProposalTemplates(
      getFiles,
      page,
      pageSize,
      search,
      type,
      originalType
    );
    return NextResponse.json(data);
  } catch (error) {
    console.log(error);
    return NextResponse.json(
      { error: "Erro ao carregar templates" },
      { status: 500 }
    );
  }
}
