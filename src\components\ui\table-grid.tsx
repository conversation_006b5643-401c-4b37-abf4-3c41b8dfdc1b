"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  ChevronsUp,
  Filter,
  Loader2,
  Search,
} from "lucide-react";
import React, { useCallback, useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./dropdown-menu";

export interface FilterOption {
  label: string;
  value: string;
}

export interface Column<T> {
  key: string;
  header: string;
  cell?: (row: T) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
  filterOptions?: FilterOption[];
  getFilterValue?: (row: T) => string;
  filterType?: 'client' | 'server'; // Indica se o filtro deve ser processado no cliente ou no servidor
}

interface TableGridProps<T> {
  columns: Column<T>[];
  data: T[];
  buttonsTemplate?: React.ReactNode;
  onSearch?: (value: string) => void;
  searchValue?: string;
  loading?: boolean;
  pagination?: {
    page: number;
    pageSize: number;
    totalPages: number;
    total: number;
  };
  onPaginationChange?: (page: number, pageSize?: number) => void;
  searchInputRef?: React.RefObject<HTMLInputElement>;
  onColumnFilter?: (columnKey: string, value: any) => void; // Função para lidar com filtros de colunas no servidor
  activeFilters?: {[key: string]: boolean}; // Estado para rastrear filtros ativos
}

export function TableGrid<T>({
  columns,
  data,
  buttonsTemplate,
  onSearch,
  searchValue = "",
  loading = false,
  pagination,
  onPaginationChange,
  searchInputRef,
  onColumnFilter,
  activeFilters = {},
}: TableGridProps<T>) {
  const [columnFilters, setColumnFilters] = useState<Record<string, string>>({});
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: "asc" | "desc" } | null>(null);

  const handleSearch = (value: string) => {
    console.log("TableGrid.handleSearch chamado com:", value);
    if (onSearch) {
      onSearch(value);
    }
  };

  const handleColumnFilter = (columnKey: string, value: string) => {
    // Verificar se a coluna tem filterType='server'
    const column = columns.find(col => col.key === columnKey);

    if (column?.filterType === 'server' && onColumnFilter) {
      // Se for filtro no servidor, chamar a função de callback
      onColumnFilter(columnKey, value);
    } else {
      // Caso contrário, aplicar filtro no cliente
      setColumnFilters((prev) => ({
        ...prev,
        [columnKey]: value,
      }));
    }
  };

  const handleSort = (key: string) => {
    let direction: "asc" | "desc" = "asc";
    if (sortConfig && sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });
  };

  const sortData = useCallback(
    (data: T[]) => {
      if (!sortConfig) return data;

      return [...data].sort((a: any, b: any) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === "asc" ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === "asc" ? 1 : -1;
        }
        return 0;
      });
    },
    [sortConfig]
  );

  // Aplicar ordenação nos dados
  const displayedData = sortData(data);

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            ref={searchInputRef}
            placeholder="Pesquisar..."
            endIcon={Search}
            value={searchValue}
            className="focus-visible:border-[1px] focus-visible:border-green-500"
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
        {buttonsTemplate && (
          <div className="flex flex-col sm:flex-row gap-2">
            {buttonsTemplate}
          </div>
        )}
      </div>

      <div className="rounded-md border overflow-x-auto max-w-full">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead key={column.key}>
                  <div className="flex items-center gap-2">
                    {column.header}
                    <div className="flex items-center gap-1">
                      {column.sortable && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() => handleSort(column.key)}
                        >
                          <ArrowUpDown className="h-4 w-4" />
                        </Button>
                      )}
                      {column.filterable && column.filterOptions && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className={`h-8 w-8 p-0 ${(activeFilters[column.key] || columnFilters[column.key]) ? 'bg-green-100 text-green-700 hover:bg-green-200 hover:text-green-800' : ''}`}
                            >
                              <Filter className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem
                              onClick={() => handleColumnFilter(column.key, "")}
                            >
                              Todos
                            </DropdownMenuItem>
                            {column.filterOptions.map((option) => (
                              <DropdownMenuItem
                                key={option.value}
                                onClick={() =>
                                  handleColumnFilter(column.key, option.value)
                                }
                              >
                                {option.label}
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  </div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <div className="flex items-center justify-center gap-2">
                    <Loader2 className="h-5 w-5 animate-spin text-green-500" />
                    <span className="text-sm text-gray-500">Carregando...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : displayedData.length > 0 ? (
              displayedData.map((row: any, rowIndex) => (
                <TableRow key={rowIndex}>
                  {columns.map((column) => (
                    <TableCell key={column.key} className="whitespace-nowrap">
                      {column.cell ? column.cell(row) : row[column.key]}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Sem dados
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center gap-3 justify-center mt-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            console.log("TableGrid: Clicou no botão de primeira página");
            onPaginationChange?.(1);
          }}
          disabled={pagination?.page === 1 || loading}
          className="hidden sm:block"
        >
          <ChevronsUp className="rotate-[270deg]" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPaginationChange?.(pagination?.page ? pagination.page - 1 : 1)}
          disabled={pagination?.page === 1 || loading}
        >
          <ChevronLeft />
        </Button>

        <p className="text-sm text-gray-700 font-medium">
          Página {pagination?.page || 1} de {pagination?.totalPages || 1} ({pagination?.total || 0} itens)
        </p>

        <Button
          variant="outline"
          size="sm"
          onClick={() => onPaginationChange?.(pagination?.page ? pagination.page + 1 : 2)}
          disabled={pagination?.page === pagination?.totalPages || loading}
        >
          <ChevronRight />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPaginationChange?.(pagination?.totalPages || 1)}
          disabled={pagination?.page === pagination?.totalPages || loading}
          className="hidden sm:block"
        >
          <ChevronsUp className="rotate-90" />
        </Button>

        <Select
          value={`${pagination?.pageSize || 10}`}
          onValueChange={(value) => {
            const newSize = Number(value);
            onPaginationChange?.(1, newSize); // Passa o novo tamanho da página
          }}
        >
          <SelectTrigger className="h-8 w-[70px]">
            <SelectValue placeholder={pagination?.pageSize || 10} />
          </SelectTrigger>
          <SelectContent side="top">
            {[10, 20, 50].map((size) => (
              <SelectItem key={size} value={size.toString()}>
                {size}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
