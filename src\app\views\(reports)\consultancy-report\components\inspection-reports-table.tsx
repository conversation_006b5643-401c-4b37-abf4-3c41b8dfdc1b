"use client";

import { loadInspectionParameters } from "@/src/actions/inspection-parameters";
import { Button } from "@/src/components/ui/button";
import { TableGrid } from "@/src/components/ui/table-grid";
import { useToast } from "@/src/hooks/use-toast";
import { InspectionParameter } from "@/src/types/core/inspection-paramenters";
import { Eraser } from "lucide-react";
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from "react";

interface InspectionReportsTableProps {
  columns: any[];
  onGenerateReportClick: (inspectionId: string) => void;
  onPageChange?: (page: number) => void;
  proposalId: string;
  buttonsTemplate?: React.ReactNode;
}

export type InspectionReportsTableRef = {
  refresh: (page?: number, searchTerm?: string) => void;
  getTableState: () => {
    page: number;
    pageSize: number;
    search: string;
  };
};

const InspectionReportsTable = forwardRef<InspectionReportsTableRef, InspectionReportsTableProps>(
  function InspectionReportsTable({ columns, onPageChange, proposalId, buttonsTemplate }, ref) {
    const [data, setData] = useState<InspectionParameter[]>([]);
    const [loading, setLoading] = useState(false);
    const [search, setSearch] = useState("");
    const searchInputRef = useRef<HTMLInputElement>(null);
    const { toast } = useToast();
    const [pagination, setPagination] = useState({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });

    const fetchInspectionParameters = useCallback(async (
      page?: number,
      pageSize?: number,
      searchTerm: string = search
    ) => {
      // Usar a página fornecida ou a página atual da paginação, ou 1 como último recurso
      const currentPage = page !== undefined ? page : (pagination.page || 1);

      // Usar o tamanho da página fornecido ou o tamanho da página atual da paginação
      const currentPageSize = pageSize !== undefined ? pageSize : pagination.pageSize;

      try {
        setLoading(true);

        if (!proposalId) {
          setData([]);
          setPagination(prev => ({
            ...prev,
            total: 0,
            totalPages: 0
          }));
          return;
        }

        const result = await loadInspectionParameters(
          proposalId,
          currentPage,
          currentPageSize,
          searchTerm
        );

        if (result) {
          setData(result.items || []);
          setPagination(prev => ({
            ...prev,
            page: currentPage,
            pageSize: currentPageSize,
            total: result.pagination.total,
            totalPages: result.pagination.totalPages
          }));
        }
      } catch (error) {
        console.error('Fetch error:', error);
        toast({
          title: "Erro",
          description: "Erro ao carregar parâmetros de inspeção",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }, [pagination.pageSize, search, proposalId, toast]);

    useImperativeHandle(ref, () => ({
      refresh: (page?: number, searchTerm?: string) => {
        // Se os parâmetros forem fornecidos, use-os; caso contrário, use os valores atuais
        const newSearch = searchTerm !== undefined ? searchTerm : search;

        // Usar a página fornecida, ou a página atual da paginação, ou 1 como último recurso
        const currentPage = page !== undefined ? page : (pagination.page || 1);

        // Atualizar os estados locais se os valores forem diferentes
        if (newSearch !== search) {
          setSearch(newSearch);
        }

        // Buscar inspeções com os novos valores
        fetchInspectionParameters(currentPage, pagination.pageSize, newSearch);
      },
      getTableState: () => ({
        page: pagination.page,
        pageSize: pagination.pageSize,
        search
      })
    }), [fetchInspectionParameters, pagination.page, pagination.pageSize, search]);

    // Carregar dados quando o componente for montado ou quando o proposalId mudar
    useEffect(() => {
      fetchInspectionParameters(1);
    }, [proposalId]);

    const handleSearch = (value: string) => {
      setSearch(value);
      fetchInspectionParameters(1, pagination.pageSize, value);
    };

    const handleClearFilters = () => {
      // Limpar o estado de pesquisa
      setSearch("");

      // Resetar para a primeira página e buscar dados
      fetchInspectionParameters(1, pagination.pageSize, "");

      // Atualizar a página atual no componente pai
      if (onPageChange) onPageChange(1);

      // Focar no campo de pesquisa após limpar
      setTimeout(() => {
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }, 0);
    };

    return (
      <TableGrid
        columns={columns}
        data={data}
        loading={loading}
        pagination={pagination}
        onPaginationChange={(newPage, newPageSize) => {
          // Evitar chamadas duplicadas verificando se a página ou tamanho da página realmente mudaram
          if (newPage !== pagination.page || newPageSize !== pagination.pageSize) {
            fetchInspectionParameters(newPage, newPageSize, search);
            if (onPageChange) onPageChange(newPage);
          }
        }}
        onSearch={handleSearch}
        searchValue={search}
        searchInputRef={searchInputRef}
        buttonsTemplate={
          buttonsTemplate || (
            <>
              <Button
                className="w-full sm:w-auto bg-blue-500 hover:bg-blue-400"
                onClick={handleClearFilters}
              >
                Limpar filtros <Eraser className="ml-2 h-4 w-4" />
              </Button>
            </>
          )
        }
      />
    );
  }
);

export default InspectionReportsTable;
