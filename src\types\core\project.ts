import { ResourceControlInterface } from "../utils";
import { CustomerRelationInterface } from "./customer";
import { FileRelationInterface } from "./file";

export interface ProjectInterface {
  project: string;
  startDate?: Date | string;
  endDate?: Date | string;
  area?: number;
  contractValue?: number;
  serviceType?: string;
}

export type Project = ProjectInterface &
  Partial<CustomerRelationInterface> &
  Partial<FileRelationInterface> &
  ResourceControlInterface;
