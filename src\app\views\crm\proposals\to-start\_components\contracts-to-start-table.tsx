"use client";

import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from "react";
import { TableGrid } from "@/src/components/ui/table-grid";
import { Proposal } from "@/src/types/core/proposal";
import { useToast } from "@/src/hooks/use-toast";
import { Button } from "@/src/components/ui/button";
import { Eraser } from "lucide-react";

interface ContractsToStartTableProps {
  columns: any[];
  onPageChange?: (page: number) => void;
  skipInitialLoad?: boolean;
  freezeTable?: boolean;
  isInitialLoad?: boolean;
}

export type ContractsToStartTableRef = {
  refresh: (page?: number, searchTerm?: string, situationValue?: string | null) => void;
  refreshWithApiUrl: (apiUrl: string) => void;
  getTableState: () => {
    page: number;
    pageSize: number;
    search: string;
    situationFilter: string | null;
  };
};

const ContractsToStartTable = forwardRef<ContractsToStartTableRef, ContractsToStartTableProps>(
  function ContractsToStartTable({ columns, onPageChange, skipInitialLoad = false, freezeTable = false, isInitialLoad = true }, ref) {
    const [data, setData] = useState<Proposal[]>([]);
    const [loading, setLoading] = useState(false);
    const [search, setSearch] = useState("");
    const [situationFilter, setSituationFilter] = useState<string | null>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);
    const { toast } = useToast();
    const [pagination, setPagination] = useState({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });

    // Estado para rastrear filtros ativos
    const [activeFilters, setActiveFilters] = useState<{[key: string]: boolean}>({});

    const fetchProposals = useCallback(async (
      page?: number,
      pageSize: number = pagination.pageSize,
      searchTerm: string = search,
      situationValue: string | null = situationFilter
    ) => {
      // Se a tabela estiver congelada, não fazer nada
      if (freezeTable) {
        console.log("Tabela congelada, ignorando fetchProposals");
        return;
      }

      const currentPage = page || pagination.page;

      try {
        setLoading(true);
        const params = new URLSearchParams({
          page: String(currentPage),
          pageSize: String(pageSize)
        });

        // Adicionar situações base (sempre incluir as situações permitidas)
        if (situationValue) {
          // Se houver um filtro de situação específico, usar apenas ele
          params.append("situation", situationValue);
        } else {
          // Caso contrário, usar todas as situações permitidas
          [ "SIGN_REQUESTED", "SIGNED"].forEach((situation) => {
            params.append("situation", situation);
          });
        }

        // Adicionar tipos de serviço
        [
          "CONSULTANCY",
          "PROJECT",
          "FISCALIZACAO",
          "INSPECAO",
          "GERENCIAMENTO",
          "CONSULTORIA",
        ].forEach((serviceType) => {
          params.append("serviceType", serviceType);
        });

        if (searchTerm) {
          params.append("search", searchTerm);
        }

        // Construir a URL da API
        const apiUrl = `/api/proposals?${params}`;

        // Salvar a URL da API no localStorage para uso posterior (ex: exclusão)
        if (typeof window !== 'undefined') {
          localStorage.setItem('lastContractApiUrl', apiUrl);
          console.log("URL da API salva no localStorage:", apiUrl);
        }

        const response = await fetch(apiUrl);

        if (!response.ok) throw new Error("Failed to fetch proposals");

        const result = await response.json();

        setData(Array.isArray(result.items) ? result.items : []);
        setPagination((prev) => ({
          ...prev,
          page: Number(result.page) || 1,
          pageSize: Number(result.pageSize) || 10,
          total: Number(result.total) || 0,
          totalPages: Math.ceil(
            (Number(result.total) || 0) / (Number(result.pageSize) || 10)
          ),
        }));

      } catch (error) {
        console.error("Fetch error:", error);
        toast({
          title: "Erro",
          description: "Erro ao carregar contratos",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }, [pagination.pageSize, search, situationFilter, toast]);

    useImperativeHandle(ref, () => ({
      refresh: (page?: number, searchTerm?: string, situationValue?: string | null) =>
        fetchProposals(page, pagination.pageSize, searchTerm, situationValue),

      refreshWithApiUrl: (apiUrl: string) => {
        console.log("refreshWithApiUrl chamado com:", apiUrl);

        // Se a tabela estiver congelada, não fazer nada
        if (freezeTable) {
          console.log("Tabela congelada, ignorando refreshWithApiUrl");
          return;
        }

        try {
          // Extrair parâmetros da URL da API
          const url = new URL(apiUrl.startsWith('http') ? apiUrl : `${window.location.origin}${apiUrl}`);

          // Extrair a página da URL da API
          const pageParam = url.searchParams.get('page');
          const pageNum = pageParam ? parseInt(pageParam, 10) : 1;

          // Extrair o tamanho da página da URL da API
          const pageSizeParam = url.searchParams.get('pageSize');
          const pageSizeNum = pageSizeParam ? parseInt(pageSizeParam, 10) : pagination.pageSize;

          // Extrair o termo de busca da URL da API
          const searchTerm = url.searchParams.get('search') || '';

          // Extrair os filtros de situação da URL da API
          const situationParams = url.searchParams.getAll('situation');
          let situationFilter: string | null = null;

          // Se houver apenas um filtro de situação, usá-lo como filtro
          if (situationParams.length === 1) {
            situationFilter = situationParams[0];
          }

          // Atualizar os estados locais
          setSearch(searchTerm);
          setSituationFilter(situationFilter);

          // Atualizar filtros ativos
          setActiveFilters(prev => ({
            ...prev,
            situation: !!situationFilter
          }));

          // Carregar dados com os parâmetros extraídos
          fetchProposals(pageNum, pageSizeNum, searchTerm, situationFilter);
        } catch (error) {
          console.error("Erro ao extrair parâmetros da URL da API:", error);
          fetchProposals(1);
        }
      },

      getTableState: () => {
        // Verificar se há uma URL da API salva no localStorage
        if (typeof window !== 'undefined') {
          const savedApiUrl = localStorage.getItem('lastContractApiUrl');
          if (savedApiUrl) {
            try {
              // Extrair parâmetros da URL da API
              const url = new URL(savedApiUrl.startsWith('http') ? savedApiUrl : `${window.location.origin}${savedApiUrl}`);

              // Extrair a página da URL da API
              const pageParam = url.searchParams.get('page');
              const pageNum = pageParam ? parseInt(pageParam, 10) : 1;

              // Extrair o tamanho da página da URL da API
              const pageSizeParam = url.searchParams.get('pageSize');
              const pageSizeNum = pageSizeParam ? parseInt(pageSizeParam, 10) : pagination.pageSize;

              // Extrair o termo de busca da URL da API
              const searchTerm = url.searchParams.get('search') || '';

              // Extrair os filtros de situação da URL da API
              const situationParams = url.searchParams.getAll('situation');
              let situationValue: string | null = null;

              // Se houver apenas um filtro de situação, usá-lo como filtro
              if (situationParams.length === 1) {
                situationValue = situationParams[0];
              }

              console.log("Estado obtido da URL da API no getTableState:", { page: pageNum, pageSize: pageSizeNum, search: searchTerm, situationFilter: situationValue });

              return {
                page: pageNum,
                pageSize: pageSizeNum,
                search: searchTerm,
                situationFilter: situationValue
              };
            } catch (error) {
              console.error("Erro ao extrair parâmetros da URL da API no getTableState:", error);
            }
          }
        }

        // Se não houver URL da API salva ou houver erro, retornar o estado atual da tabela
        console.log("Estado obtido do estado atual da tabela no getTableState:", { page: pagination.page, pageSize: pagination.pageSize, search, situationFilter });

        return {
          page: pagination.page,
          pageSize: pagination.pageSize,
          search,
          situationFilter
        };
      }
    }), [fetchProposals, pagination.page, pagination.pageSize, search, situationFilter]);

    // Escutar o evento refreshContracts para atualizar a tabela na página correta
    useEffect(() => {
      const handleRefresh = (event: any) => {
        const { page } = event.detail || {};
        if (page) {
          fetchProposals(page);
        }
      };

      window.addEventListener('refreshContracts', handleRefresh);

      return () => {
        window.removeEventListener('refreshContracts', handleRefresh);
      };
    }, [fetchProposals]);

    // Carregar dados quando o componente for montado
    useEffect(() => {
      // Se skipInitialLoad for true ou freezeTable for true, não carregar dados iniciais
      if (skipInitialLoad || freezeTable) {
        console.log("Pulando carregamento inicial devido à flag skipInitialLoad ou freezeTable");
        return;
      }

      // Se for carregamento inicial, carregar a página 1 e limpar a URL da API salva no localStorage
      if (isInitialLoad) {
        console.log("Carregamento inicial, carregando página 1");

        // Limpar a URL da API salva no localStorage
        if (typeof window !== 'undefined') {
          localStorage.removeItem('lastContractApiUrl');
          console.log("URL da API removida do localStorage no carregamento inicial (ContractsToStartTable)");
        }

        fetchProposals(1);
        return;
      }
      // Verificar se há uma URL da API salva no localStorage e se estamos em um processo de exclusão
      if (typeof window !== 'undefined') {
        const savedApiUrl = localStorage.getItem('lastContractApiUrl');
        const isDeleting = localStorage.getItem('isContractDeleting');

        console.log("Verificando flag de exclusão:", { isDeleting, savedApiUrl });

        // Verificar se estamos em um processo de exclusão
        const isInDeletionProcess = isDeleting === 'true';

        // Se não estamos em um processo de exclusão, verificar se há uma URL da API salva
        if (!isInDeletionProcess && savedApiUrl) {
          console.log("Não estamos em um processo de exclusão, mas há uma URL da API salva. Usando-a para o carregamento inicial.");

          try {
            // Extrair parâmetros da URL da API
            const url = new URL(savedApiUrl.startsWith('http') ? savedApiUrl : `${window.location.origin}${savedApiUrl}`);

            // Extrair a página da URL da API
            const pageParam = url.searchParams.get('page');
            const pageNum = pageParam ? parseInt(pageParam, 10) : 1;

            // Extrair o tamanho da página da URL da API
            const pageSizeParam = url.searchParams.get('pageSize');
            const pageSizeNum = pageSizeParam ? parseInt(pageSizeParam, 10) : pagination.pageSize;

            // Extrair o termo de busca da URL da API
            const searchTerm = url.searchParams.get('search') || '';

            // Extrair os filtros de situação da URL da API
            const situationParams = url.searchParams.getAll('situation');
            let situationFilter: string | null = null;

            // Se houver apenas um filtro de situação, usá-lo como filtro
            if (situationParams.length === 1) {
              situationFilter = situationParams[0];
            }

            // Atualizar os estados locais
            setSearch(searchTerm);
            setSituationFilter(situationFilter);

            // Atualizar filtros ativos
            setActiveFilters(prev => ({
              ...prev,
              situation: !!situationFilter
            }));

            // Carregar dados com os parâmetros extraídos
            fetchProposals(pageNum, pageSizeNum, searchTerm, situationFilter);
            return;
          } catch (error) {
            console.error("Erro ao extrair parâmetros da URL da API:", error);
          }
        }

        // Se não há URL da API salva ou houve erro ao extrair parâmetros, carregar com valores padrão
        if (!savedApiUrl || !isInDeletionProcess) {
          // Carregar dados iniciais com valores padrão
          const initialPage = 1;
          const initialSearch = '';
          const initialSituationFilter: string | null = null;

          console.log("Carregamento inicial com valores padrão");

          // Carregar dados iniciais
          fetchProposals(initialPage, pagination.pageSize, initialSearch, initialSituationFilter);
          return;
        }

        // Se estamos em um processo de exclusão e há uma URL da API salva, usar a URL da API
        if (isInDeletionProcess && savedApiUrl) {
          console.log("Carregamento inicial com URL da API salva no localStorage (processo de exclusão):", savedApiUrl);

          // Limpar a flag de exclusão após o carregamento inicial
          // Isso evita que a flag permaneça definida indefinidamente
          setTimeout(() => {
            localStorage.removeItem('isContractDeleting');
            console.log("Flag de exclusão removida após o carregamento inicial");
          }, 2000);

          try {
            // Extrair parâmetros da URL da API
            const fullUrl = savedApiUrl.startsWith('http') ? savedApiUrl : window.location.origin + savedApiUrl;
            const url = new URL(fullUrl);

            // Extrair a página da URL da API
            const pageParam = url.searchParams.get('page');
            const pageNum = pageParam ? parseInt(pageParam, 10) : 1;

            // Extrair o tamanho da página da URL da API
            const pageSizeParam = url.searchParams.get('pageSize');
            const pageSizeNum = pageSizeParam ? parseInt(pageSizeParam, 10) : pagination.pageSize;

            // Extrair o termo de busca da URL da API
            const searchTerm = url.searchParams.get('search') || '';

            // Extrair os filtros de situação da URL da API
            const situationParams = url.searchParams.getAll('situation');
            let situationFilter: string | null = null;

            // Se houver apenas um filtro de situação, usá-lo como filtro
            if (situationParams.length === 1) {
              situationFilter = situationParams[0];
            }

            // Atualizar os estados locais
            setSearch(searchTerm);
            setSituationFilter(situationFilter);

            // Atualizar filtros ativos
            setActiveFilters(prev => ({
              ...prev,
              situation: !!situationFilter
            }));

            // Carregar dados com os parâmetros extraídos
            fetchProposals(pageNum, pageSizeNum, searchTerm, situationFilter);
            return;
          } catch (error) {
            console.error("Erro ao extrair parâmetros da URL da API:", error);

            // Se houver erro ao extrair parâmetros, usar valores iniciais padrão
            const initialPage = 1;
            const initialSearch = '';
            const initialSituationFilter: string | null = null;

            console.log("Carregamento inicial com valores padrão após erro");

            // Carregar dados iniciais
            fetchProposals(initialPage, pagination.pageSize, initialSearch, initialSituationFilter);
          }
        } else if (isInDeletionProcess) {
          // Se estamos em um processo de exclusão mas não há URL da API salva, usar valores iniciais padrão
          const initialPage = 1;
          const initialSearch = '';
          const initialSituationFilter: string | null = null;

          console.log("Carregamento inicial com valores padrão (processo de exclusão sem URL da API)");

          // Limpar a flag de exclusão, pois não há URL da API salva
          localStorage.removeItem('isContractDeleting');
          console.log("Flag de exclusão removida (processo de exclusão sem URL da API)");

          // Carregar dados iniciais
          fetchProposals(initialPage, pagination.pageSize, initialSearch, initialSituationFilter);
        }
      } else {
        // Se não estamos no navegador, usar valores iniciais padrão
        const initialPage = 1;
        const initialSearch = '';
        const initialSituationFilter: string | null = null;

        console.log("Carregamento inicial com valores padrão (não estamos no navegador)");

        // Carregar dados iniciais
        fetchProposals(initialPage, pagination.pageSize, initialSearch, initialSituationFilter);
      }
    }, [fetchProposals, pagination.pageSize, skipInitialLoad, freezeTable, isInitialLoad]);

    const handleSearch = (value: string) => {
      setSearch(value);
      fetchProposals(1, pagination.pageSize, value, situationFilter);
    };

    // Função para lidar com filtros de colunas
    const handleColumnFilter = (columnKey: string, value: any) => {
      if (columnKey === 'situation') {
        // Atualizar o filtro de situação
        setSituationFilter(value);

        // Atualizar filtros ativos
        setActiveFilters(prev => ({
          ...prev,
          situation: !!value
        }));

        // Buscar dados com o novo filtro
        fetchProposals(1, pagination.pageSize, search, value);

        // Atualizar a página atual no componente pai
        if (onPageChange) onPageChange(1);
      }
    };

    const handleClearFilters = () => {
      // Limpar o estado de pesquisa e filtros
      setSearch("");
      setSituationFilter(null);
      setActiveFilters({});

      // Resetar para a primeira página e buscar dados
      fetchProposals(1, pagination.pageSize, "", null);

      // Atualizar a página atual no componente pai
      if (onPageChange) onPageChange(1);

      // Focar no campo de pesquisa após limpar
      setTimeout(() => {
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }, 0);
    };

    return (
      <TableGrid
        columns={columns}
        data={data}
        loading={loading}
        pagination={pagination}
        onPaginationChange={(newPage, newPageSize) => {
          // Se a tabela estiver congelada, não fazer nada
          if (freezeTable) {
            console.log("Tabela congelada, ignorando onPaginationChange");
            return;
          }

          fetchProposals(newPage, newPageSize, search, situationFilter);
          if (onPageChange) onPageChange(newPage);
        }}
        onSearch={handleSearch}
        searchValue={search}
        searchInputRef={searchInputRef}
        onColumnFilter={handleColumnFilter}
        activeFilters={activeFilters}
        buttonsTemplate={
          <>
            <Button
              className="w-full sm:w-auto bg-blue-500 hover:bg-blue-400"
              onClick={handleClearFilters}
            >
              Limpar filtros <Eraser className="ml-2 h-4 w-4" />
            </Button>
          </>
        }
      />
    );
  }
);

export default ContractsToStartTable;
