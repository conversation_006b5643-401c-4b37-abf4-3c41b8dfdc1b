"use client";

import { ChevronRight, type LucideIcon } from "lucide-react";
import { useState, useEffect } from "react";
import { checkRouteAccess } from "@/src/actions/route-permissions";

import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
	SidebarGroup,
	SidebarMenu,
	SidebarMenuAction,
	SidebarMenuButton,
	SidebarMenuItem,
	SidebarMenuSub,
	SidebarMenuSubButton,
	SidebarMenuSubItem,
	useSidebar,
} from "@/components/ui/sidebar";
import { DropdownMenuItem } from "@radix-ui/react-dropdown-menu";
import Link from "next/link";
import { cn } from "../lib/utils";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuTrigger,
} from "./ui/dropdown-menu";

interface Item {
	title: string;
	url: string;
	icon: LucideIcon;
	items?: Item[];
	disableLink?: boolean;
}

interface NavMainProps {
	items: Item[];
	pathname: string;
}

export function NavMain({ items, pathname }: NavMainProps) {
	const { state } = useSidebar();
	const [filteredItems, setFilteredItems] = useState<Item[]>(items);

	const activeClass = "!font-semibold !text-green-500 !bg-green-100";
	const hoverClass = "hover:!text-green-500 hover:!bg-green-100";

	// Função para verificar se o usuário tem acesso a uma rota
	const checkItemAccess = async (item: Item): Promise<boolean> => {
		if (!item.url || item.url === "") return true; // Grupos sem URL sempre visíveis

		try {
			return await checkRouteAccess(item.url);
		} catch (error) {
			console.error("Error checking access for route:", item.url, error);
			return false;
		}
	};

	// Função recursiva para filtrar itens baseado nas permissões
	const filterItemsByPermissions = async (items: Item[]): Promise<Item[]> => {
		const filteredItems: Item[] = [];

		for (const item of items) {
			const hasAccess = await checkItemAccess(item);

			if (hasAccess) {
				const filteredItem = { ...item };

				// Se tem subitens, filtrar recursivamente
				if (item.items && item.items.length > 0) {
					const filteredSubItems = await filterItemsByPermissions(item.items);
					filteredItem.items = filteredSubItems;

					// Se é um grupo e não tem subitens visíveis, não mostrar o grupo
					if (filteredSubItems.length === 0 && (!item.url || item.url === "")) {
						continue;
					}
				}

				filteredItems.push(filteredItem);
			}
		}

		return filteredItems;
	};

	// Filtrar itens quando o componente monta ou quando os itens mudam
	useEffect(() => {
		const filterItems = async () => {
			try {
				const filtered = await filterItemsByPermissions(items);
				setFilteredItems(filtered);
			} catch (error) {
				console.error("Error filtering navigation items:", error);
				// Em caso de erro, mostrar todos os itens (comportamento padrão)
				setFilteredItems(items);
			}
		};

		filterItems();
	}, [items]);

	return (
		<SidebarGroup>
			<SidebarMenu className="flex flex-col gap-3">
				{filteredItems.map((item) => (
					<Collapsible key={item.title} asChild>
						<SidebarMenuItem>
							<SidebarMenuButton
								asChild
								tooltip={item.title}
								className={hoverClass}
							>
								{item.items?.length ? (
									<CollapsibleTrigger>
										<div className={cn(hoverClass, "flex gap-2 items-center")}>
											{state == "expanded" ? (
												<item.icon className="w-4 h-4" />
											) : (
												<DropdownMenu>
													{state == "collapsed" && (
														<DropdownMenuTrigger asChild>
															<item.icon className="w-4 h-4 cursor-pointer" />
														</DropdownMenuTrigger>
													)}

													<DropdownMenuContent className="ml-10 p-1 flex flex-col gap-2">
														{item.items.map((subItem, index) => (
															<DropdownMenuItem key={index}>
																{subItem.disableLink ? (
																	<div className="flex gap-2 items-center p-1 opacity-50 cursor-not-allowed">
																		<subItem.icon className="w-4 h-4" />
																		<span className="!text-[14px]">
																			{subItem.title}
																		</span>
																	</div>
																) : (
																	<Link
																		href={subItem.url}
																		className={cn(
																			"flex gap-2 items-center p-1",
																			hoverClass,
																			pathname == subItem.url && activeClass
																		)}
																		prefetch={true}
																	>
																		<subItem.icon className="w-4 h-4" />
																		<span className="!text-[14px]">
																			{subItem.title}
																		</span>
																	</Link>
																)}
															</DropdownMenuItem>
														))}
													</DropdownMenuContent>
												</DropdownMenu>
											)}

											<span>{state == "expanded" && item.title}</span>
										</div>
									</CollapsibleTrigger>
								) : (
									<Link
										href={item.url}
										className={cn(hoverClass, pathname == item.url && activeClass)}
										prefetch={true}
									>
										<item.icon />
										<span>{item.title}</span>
									</Link>
								)}
							</SidebarMenuButton>
							{item.items?.length && (
								<>
									<CollapsibleTrigger asChild>
										<SidebarMenuAction className="data-[state=open]:rotate-90">
											<ChevronRight />
											<span className="sr-only">Toggle</span>
										</SidebarMenuAction>
									</CollapsibleTrigger>
									<CollapsibleContent>
										<SidebarMenuSub>
											{item.items?.map((subItem) => (
												<SidebarMenuSubItem key={subItem.title}>
													<SidebarMenuSubButton asChild>
														{subItem.disableLink ? (
															<div className="flex items-center gap-2 px-2 py-1 opacity-50 cursor-not-allowed">
																<div>
																	<subItem.icon className="w-4 h-4" />
																</div>
																<span>{subItem.title}</span>
															</div>
														) : (
															<Link
																href={subItem.url}
																className={cn(
																	hoverClass,
																	pathname == subItem.url && activeClass
																)}
																prefetch={true}
															>
																<div className="hover:!text-green-500">
																	<subItem.icon className="w-4 h-4" />
																</div>

																<span
																	className={cn(
																		hoverClass,
																		pathname == subItem.url && activeClass
																	)}
																>
																	{subItem.title}
																</span>
															</Link>
														)}
													</SidebarMenuSubButton>
												</SidebarMenuSubItem>
											))}
										</SidebarMenuSub>
									</CollapsibleContent>
								</>
							)}
						</SidebarMenuItem>
					</Collapsible>
				))}
			</SidebarMenu>
		</SidebarGroup>
	);
}
