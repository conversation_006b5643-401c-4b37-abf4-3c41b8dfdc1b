/* Estilos personalizados para a barra de rolagem */
html, body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* Variável para altura do viewport */
:root {
  --vh: 1vh;
}

/* Classe para usar altura do viewport corrigida */
.viewport-height {
  height: calc(var(--vh, 1vh) * 100);
}

/* Ajustes para dispositivos móveis */
@media (max-width: 768px) {
  html, body {
    height: 100svh;
    min-height: -webkit-fill-available;
    overscroll-behavior: none;
  }

  /* Ajuste para garantir que o conteúdo não seja cortado */
  body::after {
    content: '';
    display: block;
    height: 50px; /* Espaço extra no final da página */
  }
}

/* Barras de rolagem mais finas para sheets e modais */
.thin-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.thin-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  margin: 1px;
}

.thin-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(16, 185, 129, 0.5);
  border-radius: 8px;
  border: 1px solid transparent;
  background-clip: padding-box;
  min-height: 40px;
  min-width: 40px;
}

.thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(16, 185, 129, 0.7);
  border: 1px solid transparent;
  background-clip: padding-box;
}

.thin-scrollbar::-webkit-scrollbar-corner {
  background: rgba(0, 0, 0, 0.03);
  border-radius: 2px;
}

/* Garantir que as barras de rolagem estejam sempre visíveis */
.overflow-scroll {
  overflow: scroll !important;
  scrollbar-width: thin;
}

/* Forçar a exibição das barras de rolagem */
.overflow-x-scroll {
  overflow-x: scroll !important;
  -webkit-overflow-scrolling: touch;
}

.custom-scrollbar::-webkit-scrollbar,
body::-webkit-scrollbar,
html::-webkit-scrollbar,
main::-webkit-scrollbar {
  width: 32px;
  height: 32px;
}

.custom-scrollbar::-webkit-scrollbar-track,
body::-webkit-scrollbar-track,
html::-webkit-scrollbar-track,
main::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.06);
  border-radius: 16px;
  margin: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb,
body::-webkit-scrollbar-thumb,
html::-webkit-scrollbar-thumb,
main::-webkit-scrollbar-thumb {
  background: rgba(16, 185, 129, 0.5); /* Cor verde do tema */
  border-radius: 16px;
  border: 2px solid transparent;
  background-clip: padding-box;
  min-height: 80px; /* Altura mínima para a barra vertical */
  min-width: 80px; /* Largura mínima para a barra horizontal */
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover,
body::-webkit-scrollbar-thumb:hover,
html::-webkit-scrollbar-thumb:hover,
main::-webkit-scrollbar-thumb:hover {
  background: rgba(16, 185, 129, 0.7);
  border: 2px solid transparent;
  background-clip: padding-box;
}

.custom-scrollbar::-webkit-scrollbar-corner,
body::-webkit-scrollbar-corner,
html::-webkit-scrollbar-corner,
main::-webkit-scrollbar-corner {
  background: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
}

/* Animações para os cards */
@keyframes cardEnter {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-enter {
  animation: cardEnter 0.3s ease-out forwards;
}

/* Animação de brilho para cabeçalhos */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

/* Efeito de brilho para os cards */
.card-glow {
  position: relative;
  overflow: hidden;
}

.card-glow::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  transition: transform 0.5s ease-out;
  pointer-events: none;
}

.card-glow:hover::after {
  transform: rotate(30deg) translate(50%, 50%);
}

/* Estilos para o kanban board */
.kanban-container {
  display: flex;
  gap: 1.5rem;
  padding-bottom: 1.5rem;
  overflow-x: auto;
}

/* Estilos específicos para dispositivos móveis */
@media (max-width: 768px) {
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  /* Estilo para o botão de menu */
  [aria-label="Toggle menu"] {
    position: relative;
    z-index: 5;
  }

  [aria-label="Toggle menu"]:hover {
    transform: scale(1.05);
  }

  /* Garantir que elementos importantes sejam sempre visíveis */
  .always-visible {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* Ajustes para a sidebar em dispositivos móveis */
  aside {
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: transform 0.3s ease, margin-left 0.3s ease;
  }

  /* Garante que o sidebar recolhido não sobreponha o conteúdo */
  aside[class*="-ml-80"] {
    transform: translateX(-100%);
    box-shadow: none;
  }

  /* Estilo para o botão de menu móvel */
  button[aria-label="Toggle menu"] {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 60;
    position: relative;
  }

  button[aria-label="Toggle menu"]:hover {
    transform: scale(1.05);
  }

  aside > div:nth-child(3) {
    flex: 1;
    overflow-y: auto;
  }

  aside > div:last-child {
    margin-top: auto;
    background-color: white;
  }

  /* Garantir que o perfil do usuário seja exibido corretamente */
  .dropdown-menu-trigger {
    display: flex !important;
    width: 100% !important;
  }

  /* Ajustes para o avatar */
  .avatar-container {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    width: 100% !important;
  }

  /* Ajustes para o perfil do usuário em dispositivos móveis */
  @media (max-width: 768px) {
    aside > div:last-child {
      padding-bottom: 24px !important;
      margin-bottom: 16px !important;
    }

    /* Garante que o avatar seja visível */
    .h-8.w-8 {
      margin-bottom: 4px !important;
    }

    /* Ajuste para o conteúdo do sidebar em dispositivos móveis */
    aside > div:first-of-type {
      padding-bottom: 80px !important; /* Espaço extra no final do sidebar */
    }

    /* Ajustes para o conteúdo principal em dispositivos móveis */
    main {
      padding-bottom: 70px !important;
    }

    /* Ajustes para os cards e conteúdo em dispositivos móveis */
    section {
      margin-bottom: 16px !important;
    }

    /* Ajuste para o conteúdo dentro dos cards */
    section > div {
      padding-bottom: 32px !important;
    }

    /* Garantir espaço extra no final da página para evitar corte do rodapé */
    .overflow-auto {
      padding-bottom: 40px !important;
    }

    /* Estilos específicos para o formulário de proposta */
    .proposal-form-container {
      padding-bottom: 80px !important;
      margin-bottom: 40px !important;
    }

    /* Garantir que os botões no rodapé do formulário sejam visíveis */
    .proposal-form-container > div:last-child {
      margin-bottom: 60px !important;
    }
  }

  /* Melhorar a visualização de cards em dispositivos móveis */
  .card-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    width: 100%;
  }
}

.kanban-column {
  display: flex;
  flex-direction: column;
  border-radius: 0.75rem;
  min-height: calc(100vh - 200px);
  width: 20rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.kanban-column-header {
  padding: 0.75rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.kanban-column-content {
  flex: 1;
  padding: 0.75rem;
  overflow-y: auto;
}

.kanban-card {
  margin-bottom: 0.75rem;
  transition: transform 0.2s ease;
}

.kanban-card:hover {
  transform: translateY(-2px);
}

.kanban-card.is-dragging {
  transform: rotate(1deg) scale(1.05);
}

/* Estilos para os badges de status */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.status-badge-new {
  background-color: rgba(59, 130, 246, 0.1);
  color: rgb(59, 130, 246);
}

.status-badge-analysis {
  background-color: rgba(139, 92, 246, 0.1);
  color: rgb(139, 92, 246);
}

.status-badge-sent {
  background-color: rgba(79, 70, 229, 0.1);
  color: rgb(79, 70, 229);
}

.status-badge-accepted {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgb(16, 185, 129);
}

.status-badge-sign {
  background-color: rgba(245, 158, 11, 0.1);
  color: rgb(245, 158, 11);
}

.status-badge-signed {
  background-color: rgba(5, 150, 105, 0.1);
  color: rgb(5, 150, 105);
}

.status-badge-in-progress {
  background-color: rgba(245, 158, 11, 0.1);
  color: rgb(217, 119, 6);
}

.status-badge-finished {
  background-color: rgba(20, 184, 166, 0.1);
  color: rgb(20, 184, 166);
}

.status-badge-lost {
  background-color: rgba(239, 68, 68, 0.1);
  color: rgb(239, 68, 68);
}

.status-badge-custom-project {
  background-color: rgba(79, 70, 229, 0.1);
  color: rgb(79, 70, 229);
  border: 1px solid rgba(79, 70, 229, 0.2);
  font-weight: 600;
}

/* Estilo para o filtro de tipo de serviço */
/* Ajuste de altura do MultiSelect para ficar consistente com outros inputs */
.service-type-filter button {
  height: 38px !important;
  min-height: 38px !important;
}

/* Estilos para garantir que o texto nos badges seja branco */
.service-type-filter .bg-green-500,
.service-type-filter .bg-green-600,
.service-type-filter [class*="bg-green-"],
.service-type-filter div[class*="flex-wrap"] > div,
.service-type-filter div[class*="flex-wrap"] > div > *,
.service-type-filter div[class*="flex-wrap"] div {
  color: white !important;
  font-weight: 500;
}

/* Garantir que o ícone X nos badges também seja branco */
.service-type-filter .bg-green-500 svg,
.service-type-filter .bg-green-600 svg,
.service-type-filter [class*="bg-green-"] svg,
.service-type-filter div[class*="flex-wrap"] svg,
.service-type-filter div[class*="flex-wrap"] > div svg {
  color: white !important;
}

/* Estilo específico para os badges no componente MultiSelect */
.service-type-filter [class*="bg-green-"] * {
  color: white !important;
}

/* Estilo para o texto do placeholder no MultiSelect */
.service-type-filter button > div > span.text-sm,
.service-type-filter button span.text-muted-foreground,
.service-type-filter button span.text-sm,
.service-type-filter button div.flex.items-center span {
  color: #15803d !important; /* verde mais escuro para melhor contraste */
  font-weight: 500 !important;
  opacity: 1 !important;
}

/* Estilo para o ícone de seta no MultiSelect */
.service-type-filter button > div > svg,
.service-type-filter button svg.h-4 {
  color: #15803d !important;
}
