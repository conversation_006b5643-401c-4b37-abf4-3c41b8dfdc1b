"use client"

import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { ChartConfig, ChartContainer, ChartLegend, ChartLegendContent, ChartTooltip, ChartTooltipContent } from "@/src/components/ui/chart";
import { Line, LineChart, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>xi<PERSON> } from "recharts";

export default function PrioritizationReportChart() {
    const chartData = [
        {
            date: "01/2024",
            acceptedProposalAmount: 105,
            teamPerformance: 85,
        },
        {
            date: "02/2024",
            acceptedProposalAmount: 110,
            teamPerformance: 97,
        },
        {
            date: "03/2024",
            acceptedProposalAmount: 100,
            teamPerformance: 90,
        },
        {
            date: "04/2024",
            acceptedProposalAmount: 100,
            teamPerformance: 100,
        },
        {
            date: "05/2024",
            acceptedProposalAmount: 80,
            teamPerformance: 105,
        },
        {
            date: "06/2024",
            acceptedProposalAmount: 100,
            teamPerformance: 70,
        },
        {
            date: "07/2024",
            acceptedProposalAmount: 32,
            teamPerformance: 80,
        },
        {
            date: "08/2024",
            acceptedProposalAmount: 105,
            teamPerformance: 100,
        },
        {
            date: "09/2024",
            acceptedProposalAmount: 110,
            teamPerformance: 97,
        },
        {
            date: "10/2024",
            acceptedProposalAmount: 105,
            teamPerformance: 100,
        },
        {
            date: "11/2024",

            acceptedProposalAmount: 105,
            teamPerformance: 85,
        },
        {
            date: "12/2024",
            acceptedProposalAmount: 117,
            teamPerformance: 100,
        },
    ];

    const chartConfig: ChartConfig = {
        acceptedProposalAmount: {
            label: "IGRf",
            color: "rgb(22 163 74)",
        },
        teamPerformance: {
            label: "Custo",
            color: "rgb(37 99 235)",
        },
    };

    return (
        <div className="flex flex-col gap-4 w-full h-full">
            <Button className="bg-green-500 hover:bg-green-600 self-end">
                Baixar relatórios
            </Button>
            <ChartContainer
                config={chartConfig}
                className="min-h-[200px] w-full max-h-full"
            >
                <LineChart data={chartData} >
                    <XAxis
                        dataKey="date"
                        tickLine={false}
                        tickMargin={10}
                        axisLine={false}
                    />
                    <YAxis
                        type="number"
                        tickLine={false}
                        axisLine={false}
                        tickMargin={10}
                    />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <ChartLegend content={<ChartLegendContent />} />

                    <Line
                        type="monotone"
                        dataKey="acceptedProposalAmount"
                        stroke="var(--color-acceptedProposalAmount)"
                    />
                    <Line
                        type="monotone"
                        dataKey="teamPerformance"
                        stroke="var(--color-teamPerformance)"
                    />
                </LineChart >
            </ChartContainer>
        </div>
    )
}