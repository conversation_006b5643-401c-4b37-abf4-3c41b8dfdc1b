import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/src/components/ui/dialog";
import { Button } from "@/src/components/ui/button";
import { getLabortypes } from "@/src/actions/laborType";
import { useForm, FormProvider } from 'react-hook-form';
import { DialogClose } from "@radix-ui/react-dialog";
import { Loader2, Search, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { FormField, FormItem, FormControl } from "@/components/ui/form";

type LaborEquipamentProps = {
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
    type: string;
    setSelectedLabors: (labors: any[]) => void;
};

export default function LaborEquipament({ isOpen, onOpenChange, type, setSelectedLabors }: LaborEquipamentProps) {
    const methods = useForm();
    const { handleSubmit, reset } = methods;
    const [data, setData] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [searchTerm, setSearchTerm] = useState<string>("");

    // Limpar seleções quando o modal é aberto
    useEffect(() => {
        if (isOpen) {
            reset({ laborsEquipament: [] });
        }
    }, [isOpen, reset]);

    useEffect(() => {
        async function fetchData() {
            try {
                setLoading(true);
                const result = await getLabortypes();
                if (result) {
                    setData(result.data);
                }
            } catch (error) {
                console.error("Erro ao buscar os tipos de trabalho:", error);
            } finally {
                setLoading(false);
            }
        }
        fetchData();
    }, []);

    // Função para obter os itens filtrados
    const getFilteredItems = () => {
        // Primeiro filtramos por tipo
        const typeFiltered = data.filter(item => item.type === type);

        // Se não há termo de pesquisa, retornamos todos os itens do tipo
        if (searchTerm.trim() === "") {
            return typeFiltered;
        }

        // Se há termo de pesquisa, filtramos por nome ou descrição
        const term = searchTerm.toLowerCase();
        return typeFiltered.filter(item =>
            item.name.toLowerCase().includes(term) ||
            (item.description && item.description.toLowerCase().includes(term))
        );
    };

    const handleSave = (formData: any) => {
        if (formData.laborsEquipament && formData.laborsEquipament.length > 0) {
            const selectedItems = formData.laborsEquipament.map((laborId: string) => {
                const labor = getFilteredItems().find(item => item.id === laborId);

                // Verificar se o labor foi encontrado e tem as propriedades necessárias
                if (!labor || !labor.name) {
                    console.error(`Labor com ID ${laborId} não encontrado ou inválido`);
                    return null;
                }

                return {
                    laborId,
                    labor: {
                        id: labor.id,
                        name: labor.name,
                        description: labor.description || "",
                        type: labor.type
                    },
                    amount: Number(1) // Definir quantidade inicial como 1 (forçando tipo número)
                };
            }).filter(Boolean); // Remover itens nulos

            setSelectedLabors(selectedItems);
        }
        onOpenChange(false);
    };

    return (
        <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        {type === "LABOR" ? (
                            <>

                                <span>Adicionar mão de obra</span>
                            </>
                        ) : (
                            <>

                                <span>Adicionar equipamentos</span>
                            </>
                        )}
                    </DialogTitle>
                </DialogHeader>
                <FormProvider {...methods}>
                    <form onSubmit={handleSubmit(handleSave)}>
                        <div className="space-y-4">
                            {/* Barra de pesquisa */}
                            <div className="mb-2">
                                <div className="relative">
                                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        placeholder="Pesquisar..."
                                        className="pl-8"
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        disabled={loading}
                                    />
                                    {searchTerm && (
                                        <button
                                            className="absolute right-2 top-2.5"
                                            onClick={() => setSearchTerm("")}
                                            type="button"
                                        >
                                            <X className="h-4 w-4 text-muted-foreground" />
                                        </button>
                                    )}
                                </div>
                            </div>

                            {/* Conteúdo principal */}
                            {loading ? (
                                <div className="flex flex-col items-center justify-center py-8">
                                    <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                                    <p className="mt-2 text-sm text-gray-500">Carregando itens...</p>
                                </div>
                            ) : getFilteredItems().length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-8">
                                    <p className="text-sm text-gray-500">Nenhum item encontrado.</p>
                                </div>
                            ) : (
                                <div className="max-h-[300px] overflow-y-auto pr-2">
                                    <div className="grid grid-cols-1 gap-2 border rounded-md p-3 bg-gray-50/50">
                                        {getFilteredItems().map((labor: any) => (
                                            <FormField
                                                key={labor.id}
                                                name="laborsEquipament"
                                                render={({ field }) => {
                                                    return (
                                                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 p-2 hover:bg-white rounded-md transition-colors border border-transparent hover:border-gray-200 shadow-sm">
                                                            <FormControl>
                                                                <div className="flex items-start space-x-2">
                                                                    <Checkbox
                                                                        id={`laborsEquipament-${labor.id}`}
                                                                        checked={field.value?.includes(labor.id)}
                                                                        onCheckedChange={(checked) => {
                                                                            const updatedValue = checked
                                                                                ? [...(field.value || []), labor.id]
                                                                                : (field.value || []).filter(
                                                                                    (value: string) => value !== labor.id
                                                                                );
                                                                            field.onChange(updatedValue);
                                                                        }}
                                                                    />
                                                                    <div className="grid gap-1.5 leading-none">
                                                                        <div className="flex items-center">
                                                                            <Label
                                                                                htmlFor={`laborsEquipament-${labor.id}`}
                                                                                className="font-medium cursor-pointer hover:text-green-500 text-sm"
                                                                            >
                                                                                {labor.name}
                                                                            </Label>
                                                                        </div>
                                                                        {labor.description && (
                                                                            <p className="text-xs text-gray-500 ml-6 line-clamp-2">
                                                                                {labor.description}
                                                                            </p>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            </FormControl>
                                                        </FormItem>
                                                    );
                                                }}
                                            />
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                        <DialogFooter className="mt-4">
                            <DialogClose asChild>
                                <Button variant="outline" onClick={() => onOpenChange(false)}>
                                    Cancelar
                                </Button>
                            </DialogClose>
                            <Button
                                type="submit"
                                className="bg-green-500 hover:bg-green-400 min-w-[100px]"
                                disabled={loading || getFilteredItems().length === 0}
                            >
                                {loading ? "Carregando..." : "Adicionar"}
                            </Button>
                        </DialogFooter>
                    </form>
                </FormProvider>
            </DialogContent>
        </Dialog>
    );
}
