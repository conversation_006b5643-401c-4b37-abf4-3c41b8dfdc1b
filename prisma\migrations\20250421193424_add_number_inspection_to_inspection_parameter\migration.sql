-- DropForeignKey
ALTER TABLE "LaborEquipmentAmount" DROP CONSTRAINT "LaborEquipmentAmount_repairBudgetId_fkey";

-- AlterTable
ALTER TABLE "File" ADD COLUMN     "thumbnailPath" TEXT;

-- AlterTable
ALTER TABLE "InspectionParameter" ADD COLUMN     "numberInspection" INTEGER;

-- AlterTable
ALTER TABLE "LaborEquipmentAmount" ADD COLUMN     "inspectionParameterId" TEXT,
ALTER COLUMN "repairBudgetId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "Project" ALTER COLUMN "project" DROP NOT NULL;

-- AlterTable
ALTER TABLE "RepairBudget" ADD COLUMN     "endDate" TIMESTAMP(3),
ADD COLUMN     "startDate" TIMESTAMP(3);

-- AddForeignKey
ALTER TABLE "LaborEquipmentAmount" ADD CONSTRAINT "LaborEquipmentAmount_inspectionParameterId_fkey" FOREIGN KEY ("inspectionParameterId") REFERENCES "InspectionParameter"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LaborEquipmentAmount" ADD CONSTRAINT "LaborEquipmentAmount_repairBudgetId_fkey" FOREIGN KEY ("repairBudgetId") REFERENCES "RepairBudget"("id") ON DELETE SET NULL ON UPDATE CASCADE;
