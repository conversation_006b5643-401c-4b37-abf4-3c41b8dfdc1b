import { CloudUpload } from "lucide-react";
import { ChangeEvent, useState, useCallback } from "react";

interface FileUploadFieldProps {
  onFileChange: (files: File[] | null) => void;
  /**
   * Tipos de arquivo aceitos. Exemplos:
   * - ".pdf,.doc,.docx" (extensões específicas)
   * - "image/*" (todas as imagens)
   * - "video/*" (todos os vídeos)
   * - "image/png,image/jpeg" (tipos MIME específicos)
   * - Se não especificado, aceita qualquer tipo
   */
  accept?: string;
  label?: string;
  value?: File[] | null;
  className?: string;
  existingFiles?: {
    id?: string;
    name?: string;
  }[];
  multiple?: boolean;
}

export default function FileUploadField({
  onFileChange,
  accept,
  // value,
  className = "",
  // existingFiles = [],
  multiple = false,
}: FileUploadFieldProps) {
  // const [files, setFiles] = useState<File[]>(value || []);
  const [isDragging, setIsDragging] = useState(false);
  // const [hasExistingFiles, setHasExistingFiles] = useState(existingFiles.length > 0);

  // Atualizar o estado quando o existingFiles mudar
  // useEffect(() => {
  //   setHasExistingFiles(existingFiles.length > 0);
  // }, [existingFiles]);

  const handleDrop = useCallback(
    (event: React.DragEvent<HTMLLabelElement>) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(false);

      if (event.dataTransfer.files.length > 0) {
        // Permite múltiplos arquivos se multiple=true
        const droppedFiles = multiple
          ? Array.from(event.dataTransfer.files)
          : [event.dataTransfer.files[0]];
        onFileChange(droppedFiles);
      }
    },
    [onFileChange, multiple]
  );

  const handleDragOver = useCallback((event: React.DragEvent<HTMLLabelElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((event: React.DragEvent<HTMLLabelElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files
      ? multiple
        ? Array.from(e.target.files)
        : [e.target.files[0]]
      : [];
    onFileChange(selectedFiles.length > 0 ? selectedFiles : null);
  };

  // Mantém apenas o retorno solicitado, ignora os comentários
  const renderContent = () => {
    return (
      <div className="w-full flex flex-col items-center gap-2">
        <CloudUpload className="size-8" />
        {/* {existingFiles.map((file, idx) => (
          <div key={file.id || idx} className="flex items-center gap-2">
            <span className="font-medium text-base">
              Arquivo atual: {file.name}
            </span>
          </div>
        ))} */}
        <p className="text-sm text-gray-500 mt-1 text-center">
          Clique ou arraste {multiple ? "um ou mais arquivos" : "um arquivo"} para substituir todos os atuais
        </p>
      </div>
    );
  };

  return (
    <div className={className}>
      <label
        htmlFor="file-upload"
        className={`w-full p-4 hover:bg-gray-50 border-dashed border-2 ${isDragging ? "border-blue-500 bg-blue-50" : "border-gray-300"
          } text-blue-500 flex flex-col items-center justify-center cursor-pointer transition-colors`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        {renderContent()}
      </label>

      <input
        type="file"
        name="file-upload"
        id="file-upload"
        accept={accept}
        autoComplete="off"
        className="hidden"
        onChange={handleFileChange}
        multiple={multiple}
      />
    </div>
  );
}