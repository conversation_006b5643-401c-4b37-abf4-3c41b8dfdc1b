/*
  Warnings:

  - The values [PROJECT_REALIZATION] on the enum `ProposalSituation` will be removed. If these variants are still used in the database, this will fail.
  - Added the required column `area` to the `Proposal` table without a default value. This is not possible if the table is not empty.
  - Changed the type of `paymentCondition` on the `Proposal` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "PaymentCondition" AS ENUM ('CASH', 'INSTALLMENTS');

-- AlterEnum
BEGIN;
CREATE TYPE "ProposalSituation_new" AS ENUM ('NEW', 'UNDER_ANALYSIS', 'PROPOSAL_SENT', 'PROPOSAL_ACCEPTED', 'SIGN_REQUESTED', 'SIGNED', 'PROJECT_IN_PROGRESS', 'PROJECT_FINISHED', 'LOST');
ALTER TABLE "Proposal" ALTER COLUMN "situation" DROP DEFAULT;
ALTER TABLE "Proposal" ALTER COLUMN "situation" TYPE "ProposalSituation_new" USING ("situation"::text::"ProposalSituation_new");
ALTER TYPE "ProposalSituation" RENAME TO "ProposalSituation_old";
ALTER TYPE "ProposalSituation_new" RENAME TO "ProposalSituation";
DROP TYPE "ProposalSituation_old";
ALTER TABLE "Proposal" ALTER COLUMN "situation" SET DEFAULT 'NEW';
COMMIT;

-- AlterTable
ALTER TABLE "Proposal" ADD COLUMN     "area" DECIMAL(65,30) NOT NULL,
ADD COLUMN     "downPayment" DECIMAL(65,30),
ADD COLUMN     "installmentAmount" DECIMAL(65,30),
ADD COLUMN     "installmentNumber" INTEGER,
DROP COLUMN "paymentCondition",
ADD COLUMN     "paymentCondition" "PaymentCondition" NOT NULL;

-- CreateTable
CREATE TABLE "InspectionParameter" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "notes" TEXT NOT NULL,
    "technicalData" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InspectionParameter_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ServiceInspectionParameter" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "inspectionDate" TIMESTAMP(3) NOT NULL,
    "periodicity" "Periodicity" NOT NULL,
    "gravity" INTEGER NOT NULL DEFAULT 0,
    "urgency" INTEGER NOT NULL DEFAULT 0,
    "tendency" INTEGER NOT NULL DEFAULT 0,
    "gut" INTEGER NOT NULL DEFAULT 0,
    "serviceCost" DECIMAL(65,30) NOT NULL DEFAULT 0,
    "totalCost" DECIMAL(65,30) NOT NULL DEFAULT 0,
    "financialWeight" DECIMAL(65,30) NOT NULL DEFAULT 0,
    "igrf" DECIMAL(65,30) NOT NULL DEFAULT 0,
    "inspectionParameterId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ServiceInspectionParameter_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_FileToInspectionParameter" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE INDEX "ServiceInspectionParameter_inspectionParameterId_idx" ON "ServiceInspectionParameter"("inspectionParameterId");

-- CreateIndex
CREATE UNIQUE INDEX "_FileToInspectionParameter_AB_unique" ON "_FileToInspectionParameter"("A", "B");

-- CreateIndex
CREATE INDEX "_FileToInspectionParameter_B_index" ON "_FileToInspectionParameter"("B");

-- AddForeignKey
ALTER TABLE "ServiceInspectionParameter" ADD CONSTRAINT "ServiceInspectionParameter_inspectionParameterId_fkey" FOREIGN KEY ("inspectionParameterId") REFERENCES "InspectionParameter"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_FileToInspectionParameter" ADD CONSTRAINT "_FileToInspectionParameter_A_fkey" FOREIGN KEY ("A") REFERENCES "File"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_FileToInspectionParameter" ADD CONSTRAINT "_FileToInspectionParameter_B_fkey" FOREIGN KEY ("B") REFERENCES "InspectionParameter"("id") ON DELETE CASCADE ON UPDATE CASCADE;
