import { NextResponse } from "next/server";
import { auth } from "@/src/providers/auth";
import { getFirstEnabledMemberFromUserId } from "./src/actions/membership";

export async function middleware(request) {
  const session = await auth();

  if (!session?.user) {
    return NextResponse.next();
  }

  const publicPaths = ['/auth', '/api/onlyoffice/callback'];
  const path = request.nextUrl.pathname;

  if (publicPaths.some(publicPath => path.startsWith(publicPath))) {
    return NextResponse.next();
  }

  const membership = await getFirstEnabledMemberFromUserId(`${session.user.id}`)

  if (!membership) {
    return NextResponse.redirect(new URL('/welcome', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
