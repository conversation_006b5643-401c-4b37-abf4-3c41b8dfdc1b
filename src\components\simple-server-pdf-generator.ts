"use server";

import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import { formatDate } from "@/src/lib/utils";

// Função simplificada para gerar PDF no servidor
export async function generateSimpleServerPdf(data: any) {
  try {
    console.log("Iniciando geração do PDF no servidor...");
    const { inspection, customer } = data;

    // Criar um novo documento PDF
    const pdf = new jsPDF("p", "mm", "a4");
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // Configurações de margens
    const margin = 15;

    // Adicionar propriedade lastAutoTable ao objeto pdf para compatibilidade
    (pdf as any).lastAutoTable = { finalY: margin };

    // Título do relatório
    const inspectionDate = inspection.inspectionDate
      ? formatDate(inspection.inspectionDate, "DATE")
      : formatDate(new Date(), "DATE");

    // Adicionar cabeçalho
    pdf.setFontSize(11);
    pdf.setTextColor(0, 0, 0);
    pdf.setFont("helvetica", "bold");
    pdf.text(
      `Relatório ${inspectionDate} nº ${inspection.numberInspection || ""}`,
      pageWidth / 2,
      margin + 8,
      { align: "center" }
    );

    // Informações do relatório
    let yPos = margin + 20;

    // Tabela de informações do relatório
    autoTable(pdf, {
      startY: yPos,
      head: [["", ""]],
      body: [
        ["Relatório nº", `${inspection.numberInspection || ""}`],
        ["Data do relatório", `${inspectionDate}`],
        [
          "Dia da semana",
          new Date(inspection.inspectionDate).toLocaleDateString("pt-BR", {
            weekday: "long",
          }),
        ],
      ],
      theme: "grid",
      styles: { fontSize: 10, cellPadding: 5 },
    });

    // Tabela de comentários (campo observation)
    yPos = (pdf as any).lastAutoTable.finalY + 10;
    autoTable(pdf, {
      startY: yPos,
      head: [["Comentários e Observações"]],
      body: [
        [
          `Cliente: ${customer?.name || "Não informado"}\nData: ${formatDate(
            inspection.inspectionDate,
            "DATETIME"
          )}\n\nObservação: ${inspection.observation || "Sem observações"}`,
        ],
      ],
      theme: "grid",
      styles: { fontSize: 10, cellPadding: 5 },
    });

    // Adicionar número de página
    const totalPages = pdf.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      pdf.setPage(i);
      pdf.setFontSize(8);
      pdf.setFont("helvetica", "normal");
      pdf.text(
        `${i} / ${totalPages}`,
        pageWidth - margin,
        pageHeight - margin - 2
      );
    }

    // Gerar o PDF como um buffer
    const pdfBuffer = Buffer.from(pdf.output("arraybuffer"));

    return {
      success: true,
      buffer: pdfBuffer,
      fileName: `relatorio_inspecao_${
        inspection.numberInspection || Date.now()
      }.pdf`,
    };
  } catch (error) {
    console.error("Erro ao gerar PDF no servidor:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro desconhecido",
    };
  }
}
