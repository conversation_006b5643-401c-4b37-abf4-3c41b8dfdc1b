"use client";

import { useState, useEffect } from "react";
import { checkRouteAccess } from "@/src/actions/route-permissions";

export function useRoutePermissions(route: string) {
  const [hasAccess, setHasAccess] = useState<boolean>(true); // Padrão: acesso liberado
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const checkAccess = async () => {
      try {
        setLoading(true);
        const access = await checkRouteAccess(route);
        setHasAccess(access);
      } catch (error) {
        console.error("Error checking route access:", error);
        // Em caso de erro, negar acesso por segurança
        setHasAccess(false);
      } finally {
        setLoading(false);
      }
    };

    checkAccess();
  }, [route]);

  return { hasAccess, loading };
}
