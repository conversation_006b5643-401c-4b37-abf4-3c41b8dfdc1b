import sharp from "sharp";
import { storageProvider } from "./storage";
import { prisma } from "./prisma";
import { MINIO_BUCKET_NAME } from "./env/variables";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { minioClient } from "./minio";
import { getCurrentOrganization } from "./auth/get-current-organization";
import { toKebabCase } from "./utils";

/**
 * Gera e armazena uma miniatura para uma imagem
 * @param fileId ID do arquivo no banco de dados
 * @param filePath Caminho do arquivo no Minio
 * @returns Caminho da miniatura gerada ou null em caso de erro
 */
export async function generateAndStoreThumbnail(
  fileId: string,
  filePath: string
): Promise<string | null> {
  try {
    console.log(
      `Gerando miniatura para arquivo: ${fileId}, caminho: ${filePath}`
    );

    // Buscar o arquivo no storage
    const file = await storageProvider.get(filePath);
    if (!file || !file.stream) {
      console.error(`Arquivo não encontrado no storage: ${filePath}`);
      return null;
    }

    // Converter stream para buffer
    const chunks: Buffer[] = [];
    for await (const chunk of file.stream as any) {
      chunks.push(Buffer.from(chunk));
    }
    const buffer = Buffer.concat(chunks);

    // Gerar miniatura usando sharp
    const thumbnailBuffer = await sharp(buffer)
      .resize({ height: 140, width: 200, fit: "cover" })
      .toBuffer();

    const { organizationName } = await getCurrentOrganization();
    if (!organizationName) throw new Error("Nome da organização não encontrado para salvar miniatura.");
    const orgSlug = toKebabCase(organizationName);

    // Definir caminho da miniatura
    const thumbnailPath = `${orgSlug}/thumbnails/${fileId}`;

    // Salvar miniatura no Minio
    await minioClient.send(
      new PutObjectCommand({
        Bucket: MINIO_BUCKET_NAME!,
        Key: thumbnailPath,
        Body: thumbnailBuffer,
        ContentType: "image/jpeg",
        ACL: "public-read" as const,
      })
    );

    // Atualizar o registro no banco de dados
    await prisma.file.update({
      where: { id: fileId },
      data: {
        thumbnailPath: thumbnailPath,
      },
    });

    console.log(`Miniatura gerada com sucesso: ${thumbnailPath}`);
    return thumbnailPath;
  } catch (error) {
    console.error("Erro ao gerar miniatura:", error);
    return null;
  }
}
