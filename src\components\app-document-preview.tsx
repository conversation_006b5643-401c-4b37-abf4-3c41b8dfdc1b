"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	<PERSON>alogTitle,
	DialogTrigger,
} from "@/components/ui/dialog";
import { DialogDescription } from "@radix-ui/react-dialog";
import { FileTextIcon } from "lucide-react";
import { useState } from "react";
import { Template } from "../types/utils";
import Image from "next/image";
import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from "@/components/ui/accordion";

type FileType = "pdf" | "image" | "other";
type FilePreviewItem = {
	url: string;
	type: FileType;
	title?: string;
};

interface PreviewDialogProps {
	url: string | string[];
	type: FileType | FileType[];
	title?: string | string[];
	template?: Template;
}

const FilePreview = ({
	url,
	type,
	title,
}: {
	url: string;
	type: FileType;
	title: string;
}) => {
	// Verificar se a URL é válida
	if (!url) {
		return (
			<div className="flex flex-col items-center justify-center h-[calc(100vh-100px)] gap-4">
				<FileTextIcon className="w-16 h-16 text-gray-400" />
				<p className="text-gray-600 text-center max-w-md">Arquivo não encontrado ou indisponível.</p>
			</div>
		);
	}

	const [isLoading, setIsLoading] = useState(type !== "other"); // Começa false apenas para "other"

	const handleDownload = async () => {
		try {
			setIsLoading(true);
			const response = await fetch(url);
			const blob = await response.blob();
			const downloadUrl = window.URL.createObjectURL(blob);
			const link = document.createElement('a');
			link.href = downloadUrl;
			link.download = title || url.split('/').pop() || 'download'; // Use title as filename, fallback to URL path or 'download'
			document.body.appendChild(link);
			link.click();
			link.remove();
			window.URL.revokeObjectURL(downloadUrl);
		} catch (error) {
			console.error('Error downloading file:', error);
		} finally {
			setIsLoading(false);
		}
	};

	switch (type) {
		case "pdf":
			return (
				<>
					{isLoading && (
						<div className="flex items-center justify-center h-[calc(100vh-100px)]">
							<div className="flex flex-col items-center gap-4">
								<div className="size-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
								<p className="text-sm font-medium text-gray-600">Carregando PDF...</p>
							</div>
						</div>
					)}
					<iframe
						src={`${url}#view=FitH`}
						className="w-full h-[calc(100vh-100px)]"
						title="PDF Preview"
						onLoad={() => {
							setTimeout(() => setIsLoading(false), 1000);
						}}
						style={{ display: isLoading ? 'none' : 'block' }}
					/>
				</>
			);
		case "image":
			return (
				<div className="h-[calc(100vh-100px)]">
					{isLoading && (
						<div className="flex items-center justify-center h-full">
							<div className="flex flex-col items-center gap-4">
								<div className="size-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
								<p className="text-sm font-medium text-gray-600">Carregando imagem...</p>
							</div>
						</div>
					)}
					<div className="h-full flex items-center justify-center relative">
						<Image
							src={url}
							alt="Image Preview"
							fill
							className={`object-contain transition-opacity duration-500 ${isLoading ? 'opacity-0' : 'opacity-100'}`}
							onLoad={() => {
								setIsLoading(false)
							}}
							unoptimized
						/>
					</div>
				</div>
			);
		case "other":
			return (
				<div className="flex flex-col items-center justify-center h-[calc(100vh-100px)] gap-4">
					<FileTextIcon className="w-16 h-16 text-gray-400" />
					<p className="text-gray-600 text-center max-w-md">Visualizamos apenas PDF, mas você pode fazer o download deste documento clicando abaixo.</p>
					<Button
						onClick={handleDownload}
						className="mt-4 bg-blue-500 hover:bg-blue-600"
						disabled={isLoading}
					>
						{isLoading ? 'Baixando...' : 'Baixar arquivo'}
					</Button>
				</div>
			);
		default:
			return (
				<div className="flex flex-col items-center justify-center h-[calc(100vh-100px)] gap-4">
					<FileTextIcon className="w-16 h-16 text-gray-400" />
					<p className="text-gray-600 text-center max-w-md">Visualizamos apenas PDF, mas você pode fazer o download deste documento clicando abaixo.</p>
					<Button
						onClick={handleDownload}
						className="mt-4 bg-blue-500 hover:bg-blue-600"
						disabled={isLoading}
					>
						{isLoading ? 'Baixando...' : 'Baixar arquivo'}
					</Button>
				</div>
			);
	}
};

// Novo componente para múltiplos arquivos
const MultipleFilePreview = ({
	files,
}: {
	files: FilePreviewItem[];
}) => {
	return (
		<Accordion type="single" collapsible className="w-full">
			{files.map((file, idx) => (
				<AccordionItem
					value={`item-${idx}`}
					key={file.url + idx}
					className="border border-green-200 rounded-lg mb-3 bg-gradient-to-r from-green-50 to-white shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden"
				>
					<AccordionTrigger className="flex flex-1 items-center justify-between px-6 py-5 text-base font-semibold transition-all duration-200 text-left bg-gradient-to-r from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 hover:text-green-700 [&[data-state=open]>svg]:rotate-180 [&[data-state=open]]:bg-gradient-to-r [&[data-state=open]]:from-green-100 [&[data-state=open]]:to-green-200 [&[data-state=open]]:text-green-700 [&[data-state=open]]:border-b [&[data-state=open]]:border-green-200 text-green-600">
						{file.title || `Documento ${idx + 1}`}
					</AccordionTrigger>
					<AccordionContent className="px-6 py-4 bg-green-50/30 border-t border-green-100">
						<FilePreview url={file.url} type={file.type} title={file.title || `Documento ${idx + 1}`} />
					</AccordionContent>
				</AccordionItem>
			))}
		</Accordion>
	);
};

export default function DocumentPreviewDialog({
	url,
	title = "Visualizar",
	type,
	template,
}: PreviewDialogProps) {
	const [isOpen, setIsOpen] = useState(false);

	// Verifica se é múltiplo
	const isMultiple =
		Array.isArray(url) &&
		Array.isArray(type) &&
		url.length > 1 &&
		url.length === type.length;

	let files: FilePreviewItem[] = [];
	if (isMultiple) {
		files = url.map((u, idx) => ({
			url: u,
			type: type[idx],
			title: Array.isArray(title) ? (title[idx] || `Documento ${idx + 1}`) : `Documento ${idx + 1}`,
		}));
	}

	return (
		<Dialog open={isOpen} onOpenChange={setIsOpen}>
			<DialogTrigger asChild>
				{template ? (
					template
				) : (
					<Button className="gap-2 bg-blue-500 hover:bg-blue-600" type="button">
						Visualizar
					</Button>
				)}
			</DialogTrigger>
			<DialogContent className="max-w-full h-full w-full">
				<DialogHeader>
					<DialogTitle className="text-lg font-semibold">
						{isMultiple ? "Visualizar Documentos" : (Array.isArray(title) ? title[0] : title)}
					</DialogTitle>
					<DialogDescription />
				</DialogHeader>
				{isMultiple ? (
					<MultipleFilePreview files={files} />
				) : (
					<FilePreview
						url={Array.isArray(url) ? url[0] : url}
						type={Array.isArray(type) ? type[0] : type}
						title={Array.isArray(title) ? title[0] : title}
					/>
				)}
			</DialogContent>
		</Dialog>
	);
}
