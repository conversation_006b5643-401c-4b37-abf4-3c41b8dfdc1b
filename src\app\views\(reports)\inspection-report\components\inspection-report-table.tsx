"use client";

import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from "react";
import { TableGrid } from "@/src/components/ui/table-grid";
import { useToast } from "@/src/hooks/use-toast";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Eraser } from "lucide-react";
import { Proposal, ProposalFilters } from "@/src/types/core/proposal";
import { Customer } from "@/src/types/core/customer";

interface InspectionReportTableProps {
  columns: any[];
  onViewClick: (proposalId: string) => void;
  onPageChange?: (page: number) => void;
  customers: Customer[];
}

export type InspectionReportTableRef = {
  refresh: (page?: number, filters?: ProposalFilters) => void;
};

interface PaginationType {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

const InspectionReportTable = forwardRef<InspectionReportTableRef, InspectionReportTableProps>(
  function InspectionReportTable({ columns, onPageChange }, ref) {
    const [data, setData] = useState<Proposal[]>([]);
    const [loading, setLoading] = useState(false);
    const [search, setSearch] = useState("");
    const searchInputRef = useRef<HTMLInputElement>(null);
    const { toast } = useToast();
    const [pagination, setPagination] = useState<PaginationType>({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });

    const fetchProposals = useCallback(async (
      page?: number,
      pageSize: number = pagination.pageSize,
      searchTerm: string = search,
      filters?: ProposalFilters
    ) => {
      try {
        setLoading(true);

        const params = new URLSearchParams({
          page: String(page || pagination.page),
          pageSize: String(pageSize),
        });

        if (searchTerm) {
          params.append('search', searchTerm);
        }

        if (filters?.customerId) {
          params.append('customerId', filters.customerId);
        }

        const response = await fetch(`/api/proposals-with-inspection?${params}`);

        if (!response.ok) {
          throw new Error("Failed to fetch proposals with inspection parameters");
        }

        const result = await response.json();
        console.log('API response:', result);

        setData(Array.isArray(result.data) ? result.data : []);
        setPagination(prev => ({
          ...prev,
          page: Number(result.page),
          pageSize: Number(result.pageSize),
          total: Number(result.total),
          totalPages: Number(result.totalPages)
        }));
      } catch (error) {
        console.error('Erro ao buscar propostas:', error);
        toast({
          title: "Erro",
          description: "Erro ao carregar propostas com parâmetros de inspeção",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }, [pagination.page, pagination.pageSize, search, toast]);

    useImperativeHandle(ref, () => ({
      refresh: (page?: number, filters?: ProposalFilters) =>
        fetchProposals(page, pagination.pageSize, search, filters)
    }), [fetchProposals, pagination.pageSize, search]);

    useEffect(() => {
      fetchProposals(1);
    }, [fetchProposals]);

    const handleSearch = (value: string) => {
      setSearch(value);
      fetchProposals(1, pagination.pageSize, value);
    };

    const handleClearFilters = () => {
      if (searchInputRef.current) {
        searchInputRef.current.value = "";
      }
      setSearch("");
      fetchProposals(1, pagination.pageSize, "");
    };

    return (
      <TableGrid
        columns={columns}
        data={data}
        loading={loading}
        pagination={pagination}
        onPaginationChange={(newPage, newPageSize) => {
          fetchProposals(newPage, newPageSize, search);
          if (onPageChange) onPageChange(newPage);
        }}
        onSearch={handleSearch}
        searchValue={search}
        searchInputRef={searchInputRef}
        buttonsTemplate={
          <>
            <Button
              className="w-full sm:w-auto bg-blue-500 hover:bg-blue-400"
              onClick={handleClearFilters}
            >
              Limpar filtros <Eraser className="ml-2 h-4 w-4" />
            </Button>
          </>
        }
      />
    );
  }
);

export default InspectionReportTable;



