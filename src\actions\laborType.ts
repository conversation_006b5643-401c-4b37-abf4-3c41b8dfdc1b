"use server";
import { prisma } from "@/src/lib/prisma";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

export async function getLabortypes(
  page: number = 1,
  pageSize: number = 10,
  search: string = ""
) {
  try {
    const { organizationId } = await getCurrentOrganization();
    const skip = (page - 1) * pageSize;

    const where = {
      organizationId,
      ...(search
        ? {
            OR: [
              { name: { contains: search, mode: "insensitive" as const } },
              {
                description: { contains: search, mode: "insensitive" as const },
              },
            ],
          }
        : {}),
    };

    const [total, data] = await Promise.all([
      prisma.labor.count({
        where: {
          organizationId,
          ...(search
            ? {
                OR: [
                  { name: { contains: search, mode: "insensitive" as const } },
                  {
                    description: {
                      contains: search,
                      mode: "insensitive" as const,
                    },
                  },
                ],
              }
            : {}),
        },
      }),
      prisma.labor.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: {
          createdAt: "desc",
        },
      }),
    ]);

    return {
      data,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  } catch (error) {
    console.error(error);
    return {
      data: [],
      total: 0,
      page,
      pageSize,
      totalPages: 0,
    };
  }
}

export async function findLabortype(id: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const data = await prisma.labor.findUnique({
      where: {
        id,
        organizationId,
      },
    });
    return data;
  } catch (error) {
    console.error(error);
  }
}

export async function findLaborEquipamentByRepairBudgetId(
  repairBudgetId: string
) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const data = await prisma.laborEquipmentAmount.findMany({
      where: {
        repairBudgetId,
        labor: {
          organizationId,
        },
      },
      include: {
        labor: true,
      },
      orderBy: {
        labor: {
          name: "asc",
        },
      },
    });
    return data;
  } catch (error) {
    console.error(error);
  }
}

export async function saveLaborEquipament(laborEquipament: any) {
  try {
    const { organizationId } = await getCurrentOrganization();

    if (laborEquipament.id) {
      // Update existing labor equipament
      const updatedLabor = await prisma.labor.update({
        where: {
          id: laborEquipament.id,
          organizationId,
        },
        data: {
          name: laborEquipament.name,
          description: laborEquipament.description,
          type: laborEquipament.type,
          laborType: laborEquipament.laborType,
          updatedAt: new Date(),
        },
      });
      return {
        error: false,
        data: updatedLabor,
        message: "Item atualizado com sucesso!",
      };
    } else {
      // Create new labor equipament
      delete laborEquipament.id;
      const newLabor = await prisma.labor.create({
        data: {
          name: laborEquipament.name,
          description: laborEquipament.description,
          type: laborEquipament.type,
          laborType: laborEquipament.laborType,
          organizationId,
        },
      });
      return {
        error: false,
        data: newLabor,
        message: "Item criado com sucesso!",
      };
    }
  } catch (error) {
    console.error(error);
    return {
      error: true,
      message: laborEquipament.id
        ? "Erro ao atualizar item"
        : "Erro ao criar item",
    };
  }
}

export async function removeLabortype(id: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    await prisma.labor.delete({
      where: {
        id,
        organizationId,
      },
    });

    return { message: "Item removido com sucesso!" };
  } catch (error) {
    console.error(error);
  }
}

export async function saveLaborEquipamentByRepairBudget(
  repairBudgetId: string,
  laborEquipaments: Record<
    string,
    { id?: string; laborId: string; amount: number }
  >
) {
  try {
    const { organizationId } = await getCurrentOrganization();

    // Verify labor belongs to organization
    const labors = await prisma.labor.findMany({
      where: {
        id: {
          in: Object.values(laborEquipaments).map((item) => item.laborId),
        },
        organizationId,
      },
    });

    if (labors.length !== Object.keys(laborEquipaments).length) {
      throw new Error("Some labor items don't belong to your organization");
    }

    // Get existing laborEquipaments
    const existingLaborEquipaments = await prisma.laborEquipmentAmount.findMany(
      {
        where: { repairBudgetId },
      }
    );

    // Convert laborEquipaments object to array
    const laborEquipamentsArray = Object.values(laborEquipaments).filter(
      (item) =>
        item &&
        typeof item === "object" &&
        "laborId" in item &&
        "amount" in item
    );

    // Find removed items
    const removedItems = existingLaborEquipaments.filter(
      (existingItem) =>
        !laborEquipamentsArray.some(
          (item) => item.laborId === existingItem.laborId
        )
    );

    // Delete removed items
    await Promise.all(
      removedItems.map((item) =>
        prisma.laborEquipmentAmount.delete({
          where: { id: item.id },
        })
      )
    );

    // Save or update remaining items
    const data = await Promise.all(
      laborEquipamentsArray.map(async (laborEquipament) => {
        if (laborEquipament.id) {
          return await prisma.laborEquipmentAmount.update({
            where: { id: laborEquipament.id },
            data: { amount: laborEquipament.amount },
          });
        } else {
          return await prisma.laborEquipmentAmount.create({
            data: {
              amount: laborEquipament.amount,
              repairBudgetId: repairBudgetId,
              laborId: laborEquipament.laborId,
            },
          });
        }
      })
    );

    return data;
  } catch (e) {
    console.error(e);
  }
}

export async function removeLaborEquipamentByRepairBudget(
  laborEquipamentId: string
) {
  try {
    await prisma.laborEquipmentAmount.delete({
      where: { id: laborEquipamentId },
    });

    return { message: "Item removido com sucesso!" };
  } catch (error) {
    console.error(error);
  }
}
