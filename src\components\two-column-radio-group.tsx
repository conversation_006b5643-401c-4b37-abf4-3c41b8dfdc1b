"use client";

import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { RadioGroup, RadioGroupItem } from "./ui/radio-group";
import { FieldPath, FieldValues, useFormContext } from "react-hook-form";

interface Item {
  label: string;
  value: string;
  title?: string;
}

interface TwoColumnRadioGroupProps<TFieldValues extends FieldValues> {
  name: FieldPath<TFieldValues>;
  label?: string;
  items: Item[];
  value?: string;
  disabled?: boolean;
  className?: string;
  labelClassName?: string;
  required?: boolean;
}

export function TwoColumnRadioGroup<TFieldValues extends FieldValues = FieldValues>({
  name,
  label,
  items,
  disabled,
  className,
  labelClassName,
  required,
}: TwoColumnRadioGroupProps<TFieldValues>) {
  const { control } = useFormContext<TFieldValues>();

  return (
    <div className={className}>
      {label && (
        <Label
          className={cn("font-bold text-gray-700", labelClassName)}
          htmlFor={name}
        >
          {label} {required && <span className="text-black">*</span>}
        </Label>
      )}
      <FormField
        name={name}
        control={control}
        render={({ field }) => (
          <FormItem className="mt-2">
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                value={field.value}
                className="grid grid-cols-2 gap-4"
                disabled={disabled}
              >
                {items.map((item) => (
                  <FormItem
                    key={item.value}
                    className="flex items-center space-x-2 space-y-0"
                  >
                    <FormControl>
                      <RadioGroupItem
                        value={item.value}
                        id={`${name}-${item.value}`}
                        className="text-green-500 focus:ring-green-500"
                      />
                    </FormControl>
                    <Label
                      htmlFor={`${name}-${item.value}`}
                      className="font-normal cursor-pointer hover:text-green-500"
                    >
                      {item.label}
                    </Label>
                  </FormItem>
                ))}
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
