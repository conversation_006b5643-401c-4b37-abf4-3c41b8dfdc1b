import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

export async function GET(request: Request) {
  try {
    const { organizationId } = await getCurrentOrganization();
    const { searchParams } = new URL(request.url);

    const page = Number(searchParams.get("page")) || 1;
    const pageSize = Number(searchParams.get("pageSize")) || 10;
    const search = searchParams.get("search") || "";
    const types = searchParams.getAll("type");

    const skip = (page - 1) * pageSize;

    const where = {
      organizationId,
      ...(search
        ? {
            OR: [
              { name: { contains: search, mode: "insensitive" as any } },
              { description: { contains: search, mode: "insensitive" as any } },
            ],
          }
        : {}),
      ...(types.length > 0 ? { types: { hasSome: types as any } } : {}),
    };

    const [total, items] = await Promise.all([
      prisma.serviceScope.count({ where }),
      prisma.serviceScope.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: { createdAt: "desc" },
      }),
    ]);

    return NextResponse.json({
      data: items,
      page,
      pageSize,
      total,
      totalPages: Math.ceil(total / pageSize),
    });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Error fetching services" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const { organizationId } = await getCurrentOrganization();
    const data = await request.json();

    const newService = await prisma.serviceScope.create({
      data: {
        name: data.name,
        description: data.description,
        types: data.types,
        organizationId
      },
    });

    return NextResponse.json(newService);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Error creating service" },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  try {
    const { organizationId } = await getCurrentOrganization();
    const data = await request.json();

    const updatedService = await prisma.serviceScope.update({
      where: {
        id: data.id,
        organizationId
      },
      data: {
        name: data.name,
        description: data.description,
        types: data.types,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json(updatedService);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Error updating service" },
      { status: 500 }
    );
  }
}

