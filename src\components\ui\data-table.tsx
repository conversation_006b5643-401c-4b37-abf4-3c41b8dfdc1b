"use client";

import {
	flexRender,
	getCoreRowModel,
	getFilteredRowModel,
	// getPaginationRowModel,
	getSortedRowModel,
	useReactTable,
	type ColumnDef,
	// type PaginationState,
	type SortingState,
} from "@tanstack/react-table";
import * as React from "react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
// import {
// 	Select,
// 	SelectContent,
// 	SelectItem,
// 	SelectTrigger,
// 	SelectValue,
// } from "@/components/ui/select";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import {
	// ChevronLeft,
	// ChevronRight,
	// ChevronsUp,
	Eraser,
	Loader2,
	Plus,
	Search,
} from "lucide-react";
import { cn } from "@/src/lib/utils";

interface DataTableProps<TData, TValue> {
	columns: ColumnDef<TData, TValue>[];
	data: TData[];
	onAddButtonClick?: () => void;
	onClearButtonClick?: () => void;
	buttonsTemplate?: React.ReactNode;
	disabledAddButton?: boolean;
	hideAddButton?: boolean;
	pagination?: {
		page: number;
		pageSize: number;
		total: number;
		totalPages: number;
	};
	onPaginationChange?: (page: number, pageSize: number) => void;
	onSearch?: (search: string) => void;
	searchValue?: string;
	loading?: boolean;
}

export function DataTable<TData, TValue>({
	columns,
	data,
	onClearButtonClick,
	onAddButtonClick,
	disabledAddButton = false,
	hideAddButton = false,
	buttonsTemplate,
	// pagination,
	// onPaginationChange,
	// onSearch,
	// searchValue,
	loading = false
}: DataTableProps<TData, TValue>) {
	const [sorting, setSorting] = React.useState<SortingState>([]);
	const [globalFilter, setGlobalFilter] = React.useState("");

	const table = useReactTable({
		data,
		columns,
		getCoreRowModel: getCoreRowModel(),
		// Remova getPaginationRowModel para evitar paginação local
		// getPaginationRowModel: getPaginationRowModel(),
		onSortingChange: setSorting,
		getSortedRowModel: getSortedRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		onGlobalFilterChange: setGlobalFilter,
		state: {
			sorting,
			globalFilter,
		},
	});

	return (
		<div className="space-y-4">
			<div className="flex flex-col sm:flex-row items-center gap-5 py-4">
				<Input
					placeholder="Pesquisar..."
					endIcon={Search}
					value={globalFilter ?? ""}
					className="focus-visible:border-[1px] focus-visible:border-green-500"
					onChange={(event) => setGlobalFilter(String(event.target.value))}
				/>
				{buttonsTemplate ? (
					buttonsTemplate
				) : (
					<>
						<Button
							className="w-full sm:w-auto bg-blue-500 hover:bg-blue-400"
							onClick={() => onClearButtonClick?.()}
						>
							Limpar filtros <Eraser />
						</Button>
						<Button
							className={cn("w-full sm:w-auto bg-green-500 hover:bg-green-400", hideAddButton && "hidden")}
							disabled={disabledAddButton}
							onClick={() => onAddButtonClick?.()}
						>
							Adicionar <Plus />
						</Button>
					</>
				)}
			</div>

			<div className="rounded-md border overflow-auto">
				<Table>
					<TableHeader>
						{table.getHeaderGroups().map((headerGroup) => (
							<TableRow key={headerGroup.id}>
								{headerGroup.headers.map((header) => (
									<TableHead key={header.id}>
										{header.isPlaceholder
											? null
											: flexRender(
												header.column.columnDef.header,
												header.getContext()
											)}
									</TableHead>
								))}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>
						{loading ? (
							<TableRow>
								<TableCell
									colSpan={columns.length}
									className="h-24 text-center"
								>
									<div className="flex items-center justify-center gap-2">
										<Loader2 className="h-5 w-5 animate-spin text-green-500" />
										<span className="text-sm text-gray-500">Carregando...</span>
									</div>
								</TableCell>
							</TableRow>
						) : table.getRowModel().rows?.length ? (
							table.getRowModel().rows.map((row) => (
								<TableRow
									key={row.id}
									data-state={row.getIsSelected() && "selected"}
								>
									{row.getVisibleCells().map((cell) => (
										<TableCell key={cell.id}>
											{flexRender(
												cell.column.columnDef.cell,
												cell.getContext()
											)}
										</TableCell>
									))}
								</TableRow>
							))
						) : (
							<TableRow>
								<TableCell
									colSpan={columns.length}
									className="h-24 text-center"
								>
									Nenhum resultado encontrado.
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</Table>
			</div>
		</div>
	);
}
