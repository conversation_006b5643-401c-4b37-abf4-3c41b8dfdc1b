/*
  Warnings:

  - You are about to drop the column `periodicity` on the `Productivity` table. All the data in the column will be lost.
  - Added the required column `periodicityId` to the `Productivity` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "PlanningFrequencyItem" DROP CONSTRAINT "PlanningFrequencyItem_repairBudgetId_fkey";

-- AlterTable
ALTER TABLE "Productivity" DROP COLUMN "periodicity",
ADD COLUMN     "periodicityId" TEXT NOT NULL,
ALTER COLUMN "label" DROP NOT NULL;

-- AlterTable
ALTER TABLE "RepairBudget" ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- CreateTable
CREATE TABLE "Contract" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "proposalId" TEXT NOT NULL,
    "fileId" TEXT,

    CONSTRAINT "Contract_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_PlanningFrequencyItemsToRepairBudgets" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "_PlanningFrequencyItemsToRepairBudgets_AB_unique" ON "_PlanningFrequencyItemsToRepairBudgets"("A", "B");

-- CreateIndex
CREATE INDEX "_PlanningFrequencyItemsToRepairBudgets_B_index" ON "_PlanningFrequencyItemsToRepairBudgets"("B");

-- AddForeignKey
ALTER TABLE "Contract" ADD CONSTRAINT "Contract_proposalId_fkey" FOREIGN KEY ("proposalId") REFERENCES "Proposal"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Contract" ADD CONSTRAINT "Contract_fileId_fkey" FOREIGN KEY ("fileId") REFERENCES "File"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Productivity" ADD CONSTRAINT "Productivity_periodicityId_fkey" FOREIGN KEY ("periodicityId") REFERENCES "PlanningFrequencyItem"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_PlanningFrequencyItemsToRepairBudgets" ADD CONSTRAINT "_PlanningFrequencyItemsToRepairBudgets_A_fkey" FOREIGN KEY ("A") REFERENCES "PlanningFrequencyItem"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_PlanningFrequencyItemsToRepairBudgets" ADD CONSTRAINT "_PlanningFrequencyItemsToRepairBudgets_B_fkey" FOREIGN KEY ("B") REFERENCES "RepairBudget"("id") ON DELETE CASCADE ON UPDATE CASCADE;
