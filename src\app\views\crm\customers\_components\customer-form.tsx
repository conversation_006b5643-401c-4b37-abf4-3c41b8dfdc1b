import { CustomInput } from "@/src/components/app-input";
import { documentMask } from "@/src/constants";
import { FormProvider, UseFormReturn, useWatch } from "react-hook-form";
import { CustomerSchema } from "../_schemas/customer.schema";
import { states } from "@/src/constants";
import { useState } from "react";

interface CustomerFormProps {
	methods: UseFormReturn<CustomerSchema>;
	title?: string;
}

export default function CustomerForm({
	methods,
	title = "Informações Pessoais",
}: CustomerFormProps) {

	const documentType = useWatch({
		control: methods.control,
		name: "documentType"
	});

	const [cepLoading, setCepLoading] = useState(false);

	const searchAddresByCep = async () => {
		const cep = methods.getValues("cep")?.replace("-", "")?.replace(".", "");
		if (!cep || cep.length !== 8) return;
		setCepLoading(true);
		try {
			const response = await fetch(`https://viacep.com.br/ws/${cep}/json/`);
			const data = await response.json();
			if (!data.erro) {
				const bairro = data.bairro ? `Bairro ${data.bairro}, ` : "";
				const logradouro = data.logradouro ? `Logradouro ${data.logradouro}` : "";
				methods.setValue("address", `${bairro}${logradouro}`);
				methods.setValue("city", data.localidade);
				methods.setValue("state", data.uf);
			}
		} catch (error) {
			console.error("Erro ao buscar CEP:", error);
		} finally {
			setCepLoading(false);
		}
	};

	return (
		<FormProvider {...methods}>
			<form className="flex flex-col gap-4">
				<h1 className="text-2xl font-bold text-green-500">{title}</h1>
				<div className="grid grid-cols-1 gap-2">
					<div className="grid grid-cols-1 sm:grid-cols-[2fr_1fr_1fr] gap-4">
						<CustomInput label="Nome" name="name" placeholder="Nome" />
						<CustomInput
							label="Tipo de documento"
							name="documentType"
							placeholder="Selecione o tipo de documento"
							type="select"
							items={[
								{ value: "CPF", label: "CPF" },
								{ value: "CNPJ", label: "CNPJ" },
								{ value: "RG", label: "RG" },
								{ value: "CNH", label: "CNH" },
							]}
						/>
						<CustomInput
							label="Documento"
							name="document"
							placeholder={documentType || "Selecione o tipo"}
							type="mask"
							mask={documentType ? documentMask[documentType].mask : ""}
						/>
					</div>
					<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
						<CustomInput
							type="tel"
							label="Telefone"
							name="phone"
							placeholder="(99) 9 9999-9999"
						/>
						<CustomInput
							label="Email"
							name="email"
							placeholder="<EMAIL>"
						/>
					</div>
					<div className="grid grid-cols-1 sm:grid-cols-[1.2fr_2fr_1.2fr_0.5fr] gap-4 items-end">
						<div className="flex gap-2">
							<div className="flex-1">
								<CustomInput
									label="CEP"
									name="cep"
									placeholder="00000-000"
									type="text"
									maxLength={9}
								/>
							</div>
							<button
								type="button"
								className="h-10 px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 self-end flex-shrink-0"
								onClick={searchAddresByCep}
								disabled={cepLoading}
							>
								{cepLoading ? "Buscando..." : "Buscar CEP"}
							</button>
						</div>
						<CustomInput
							label="Endereço"
							name="address"
							placeholder="Endereço"
						/>
						<CustomInput
							label="Cidade"
							name="city"
							placeholder="Cidade"
						/>
						<div className="w-full sm:w-44">
							<CustomInput
								label="Estado"
								name="state"
								placeholder="Estado"
								type="select"
								items={states}
							/>
						</div>
					</div>
					<CustomInput
						label="Observação"
						name="observation"
						placeholder="Observação"
					/>
				</div>
			</form>
		</FormProvider>
	);
}
