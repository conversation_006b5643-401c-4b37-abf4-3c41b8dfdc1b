import {
  MINIO_BUCKET_NAME,
  MINIO_ENDPOINT,
  MINIO_ROOT_PASSWORD,
  MINIO_ROOT_USER,
  STORAGE_REGION,
} from "@/src/lib/env/variables";
import { formatFileName } from "@/src/lib/utils";
import { File } from "@/src/types/core/file";
import {
  DeleteObjectCommand,
  GetObjectCommand,
  PutObjectCommand,
  S3Client,
} from "@aws-sdk/client-s3";

export class MinioStorageProviderV3 {
  private client: S3Client;

  constructor() {
    this.client = new S3Client({
      endpoint: MINIO_ENDPOINT!,
      region: STORAGE_REGION!,
      credentials: {
        accessKeyId: MINIO_ROOT_USER!,
        secretAccessKey: MINIO_ROOT_PASSWORD!,
      },
      forcePathStyle: true, // Necessário para MinIO
    });
  }

  async get(path: string) {
    try {
      console.log(`Tentando obter arquivo com caminho: ${path}`);

      // Remover qualquer prefixo de URL que possa estar presente
      let cleanPath = path.replace(/^https?:\/\/[^\/]+\//, "");

      // Remover o nome do bucket se estiver presente no caminho
      if (cleanPath.startsWith(`${MINIO_BUCKET_NAME}/`)) {
        cleanPath = cleanPath.substring(MINIO_BUCKET_NAME!.length + 1);
      }

      console.log(`Caminho limpo: ${cleanPath}`);

      const command = new GetObjectCommand({
        Bucket: MINIO_BUCKET_NAME!,
        Key: cleanPath,
      });

      try {
        const data = await this.client.send(command);

        if (!data.Body) {
          console.log(`Arquivo não encontrado: ${cleanPath}`);
          return undefined;
        }

        console.log(
          `Arquivo encontrado: ${cleanPath}, tipo: ${data.ContentType}`
        );
        return {
          name: cleanPath.split("/").pop()!,
          contentType: data.ContentType,
          stream: data.Body as ReadableStream,
        };
      } catch (s3Error) {
        // Se o arquivo não for encontrado com o caminho limpo, tente com o caminho original
        console.log(s3Error);
        console.log(
          `Erro ao buscar com caminho limpo, tentando caminho original: ${path}`
        );
        const originalCommand = new GetObjectCommand({
          Bucket: MINIO_BUCKET_NAME!,
          Key: path,
        });

        const data = await this.client.send(originalCommand);

        if (!data.Body) {
          console.log(`Arquivo não encontrado com caminho original: ${path}`);
          return undefined;
        }

        console.log(
          `Arquivo encontrado com caminho original: ${path}, tipo: ${data.ContentType}`
        );
        return {
          name: path.split("/").pop()!,
          contentType: data.ContentType,
          stream: data.Body as ReadableStream,
        };
      }
    } catch (error) {
      console.error(`Erro ao obter arquivo ${path}:`, error);
      return undefined;
    }
  }

  async uploadBuffer(
    buffer: Buffer<any>,
    fileType: string,
    fileKey: string
  ): Promise<string> {
    const params = {
      Bucket: MINIO_BUCKET_NAME!,
      Key: fileKey,
      Body: buffer,
      ACL: "public-read" as const,
      ContentType: fileType,
    };

    try {
      const command = new PutObjectCommand(params);
      await this.client.send(command);
      return params.Key;
    } catch (error) {
      console.error("Upload Error", error);
      throw new Error("Error Upload file");
    }
  }

  async upload(file: File): Promise<string> {
    const fileName = formatFileName(file.name);
    const filebBuffer = Buffer.from((file as any).buffer);
    const params = {
      Bucket: MINIO_BUCKET_NAME!,
      Key: `${file.path}/${fileName}`,
      Body: filebBuffer,
      ACL: "public-read" as const,
      ContentType: file.type,
    };

    try {
      const command = new PutObjectCommand(params);
      await this.client.send(command);
      return params.Key;
    } catch (error) {
      console.error("Upload Error", error);
      throw new Error("Error Upload file");
    }
  }

  async uploadFiles(files: File[]): Promise<string[]> {
    try {
      const uploadPromises = files.map((file) => this.upload(file));
      const uploadedKeys = await Promise.all(uploadPromises);
      return uploadedKeys;
    } catch (error) {
      console.error("Multiple Upload Error", error);
      throw new Error("Error uploading multiple files");
    }
  }

  async delete(path: string): Promise<void> {
    const formatedPath = path.replace(
      `${MINIO_ENDPOINT!}/${MINIO_BUCKET_NAME!}`,
      ""
    );

    const params = {
      Bucket: MINIO_BUCKET_NAME!,
      Key: formatedPath,
    };

    try {
      const command = new DeleteObjectCommand(params);
      await this.client.send(command);
    } catch (error) {
      console.error("Delete Error", error);
      throw new Error("Error delete file");
    }
  }

  async deleteMany(paths: string[]): Promise<void> {
    for (const path of paths) {
      await this.delete(path);
    }
  }
}
