"use client";
import { removeCustomer } from "@/src/actions/customers";
import ContentWrapper from "@/src/components/content-wrapper";
import { TouchTooltip } from "@/src/components/ui/touch-tooltip";
import { useToast } from "@/src/hooks/use-toast";
import { constructActionColumn, contructColumn } from "@/src/lib/table/columns";


import { useRouter } from "next/navigation";
import { useRef, useState } from "react";
import CustomersTable, { CustomersTableRef } from "./components/customers-table";


export default function Customers() {
	const { toast } = useToast();
	const router = useRouter();
	const [loading, setLoading] = useState(false);
	const [currentPage, setCurrentPage] = useState(1);
	const tableRef = useRef<CustomersTableRef>(null);

	const deleteCustomer = async (id: string) => {
		try {
			setLoading(true);
			const result = await removeCustomer(id);

			if (result?.error) {
				toast({
					title: "Erro",
					description: result.message || "Falha ao remover o cliente",
					variant: "destructive"
				});
			} else {
				toast({
					title: "Sucesso",
					description: "Cliente excluído com sucesso!",
					variant: "default"
				});
				tableRef.current?.refresh(currentPage);
			}
		} catch (error: any) {
			console.error('Erro ao excluir cliente:', error);
			toast({
				title: "Erro",
				description: error?.message || "Falha ao remover o cliente",
				variant: "destructive"
			});
		} finally {
			setLoading(false);
		}
	};

	const columns = [
		contructColumn("name", "Nome"),
		contructColumn("document", "Documento"),
		contructColumn("email", "Email"),
		contructColumn("phone", "Telefone"),
		contructColumn("observation", "Observação", (row: any) => {
			const observation = row.observation as string | null;
			const displayText = observation
				? (observation.length > 30 ? observation.substring(0, 30) + "..." : observation)
				: "Sem observações";

			return (
				<TouchTooltip
					content={observation || "Sem observações"}
					maxWidth="300px"
					className="max-w-[120px] sm:max-w-[200px] truncate hover:text-blue-500 transition-colors"
				>
					{displayText}
				</TouchTooltip>
			)
		}),
		constructActionColumn([
			{
				action: "edit",
				callback: (row: any) => router.push(`/views/crm/customers/${row.id}`),
			},
			{
				action: "delete",
				dialogTitle: "Deseja realmente excluir o cliente?",
				dialogDescription:
					"Esta operação não poderá ser desfeita, todos os dados associados ao cliente serão apagados.",
				callback: (row: any) => deleteCustomer(row.id),
			},
		]),
	];

	return (
		<ContentWrapper title="Clientes" loading={loading}>
			<CustomersTable
				ref={tableRef}
				columns={columns}
				onAddClick={() => router.push("/views/crm/customers/add")}
				onPageChange={(page) => setCurrentPage(page)}
			/>
		</ContentWrapper>
	);
}
