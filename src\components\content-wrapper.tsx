"use client"

import { Template } from "../types/utils";
import Loading from "./app-loading";
import { useIsMobile } from "@src/hooks/use-mobile";

interface ContentWrapperProps {
  title?: string;
  header?: Template;
  children?: Template;
  loading?: boolean;
  id?: string;
}

export default function ContentWrapper({
  title = "",
  loading,
  header,
  children,
  id,
}: ContentWrapperProps) {
  const isMobile = useIsMobile();

  return (
    <section
      style={{ height: isMobile ? "calc(var(--vh, 1vh) * 100 - 5rem)" : "calc(100vh - 5rem)" }}
      className="bg-white shadow-lg rounded-md p-3 block mb-16"
    >
      <div id={id} className={`flex flex-col h-full ${isMobile ? 'p-2.5 pb-20 mb-16' : 'p-3'}`}>
        <h1 className={`${isMobile ? 'text-xl mb-1' : 'text-3xl'} font-semibold text-green-500`}>{title}</h1>
        {header}
        <div className={`overflow-auto w-full h-full ${isMobile ? 'mt-3 pb-32' : 'mt-4'} block`}>
          {loading ? <Loading /> : children}
        </div>
      </div>
    </section>
  );
}
