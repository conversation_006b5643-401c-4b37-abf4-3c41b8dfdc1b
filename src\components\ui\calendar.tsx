"use client";

import { ChevronLeftIcon, ChevronRightIcon } from "@radix-ui/react-icons";
import * as React from "react";
import { DayPicker } from "react-day-picker";
import { Button, buttonVariants } from "@src/components/ui/button";
import { cn } from "@src/lib/utils";
import { ptBR } from "date-fns/locale";
import { format } from "date-fns";

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

function Calendar({
	className,
	classNames,
	showOutsideDays = true,
	...props
}: CalendarProps) {
	const [month, setMonth] = React.useState(new Date());

	// Lista de meses para o dropdown
	const months = Array.from({ length: 12 }, (_, i) =>
		format(new Date(2023, i, 1), "MMM", { locale: ptBR })
			.replace(/^\w/, (c) => c.toUpperCase())
	);

	// Lista de anos de 2000 até 2035
	const years = Array.from(
		{ length: 2035 - 2000 + 1 },
		(_, i) => 2000 + i
	);

	return (
		<DayPicker
			locale={ptBR}
			showOutsideDays={showOutsideDays}
			month={month}
			onMonthChange={setMonth}
			className={cn("p-3", className)}
			classNames={{
				months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
				month: "space-y-4",
				caption: "flex justify-center pt-1 relative items-center",
				caption_label: "hidden", // Esconde o label padrão
				nav: "space-x-1 flex items-center",
				nav_button: cn(
					buttonVariants({ variant: "outline" }),
					"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
				),
				table: "w-full border-collapse space-y-1",
				head_row: "flex",
				head_cell: "text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",
				row: "flex w-full mt-2",
				cell: cn(
					"relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected].day-range-end)]:rounded-r-md",
					props.mode === "range"
						? "[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md"
						: "[&:has([aria-selected])]:rounded-md"
				),
				day: cn(
					buttonVariants({ variant: "ghost" }),
					"h-8 w-8 p-0 font-normal aria-selected:opacity-100"
				),
				day_selected: "bg-green-500 text-primary-foreground hover:bg-green-600 hover:text-primary-foreground focus:bg-green-500 focus:text-primary-foreground",
				day_today: "bg-accent text-accent-foreground",
				day_outside: "day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",
				day_disabled: "text-muted-foreground opacity-50",
				day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
				day_hidden: "invisible",
				...classNames,
			}}
			components={{
				IconLeft: () => <ChevronLeftIcon className="h-4 w-4" />,
				IconRight: () => <ChevronRightIcon className="h-4 w-4" />,
				Caption: ({ displayMonth }) => (
					<div className="flex justify-between items-center w-full">
						<Button
							variant="outline"
							className="h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
							onClick={(e) => {
								e.stopPropagation();
								const prevMonth = new Date(displayMonth);
								prevMonth.setMonth(prevMonth.getMonth() - 1);
								setMonth(prevMonth);
							}}
						>
							<ChevronLeftIcon className="h-4 w-4" />
						</Button>

						<div className="flex justify-center items-center space-x-2">
							<select
								value={displayMonth.getMonth()}
								onChange={(e) => setMonth(new Date(displayMonth.getFullYear(), Number(e.target.value), 1))}
								className="border rounded-md px-2 py-1 text-sm"
							>
								{months.map((m, i) => (
									<option key={i} value={i}>
										{m}
									</option>
								))}
							</select>

							<select
								value={displayMonth.getFullYear()}
								onChange={(e) => setMonth(new Date(Number(e.target.value), displayMonth.getMonth(), 1))}
								className="border rounded-md px-2 py-1 text-sm"
							>
								{years.map((y) => (
									<option key={y} value={y}>
										{y}
									</option>
								))}
							</select>
						</div>

						<Button
							variant="outline"
							className="h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
							onClick={(e) => {
								e.stopPropagation();
								const nextMonth = new Date(displayMonth);
								nextMonth.setMonth(nextMonth.getMonth() + 1);
								setMonth(nextMonth);
							}}
						>
							<ChevronRightIcon className="h-4 w-4" />
						</Button>
					</div>
				)
			}}
			// components={{
			// 	IconLeft: () => <ChevronLeftIcon className="h-4 w-4" />,
			// 	IconRight: () => <ChevronRightIcon className="h-4 w-4" />,
			// }}
			modifiersClassNames={{
				selected: "bg-primary text-white",
				today: "font-bold underline",
			}}
			// 🔥 Removendo o captionLayout para não exibir os selects duplicados!
			// captionLayout="dropdown-buttons" ❌ REMOVIDO
			// Mantendo apenas os selects no rodapé
			// caption={
			// 	<div className="flex justify-center items-center space-x-2 mt-2">
			// 		<select
			// 			value={month.getMonth()}
			// 			onChange={(e) => setMonth(new Date(month.getFullYear(), Number(e.target.value), 1))}
			// 			className="border rounded-md px-2 py-1 text-sm"
			// 		>
			// 			{months.map((m, i) => (
			// 				<option key={i} value={i}>
			// 					{m}
			// 				</option>
			// 			))}
			// 		</select>

			// 		<select
			// 			value={month.getFullYear()}
			// 			onChange={(e) => setMonth(new Date(Number(e.target.value), month.getMonth(), 1))}
			// 			className="border rounded-md px-2 py-1 text-sm"
			// 		>
			// 			{years.map((y) => (
			// 				<option key={y} value={y}>
			// 					{y}
			// 				</option>
			// 			))}
			// 		</select>
			// 	</div>
			// }
			{...props}
		/>
	);
}
Calendar.displayName = "Calendar";

export { Calendar };
