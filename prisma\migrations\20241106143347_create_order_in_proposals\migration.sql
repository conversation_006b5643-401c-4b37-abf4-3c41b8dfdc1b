/*
  Warnings:

  - You are about to drop the column `status` on the `Proposal` table. All the data in the column will be lost.
  - You are about to drop the `_CustomerToProposal` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `updatedAt` to the `Contact` table without a default value. This is not possible if the table is not empty.
  - Added the required column `customerId` to the `Proposal` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `Proposal` table without a default value. This is not possible if the table is not empty.
  - Changed the type of `budget` on the `Proposal` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Added the required column `updatedAt` to the `ServiceScope` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "StatusProjeto" AS ENUM ('PRE_PROJETO', 'EM_ANALISE', 'APROVADO', 'AGUARDANDO_ASSINATURA', 'AGUAR<PERSON>NDO_INICIO', 'EM_ANDAMENTO', 'FINALIZADO', 'CANCELADO');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "ProposalSituation" AS ENUM ('NEW', 'UNDER_ANALYSIS', 'PROPOSAL_SENT', 'PROPOSAL_ACCEPTED', 'SIGN_REQUESTED', 'SIGNED', 'PROJECT_REALIZATION', 'LOST');

-- DropForeignKey
ALTER TABLE "_CustomerToProposal" DROP CONSTRAINT "_CustomerToProposal_A_fkey";

-- DropForeignKey
ALTER TABLE "_CustomerToProposal" DROP CONSTRAINT "_CustomerToProposal_B_fkey";

-- AlterTable
ALTER TABLE "Contact" ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "Proposal" DROP COLUMN "status",
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "customService" TEXT,
ADD COLUMN     "customerId" TEXT NOT NULL,
ADD COLUMN     "ordem" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "situation" "ProposalSituation" NOT NULL DEFAULT 'NEW',
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL,
DROP COLUMN "budget",
ADD COLUMN     "budget" DECIMAL(65,30) NOT NULL;

-- AlterTable
ALTER TABLE "ServiceScope" ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL;

-- DropTable
DROP TABLE "_CustomerToProposal";

-- CreateTable
CREATE TABLE "PlanningFrequencyItem" (
    "id" TEXT NOT NULL,
    "order" INTEGER NOT NULL DEFAULT 0,
    "content" TEXT NOT NULL,
    "proposalId" TEXT NOT NULL,

    CONSTRAINT "PlanningFrequencyItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Cliente" (
    "id" SERIAL NOT NULL,
    "nome" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "telefone" TEXT NOT NULL,

    CONSTRAINT "Cliente_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Projeto" (
    "id" SERIAL NOT NULL,
    "nome" TEXT NOT NULL,
    "orcamento" DOUBLE PRECISION,
    "dataInicio" TIMESTAMP(3) NOT NULL,
    "status" "StatusProjeto" NOT NULL,
    "ordem" INTEGER NOT NULL DEFAULT 0,
    "clienteId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Projeto_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Proposal" ADD CONSTRAINT "Proposal_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "Customer"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PlanningFrequencyItem" ADD CONSTRAINT "PlanningFrequencyItem_proposalId_fkey" FOREIGN KEY ("proposalId") REFERENCES "Proposal"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Projeto" ADD CONSTRAINT "Projeto_clienteId_fkey" FOREIGN KEY ("clienteId") REFERENCES "Cliente"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
