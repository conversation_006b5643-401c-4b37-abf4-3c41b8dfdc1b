import { ResourceControlInterface } from "../utils";
import { Planning, ProposalRelationInterface } from "./proposal";
import { ServicesScopeRelationInterface } from "./services-scope";

export interface RepairBudgetInterface {
  measurementDate: string | Date;
  startDate?: string | Date;
  endDate?: string | Date;
  gravity: number;
  urgency: number;
  tendency: number;
  gut: number;
  serviceCost: number;
  totalCost: number;
  financialWeight: number;
  igrf: number;
  equipmentDescription: string;
  laborAmount?: number;
  equipmentAmount?: number;
  planningFrequencyItems: Planning[];
  laborEquipament?: any;
}

export type RepairBudget = RepairBudgetInterface &
  ResourceControlInterface &
  ProposalRelationInterface &
  ServicesScopeRelationInterface;
