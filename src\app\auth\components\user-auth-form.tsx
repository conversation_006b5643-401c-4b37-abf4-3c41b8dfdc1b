"use client";

import { <PERSON><PERSON> } from "@/src/components/ui/button";
import GoogleIcon from "@public/icons/google.svg";

import { cn } from "@/lib/utils";
import { signIn } from "next-auth/react";

import Image from "next/image";

export function UserAuthForm() {
	return (
		<div className="relative">
			<div className={cn("grid gap-6")}>

				<Button
					className="h-[60px]"
					variant="outline"
					type="button"
					onClick={() => signIn("google")}
				>
					<Image alt="Google" width={20} height={20} src={GoogleIcon} />
					<div className="ml-2">Login com Google</div>
				</Button>
			</div>
		</div>
	);
}
