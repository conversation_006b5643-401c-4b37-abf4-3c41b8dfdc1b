"use client";

import { <PERSON><PERSON> } from "@/src/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog";
import { Checkbox } from "@/src/components/ui/checkbox";
import { Label } from "@/src/components/ui/label";
import { useState } from "react";
import { DateRange } from "react-day-picker";
import { useToast } from "@/src/hooks/use-toast";
import { FileText, Loader2 } from "lucide-react";

interface ExportPdfDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  dateRange?: DateRange;
}

export default function ExportPdfDialog({
  open,
  onOpenChange,
  dateRange,
}: ExportPdfDialogProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [selectedCharts, setSelectedCharts] = useState({
    revenue: true,
    squareMeter: true,
    conversionRate: true,
    contractsCount: true,
    statusDistribution: true,
  });

  const handleExport = async () => {
    try {
      setLoading(true);

      // Simulação de exportação para teste
      setTimeout(() => {
        toast({
          title: "Sucesso",
          description: "PDF exportado com sucesso!",
          variant: "default"
        });
        onOpenChange(false);
        setLoading(false);
      }, 1500);

    } catch (error) {
      console.error("Erro ao exportar PDF:", error);
      toast({
        title: "Erro",
        description: "Erro ao exportar PDF. Tente novamente.",
        variant: "destructive"
      });
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-blue-700 flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Exportar PDF
          </DialogTitle>
          <DialogDescription className="text-blue-600">
            Selecione os gráficos que deseja incluir no PDF.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-100 mb-4">
            <h3 className="font-medium text-blue-800 mb-2">Período do relatório</h3>
            <p className="text-sm text-blue-700">
              {dateRange?.from && dateRange?.to
                ? `${dateRange.from.toLocaleDateString()} a ${dateRange.to.toLocaleDateString()}`
                : "Todo o período disponível"}
            </p>
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="revenue-pdf"
                checked={selectedCharts.revenue}
                onCheckedChange={(checked) =>
                  setSelectedCharts({ ...selectedCharts, revenue: !!checked })
                }
                className="border-blue-400 data-[state=checked]:bg-blue-600 data-[state=checked]:text-white"
              />
              <Label htmlFor="revenue-pdf" className="font-medium">
                Faturamento Mensal
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="squareMeter-pdf"
                checked={selectedCharts.squareMeter}
                onCheckedChange={(checked) =>
                  setSelectedCharts({ ...selectedCharts, squareMeter: !!checked })
                }
                className="border-blue-400 data-[state=checked]:bg-blue-600 data-[state=checked]:text-white"
              />
              <Label htmlFor="squareMeter-pdf" className="font-medium">
                Valor por Metro Quadrado
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="conversionRate-pdf"
                checked={selectedCharts.conversionRate}
                onCheckedChange={(checked) =>
                  setSelectedCharts({
                    ...selectedCharts,
                    conversionRate: !!checked,
                  })
                }
                className="border-blue-400 data-[state=checked]:bg-blue-600 data-[state=checked]:text-white"
              />
              <Label htmlFor="conversionRate-pdf" className="font-medium">
                Taxa de Conversão de Propostas
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="contractsCount-pdf"
                checked={selectedCharts.contractsCount}
                onCheckedChange={(checked) =>
                  setSelectedCharts({
                    ...selectedCharts,
                    contractsCount: !!checked,
                  })
                }
                className="border-blue-400 data-[state=checked]:bg-blue-600 data-[state=checked]:text-white"
              />
              <Label htmlFor="contractsCount-pdf" className="font-medium">
                Quantidade de Contratos
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="statusDistribution-pdf"
                checked={selectedCharts.statusDistribution}
                onCheckedChange={(checked) =>
                  setSelectedCharts({
                    ...selectedCharts,
                    statusDistribution: !!checked,
                  })
                }
                className="border-blue-400 data-[state=checked]:bg-blue-600 data-[state=checked]:text-white"
              />
              <Label htmlFor="statusDistribution-pdf" className="font-medium">
                Distribuição por Status
              </Label>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="border-gray-300"
          >
            Cancelar
          </Button>
          <Button
            onClick={handleExport}
            disabled={loading || !Object.values(selectedCharts).some(Boolean)}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Exportando...
              </>
            ) : (
              "Exportar PDF"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
