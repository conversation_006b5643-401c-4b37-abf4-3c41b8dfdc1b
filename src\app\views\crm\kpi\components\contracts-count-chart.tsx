"use client";

import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/src/components/ui/chart";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card";
import { useEffect, useState, useMemo } from "react";
import { Bar, BarChart, CartesianGrid, XAxis, YAxis } from "recharts";
import { DateRange } from "react-day-picker";

interface ContractsCountChartProps {
  data: any[];
  dateRange?: DateRange;
  isMobile?: boolean;
}

export default function ContractsCountChart({ data, dateRange, isMobile = false }: ContractsCountChartProps) {
  const [maxCount, setMaxCount] = useState<number>(0);

  const chartConfig: ChartConfig = {
    contractsCount: {
      label: "Contratos",
      color: "rgb(14 165 233)",
    },
  };

  // Filtrar dados com base no intervalo de datas selecionado
  const filteredData = useMemo(() => {
    if (!data) return [];

    // Se não houver intervalo de datas, mostrar todos os dados
    if (!dateRange?.from && !dateRange?.to) return data;

    return data.filter(item => {
      const itemDate = new Date(item.year, item.month - 1, 15); // Dia 15 do mês para evitar problemas com dias no fim/início do mês

      if (dateRange.from && dateRange.to) {
        return itemDate >= dateRange.from && itemDate <= dateRange.to;
      } else if (dateRange.from) {
        return itemDate >= dateRange.from;
      } else if (dateRange.to) {
        return itemDate <= dateRange.to;
      }

      return true;
    });
  }, [data, dateRange]);

  // Determinar o intervalo adequado para os rótulos do eixo X
  const xAxisInterval = useMemo(() => {
    // Se tivermos muitos pontos de dados, aumentamos o intervalo
    if (filteredData.length > 6) {
      return Math.ceil(filteredData.length / 6);
    }
    // Para dispositivos móveis, sempre mostramos menos rótulos
    return isMobile ? 1 : 0;
  }, [filteredData.length, isMobile]);

  useEffect(() => {
    if (filteredData && filteredData.length > 0) {
      const maxVal = Math.max(...filteredData.map((item: any) => item.contractsCount));
      setMaxCount(maxVal);
    }
  }, [filteredData]);

  // Calcular o total de contratos apenas com os dados filtrados
  const totalContracts = filteredData?.reduce((sum, item) => sum + item.contractsCount, 0) || 0;

  // Determinar o título com base no intervalo de datas
  const chartTitle = useMemo(() => {
    if (!dateRange?.from && !dateRange?.to) return "Quantidade de Contratos";

    if (dateRange.from && dateRange.to) {
      // Se o intervalo está no mesmo ano, mostrar apenas o ano
      if (dateRange.from.getFullYear() === dateRange.to.getFullYear()) {
        return `Quantidade de Contratos ${dateRange.from.getFullYear()}`;
      }
      // Se o intervalo abrange anos diferentes, mostrar o intervalo de anos
      return `Quantidade de Contratos ${dateRange.from.getFullYear()} - ${dateRange.to.getFullYear()}`;
    }

    if (dateRange.from) return `Quantidade de Contratos desde ${dateRange.from.getFullYear()}`;
    if (dateRange.to) return `Quantidade de Contratos até ${dateRange.to.getFullYear()}`;

    return "Quantidade de Contratos";
  }, [dateRange]);

  return (
    <Card className="w-full" id="contracts-count-chart">
      <CardHeader className="pb-2">
        <CardTitle className={`${isMobile ? 'text-lg' : 'text-xl'} font-bold`}>{chartTitle}</CardTitle>
        <CardDescription className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
          <span className="text-xs sm:text-sm">Número de contratos por mês no período selecionado</span>
          <span className="text-base sm:text-lg font-semibold text-sky-600 mt-1 sm:mt-0">
            {totalContracts}
          </span>
        </CardDescription>
      </CardHeader>
      <CardContent className={`${isMobile ? 'h-60' : 'h-80'}`}>
        {filteredData.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <p className="text-gray-500">Não há dados disponíveis para o período selecionado</p>
          </div>
        ) : (
          <ChartContainer
            config={chartConfig}
            className="h-full w-full"
          >
            <BarChart
              data={filteredData}
              barGap={0}
              barCategoryGap={isMobile ? 5 : 10}
              margin={isMobile ? { top: 5, right: 5, left: 0, bottom: 5 } : { top: 10, right: 30, left: 0, bottom: 5 }}
            >
              <CartesianGrid vertical={false} strokeDasharray="3 3" />
              <XAxis
                dataKey="monthYear"
                tickLine={false}
                tickMargin={isMobile ? 5 : 10}
                axisLine={true}
                tick={{ fontSize: isMobile ? 10 : 12 }}
                interval={xAxisInterval}
              />
              <YAxis
                type="number"
                tickLine={true}
                axisLine={true}
                tickMargin={isMobile ? 5 : 10}
                width={isMobile ? 30 : 50}
                domain={[0, maxCount * 1.2]}
                allowDecimals={false}
                tick={{ fontSize: isMobile ? 10 : 12 }}
              />
              <ChartTooltip content={<ChartTooltipContent />} cursor={false} />
              <ChartLegend content={<ChartLegendContent />} />
              <Bar
                dataKey="contractsCount"
                name="Contratos"
                fill="var(--color-contractsCount)"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
}
