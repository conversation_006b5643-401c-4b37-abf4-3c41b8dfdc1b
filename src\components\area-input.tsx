import React from 'react';
import { NumericFormat } from 'react-number-format';
import { Label } from './ui/label';

interface AreaInputProps {
  value: number | undefined;
  onChange: (value: number) => void;
  label?: string;
  placeholder?: string;
  name?: string;
  required?: boolean;
  hideErrorMessage?: boolean;
  disabled?: boolean;
}

export function AreaInput({
  value,
  onChange,
  label,
  name,
  required = false,
  disabled = false,
  placeholder = '0,00',
}: AreaInputProps) {
  const handleValueChange = (values: any) => {
    // Converte para número quando o onChange é chamado
    onChange(values.floatValue || 0);
  };

  return (
    <div className="grid gap-2">
      {label && (
        <div className="flex mb-2">
          <Label htmlFor={name} className="font-bold text-gray-700">
            {label} {required && <span className="text-red-500">*</span>}
          </Label>
        </div>
      )}
      <div className="relative">
        <NumericFormat
          id={name}
          name={name}
          value={value}
          thousandSeparator="."
          decimalSeparator=","
          decimalScale={2}
          fixedDecimalScale
          placeholder={placeholder}
          onValueChange={handleValueChange}
          disabled={disabled}
          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pr-8"
        />
        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
          m²
        </span>
      </div>
    </div>
  );
}
