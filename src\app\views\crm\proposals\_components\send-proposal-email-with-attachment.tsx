"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/src/components/ui/button";
import { toast } from "@/src/hooks/use-toast";
import { Loader2, Send } from "lucide-react";
import { Proposal } from "@/src/types/core/proposal";
import { findCustomer } from "@/src/actions/customers";

interface SendProposalEmailWithAttachmentProps {
  proposal: Proposal;
  customerId: string;
  fileEditorId?: string;
  onSuccess?: () => void;
  variant?: "default" | "outline" | "secondary" | "destructive" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  disabled?: boolean;
  isLoading?: boolean;
  isResend?: boolean;
  onSendingChange?: (isSending: boolean) => void; // Callback para notificar o componente pai sobre o estado de envio
  onClick?: () => void; // Callback personalizado para o clique do botão
}

export function SendProposalEmailWithAttachment({
  proposal,
  customerId,
  fileEditorId,
  onSuccess,
  variant = "default",
  size = "default",
  className,
  disabled = false,
  isLoading = false,
  isResend = false,
  onSendingChange,
  onClick,
}: SendProposalEmailWithAttachmentProps) {
  const [isSending, setIsSending] = useState(false);

  // Notificar o componente pai quando o estado de envio mudar
  useEffect(() => {
    if (onSendingChange) {
      onSendingChange(isSending);
    }
  }, [isSending, onSendingChange]);

  const handleSendEmail = async () => {
    console.log('=== INÍCIO DO PROCESSO DE ENVIO DE EMAIL DA PROPOSTA ===');
    console.log('Dados da proposta:', {
      id: proposal.id,
      name: proposal.name,
      customerId,
      fileEditorId
    });

    if (!customerId) {
      console.log('Erro: Cliente não encontrado');
      toast({
        title: "Erro",
        description: "Cliente não encontrado.",
        variant: "destructive",
      });
      return;
    }

    if (!fileEditorId) {
      console.log('Erro: Arquivo da proposta não encontrado');
      toast({
        title: "Erro",
        description: "Arquivo da proposta não encontrado.",
        variant: "destructive",
      });
      return;
    }

    setIsSending(true);
    console.log('Estado de envio definido como true');

    try {
      // Buscar informações do cliente
      console.log('Buscando informações do cliente com ID:', customerId);
      const customer = await findCustomer(customerId);
      console.log('Informações do cliente obtidas:', {
        id: customer?.id,
        name: customer?.name,
        email: customer?.email
      });

      if (!customer || !customer.email) {
        console.log('Erro: Cliente não possui email cadastrado');
        toast({
          title: "Erro",
          description: "O cliente não possui um email cadastrado.",
          variant: "destructive",
        });
        setIsSending(false);
        return;
      }

      // Construir o link para a proposta
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || window.location.origin;

      // Usar o fileEditorId para construir o link para o documento
      // A rota correta para visualizar documentos é /document-editor/[fileEditorId]
      const proposalLink = `${baseUrl}/document-editor/${fileEditorId}`;
      console.log('Link da proposta gerado:', proposalLink);

      // Obter o arquivo da proposta
      // Primeiro, precisamos obter a key do arquivo a partir do fileEditorId
      console.log('Buscando informações do arquivo com ID:', fileEditorId);

      let fileBlob: Blob;

      try {
        // Buscar o arquivo pelo ID para obter a key
        const fileInfoResponse = await fetch(`/api/file-editor-info?id=${fileEditorId}`);

        if (!fileInfoResponse.ok) {
          console.error('Erro ao obter informações do arquivo. Status:', fileInfoResponse.status);
          throw new Error("Não foi possível obter informações do arquivo da proposta");
        }

        const fileInfo = await fileInfoResponse.json();
        console.log('Informações do arquivo obtidas:', fileInfo);

        if (!fileInfo || !fileInfo.key) {
          console.error('Arquivo não encontrado ou key não disponível');
          throw new Error("Arquivo da proposta não encontrado");
        }

        // Agora podemos obter o arquivo usando a key
        const fileUrl = `/api/file-editor/${fileInfo.key}`;
        console.log('URL do arquivo da proposta:', fileUrl);

        // Fazer o download do arquivo
        console.log('Iniciando download do arquivo...');
        const fileResponse = await fetch(fileUrl);
        console.log('Resposta do download:', {
          status: fileResponse.status,
          statusText: fileResponse.statusText,
          ok: fileResponse.ok
        });

        if (!fileResponse.ok) {
          console.error('Erro ao obter arquivo da proposta. Status:', fileResponse.status);
          throw new Error("Não foi possível obter o arquivo da proposta");
        }

        fileBlob = await fileResponse.blob();
        console.log('Arquivo obtido com sucesso. Tamanho:', fileBlob.size, 'bytes, tipo:', fileBlob.type);

        // Independentemente do tipo MIME original, vamos forçar o tipo MIME para DOCX
        console.log('Tipo MIME original do arquivo:', fileBlob.type);

        // Criar um novo Blob com o tipo MIME para DOCX
        const arrayBuffer = await fileBlob.arrayBuffer();
        fileBlob = new Blob([arrayBuffer], {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        });

        console.log('Tipo MIME forçado para DOCX:', fileBlob.type);
      } catch (error) {
        console.error('Erro ao buscar arquivo da proposta:', error);
        throw new Error("Não foi possível obter o arquivo da proposta: " + (error instanceof Error ? error.message : String(error)));
      }

      const fileName = proposal.name.replace(/[^a-zA-Z0-9]/g, '_') + '.docx';
      console.log('Nome do arquivo gerado:', fileName);

      // Determinar o tipo MIME correto
      // Como sabemos que queremos enviar como DOCX, vamos definir o tipo MIME diretamente
      const mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      console.log('Tipo MIME definido para DOCX:', mimeType);

      // Verificar se o tipo MIME original do arquivo é diferente
      if (fileBlob.type && fileBlob.type !== mimeType && fileBlob.type !== 'application/octet-stream') {
        console.log('Aviso: O tipo MIME original do arquivo é diferente:', fileBlob.type);
        console.log('Estamos forçando o envio como DOCX conforme solicitado.');
      }

      // Criar um objeto File a partir do Blob com o tipo MIME correto
      const file = new File([fileBlob], fileName, { type: mimeType });
      console.log('Objeto File criado com sucesso:', {
        name: file.name,
        type: file.type,
        size: file.size,
        lastModified: new Date(file.lastModified).toISOString()
      });

      // Determinar se é um contrato ou uma proposta com base na situação
      const isContract = ['PROPOSAL_ACCEPTED', 'SIGN_REQUESTED', 'SIGNED', 'PROJECT_IN_PROGRESS', 'PROJECT_FINISHED'].includes(proposal.situation);
      console.log(`Tipo de documento: ${isContract ? 'Contrato' : 'Proposta'}`);

      // Converter o arquivo para um objeto FormData
      console.log('Criando FormData para envio...');
      const formData = new FormData();
      formData.append('type', isContract ? 'contract' : 'proposal');
      formData.append('to', customer.email);
      formData.append('proposalName', proposal.name);
      formData.append('proposalLink', proposalLink);
      formData.append('customerName', customer.name);
      formData.append('file', file);
      formData.append('isContract', isContract.toString());
      console.log('FormData criado com sucesso');

      // Enviar o email com o anexo
      console.log('Enviando requisição para API de email...');
      const response = await fetch('/api/email-with-attachment', {
        method: 'POST',
        body: formData,
      });

      console.log('Resposta da API recebida:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });

      const result = await response.json();
      console.log('Resultado da API:', result);

      if (response.ok && result.success) {
        console.log('Email enviado com sucesso! MessageId:', result.messageId);
        toast({
          title: "Sucesso",
          description: "Proposta enviada com sucesso para o cliente.",
        });

        // Chamar o callback de sucesso, se fornecido
        if (onSuccess) {
          console.log('Chamando callback de sucesso...');
          onSuccess();
        }
      } else {
        console.error('Erro retornado pela API:', result.error, 'Detalhes:', result.details);
        toast({
          title: "Erro",
          description: result.error || "Falha ao enviar a proposta. Tente novamente.",
          variant: "destructive",
        });
      }
    } catch (error: unknown) {
      console.error('=== ERRO AO ENVIAR PROPOSTA POR EMAIL ===');
      console.error("Erro ao enviar proposta por email:", error);

      // Tratamento seguro do erro
      const errorMessage = error instanceof Error ? error.message : String(error);

      console.error('Detalhes do erro:', {
        name: error instanceof Error ? error.name : 'UnknownError',
        message: errorMessage,
        stack: error instanceof Error ? error.stack : 'No stack trace available'
      });

      toast({
        title: "Erro",
        description: `Ocorreu um erro ao enviar a proposta por email: ${errorMessage}`,
        variant: "destructive",
      });
    } finally {
      setIsSending(false);
      console.log('Estado de envio definido como false');
      console.log('=== FIM DO PROCESSO DE ENVIO DE EMAIL DA PROPOSTA ===');
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={onClick || handleSendEmail}
      disabled={disabled || isSending || isLoading}
      className={className}
    >
      {isSending || isLoading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          {(() => {
            // Determinar se é um contrato ou uma proposta com base na situação
            const isContract = ['PROPOSAL_ACCEPTED', 'SIGN_REQUESTED', 'SIGNED', 'PROJECT_IN_PROGRESS', 'PROJECT_FINISHED'].includes(proposal.situation);

            if (isResend) {
              return isContract ? "Reenviando contrato..." : "Reenviando proposta...";
            } else {
              return isContract ? "Enviando contrato..." : "Enviando proposta...";
            }
          })()}
        </>
      ) : (
        <>
          <Send className="mr-2 h-4 w-4" />
          {(() => {
            // Determinar se é um contrato ou uma proposta com base na situação
            const isContract = ['PROPOSAL_ACCEPTED', 'SIGN_REQUESTED', 'SIGNED', 'PROJECT_IN_PROGRESS', 'PROJECT_FINISHED'].includes(proposal.situation);

            if (isResend) {
              return isContract ? "Reenviar contrato" : "Reenviar proposta";
            } else {
              return isContract ? "Enviar contrato" : "Enviar proposta";
            }
          })()}
        </>
      )}
    </Button>
  );
}
