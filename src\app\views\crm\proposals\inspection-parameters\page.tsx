"use client";
import ContentWrapper from "@/src/components/content-wrapper";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import InspectionParametersTable, { InspectionParametersTableRef } from "./_components/inspection-parameters-table";

export default function ProposalInspectionParameters() {
	const [proposalId, setProposalId] = useState<string | null>(null);
	const tableRef = useRef<InspectionParametersTableRef>(null);
	const searchParams = useSearchParams();
	const router = useRouter();

	useEffect(() => {
		const proposalId = searchParams.get("id");
		if (!proposalId) {
			router.push("/views/crm/proposals/accepted");
			return;
		}
		setProposalId(proposalId);
	}, [searchParams, router]);

	const handleAddClick = () => {
		router.push(
			`/views/crm/proposals/inspection-parameters/form?proposalId=${proposalId}`
		);
	};

	return (
		<ContentWrapper title="Parâmetros de Inspeção">
			<InspectionParametersTable
				ref={tableRef}
				proposalId={proposalId}
				onAddClick={handleAddClick}
			/>
		</ContentWrapper>
	);
}
