"use client";

import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { TableGrid } from "@/src/components/ui/table-grid";
import { useToast } from "@/src/hooks/use-toast";
import { Customer } from "@/src/types/core/customer";
import { Proposal, ProposalFilters } from "@/src/types/core/proposal";
import { Eraser } from "lucide-react";
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from "react";

interface ConstructionInspectionTableProps {
  columns: any[];
  onViewInspectionsClick: (proposalId: string) => void; // Usado apenas na interface, implementado no componente pai
  onPageChange?: (page: number) => void;
  customers: Customer[]; // Usado apenas na interface, para tipagem
}

export type ConstructionInspectionTableRef = {
  refresh: (page?: number, filters?: ProposalFilters) => void;
};

const ConstructionInspectionTable = forwardRef<ConstructionInspectionTableRef, ConstructionInspectionTableProps>(
  function ConstructionInspectionTable(props, ref) {
    // Extrair apenas as props que são realmente utilizadas
    const { columns, onPageChange } = props;
    const [data, setData] = useState<Proposal[]>([]);
    const [loading, setLoading] = useState(false);
    const [search, setSearch] = useState("");
    const [situationFilter, setSituationFilter] = useState<string | null>(null);
    const [customerFilter, setCustomerFilter] = useState<string | null>(null);
    const [serviceTypeFilter, setServiceTypeFilter] = useState<string | null>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);
    const { toast } = useToast();
    const [pagination, setPagination] = useState({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });

    // Estado para rastrear filtros ativos
    const [activeFilters, setActiveFilters] = useState<{ [key: string]: boolean }>({});

    // Nova função para buscar propostas via API
    async function fetchProposalsAPI({
      page = 1,
      pageSize = 10,
      search = "",
      situation = null,
      customerId = null,
      serviceType = null,
    }: {
      page?: number;
      pageSize?: number;
      search?: string;
      situation?: string | null;
      customerId?: string | null;
      serviceType?: string | null;
    }) {
      const params = new URLSearchParams();
      params.set("page", String(page));
      params.set("pageSize", String(pageSize));
      if (search) params.set("search", search);
      if (customerId) params.set("customerId", customerId);
      if (situation) {
        params.append("situation", situation);
      } else {
        // Situações padrão: apenas PROJECT_IN_PROGRESS e PROJECT_FINISHED
        params.append("situation", "PROJECT_IN_PROGRESS");
        params.append("situation", "PROJECT_FINISHED");
      }
      // Filtro de tipo de serviço: se não houver filtro, sempre enviar FISCALIZACAO e GERENCIAMENTO
      if (serviceType) {
        params.append("serviceType", serviceType);
      } else {
        params.append("serviceType", "FISCALIZACAO");
        params.append("serviceType", "GERENCIAMENTO");
      }
      // Adiciona o filtro para propostas com inspectionParameters
      params.set("onlyWithInspectionParameters", "true");
      const res = await fetch(`/api/proposals?${params.toString()}`);
      if (!res.ok) throw new Error("Erro ao buscar propostas");
      const json = await res.json();
      return {
        data: json.items || [],
        pagination: {
          total: json.total,
          page: json.page,
          pageSize: json.pageSize,
          totalPages: json.totalPages,
        },
      };
    }

    const fetchProposals = useCallback(async (
      page = 1,
      pageSize = 10,
      searchTerm = "",
      situationValue: string | null = null,
      customerValue: string | null = null,
      serviceTypeValue: string | null = null
    ) => {
      try {
        setLoading(true);
        // Buscar via API
        const result = await fetchProposalsAPI({
          page,
          pageSize,
          search: searchTerm,
          situation: situationValue,
          customerId: customerValue,
          serviceType: serviceTypeValue,
        });
        if (result) {
          const proposalArray = (Array.isArray(result.data) ? result.data : []).map((p) => ({
            ...p,
            startDate: p.startDate ?? "",
            endDate: p.endDate ?? "",
            area: typeof p.area === "number" ? p.area : Number(p.area ?? 0),
            budget: typeof p.budget === "number" ? p.budget : Number(p.budget ?? 0),
            workTotalCost: p.workTotalCost == null ? undefined : (typeof p.workTotalCost === "number" ? p.workTotalCost : Number(p.workTotalCost)),
            downPayment: p.downPayment == null ? undefined : (typeof p.downPayment === "number" ? p.downPayment : Number(p.downPayment)),
            installmentAmount: p.installmentAmount == null ? undefined : (typeof p.installmentAmount === "number" ? p.installmentAmount : Number(p.installmentAmount)),
            installmentNumber: p.installmentNumber == null ? undefined : (typeof p.installmentNumber === "number" ? p.installmentNumber : Number(p.installmentNumber)),
            customService: p.customService == null ? undefined : p.customService,
            cep: p.cep == null ? undefined : p.cep,
            address: p.address == null ? undefined : p.address,
            city: p.city == null ? undefined : p.city,
            state: p.state == null ? undefined : p.state,
            serviceType: p.serviceType == null ? undefined : p.serviceType,
            proposalTemplateId: p.proposalTemplateId == null ? undefined : p.proposalTemplateId,
            fileEditorId: p.fileEditorId == null ? undefined : p.fileEditorId,
            fileId: p.fileId == null ? undefined : p.fileId,
          }));
          setData(proposalArray);
          setPagination(prev => ({
            ...prev,
            page: result.pagination.page,
            pageSize: result.pagination.pageSize,
            total: result.pagination.total,
            totalPages: result.pagination.totalPages,
          }));
        }
      } catch (error) {
        console.error('Fetch error:', error);
        toast({
          title: "Erro",
          description: "Erro ao carregar propostas",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }, [toast]);

    useImperativeHandle(ref, () => ({
      refresh: (page?: number, filters?: ProposalFilters) => {
        const currentPage = page || pagination.page;
        const searchTerm = filters?.search || search;
        const situationValue = filters?.situation ? filters.situation[0] as string : situationFilter;
        const customerValue = filters?.customerId || customerFilter;
        const serviceTypeValue = filters?.serviceType ? filters.serviceType[0] as string : serviceTypeFilter;

        // Atualizar os estados locais
        if (searchTerm !== search) {
          setSearch(searchTerm);
        }

        if (situationValue !== situationFilter) {
          setSituationFilter(situationValue);
          setActiveFilters(prev => ({
            ...prev,
            situation: !!situationValue
          }));
        }

        if (customerValue !== customerFilter) {
          setCustomerFilter(customerValue);
          setActiveFilters(prev => ({
            ...prev,
            customer: !!customerValue
          }));
        }

        if (serviceTypeValue !== serviceTypeFilter) {
          setServiceTypeFilter(serviceTypeValue);
          setActiveFilters(prev => ({
            ...prev,
            serviceType: !!serviceTypeValue
          }));
        }

        // Buscar propostas com os novos valores
        fetchProposals(
          currentPage,
          pagination.pageSize,
          searchTerm,
          situationValue,
          customerValue,
          serviceTypeValue
        );
      }
    }), [fetchProposals, pagination.page, pagination.pageSize, search, situationFilter, customerFilter, serviceTypeFilter]);

    // Carregar dados quando o componente for montado
    useEffect(() => {
      fetchProposals();
    }, [fetchProposals]);

    const handleSearch = (value: string) => {
      setSearch(value);
      fetchProposals(1, pagination.pageSize, value, situationFilter, customerFilter, serviceTypeFilter);
    };

    // Função para lidar com filtros de colunas
    const handleColumnFilter = (columnKey: string, value: any) => {
      if (columnKey === 'situation') {
        // Permitir apenas PROJECT_IN_PROGRESS e PROJECT_FINISHED
        const allowedSituations = ["PROJECT_IN_PROGRESS", "PROJECT_FINISHED"];
        if (!allowedSituations.includes(value)) {
          // Ignora valores não permitidos
          return;
        }
        // Atualizar o filtro de situação
        setSituationFilter(value);
        setActiveFilters(prev => ({
          ...prev,
          situation: !!value
        }));

        // Buscar dados com o novo filtro
        fetchProposals(1, pagination.pageSize, search, value, customerFilter, serviceTypeFilter);

        // Atualizar a página atual no componente pai
        if (onPageChange) onPageChange(1);
      } else if (columnKey === 'customer') {
        // Atualizar o filtro de cliente
        setCustomerFilter(value);
        setActiveFilters(prev => ({
          ...prev,
          customer: !!value
        }));

        // Buscar dados com o novo filtro
        fetchProposals(1, pagination.pageSize, search, situationFilter, value, serviceTypeFilter);

        // Atualizar a página atual no componente pai
        if (onPageChange) onPageChange(1);
      } else if (columnKey === 'serviceType') {
        // Atualizar o filtro de tipo de serviço
        setServiceTypeFilter(value);
        setActiveFilters(prev => ({
          ...prev,
          serviceType: !!value
        }));
        // Buscar dados com o novo filtro
        fetchProposals(1, pagination.pageSize, search, situationFilter, customerFilter, value);
        if (onPageChange) onPageChange(1);
      }
    };

    const handleClearFilters = () => {
      // Limpar o estado de pesquisa e filtros
      setSearch("");
      setSituationFilter(null);
      setCustomerFilter(null);
      setServiceTypeFilter(null);
      setActiveFilters({});

      // Resetar para a primeira página e buscar dados
      fetchProposals(1, pagination.pageSize, "", null, null, null);

      // Atualizar a página atual no componente pai
      if (onPageChange) onPageChange(1);

      // Focar no campo de pesquisa após limpar
      setTimeout(() => {
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }, 0);
    };

    return (
      <TableGrid
        columns={columns}
        data={data}
        loading={loading}
        pagination={pagination}
        onPaginationChange={(newPage, newPageSize) => {
          fetchProposals(newPage, newPageSize, search, situationFilter, customerFilter, serviceTypeFilter);
          if (onPageChange) onPageChange(newPage);
        }}
        onSearch={handleSearch}
        searchValue={search}
        searchInputRef={searchInputRef}
        onColumnFilter={handleColumnFilter}
        activeFilters={activeFilters}
        buttonsTemplate={
          <>
            <Button
              className="w-full sm:w-auto bg-blue-500 hover:bg-blue-400"
              onClick={handleClearFilters}
            >
              Limpar filtros <Eraser className="ml-2 h-4 w-4" />
            </Button>
          </>
        }
      />
    );
  }
);

export default ConstructionInspectionTable;



