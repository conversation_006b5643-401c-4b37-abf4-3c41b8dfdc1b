/*
  Warnings:

  - You are about to drop the column `fileName` on the `Project` table. All the data in the column will be lost.
  - You are about to drop the column `filePath` on the `Project` table. All the data in the column will be lost.

*/
-- CreateEnum
CREATE TYPE "Periodicity" AS ENUM ('WEEKLY', 'MONTHLY');

-- AlterTable
ALTER TABLE "Project" DROP COLUMN "fileName",
DROP COLUMN "filePath",
ADD COLUMN     "fileId" TEXT;

-- AlterTable
ALTER TABLE "ServiceScope" ADD COLUMN     "proposalId" TEXT;

-- CreateTable
CREATE TABLE "File" (
    "id" TEXT NOT NULL,
    "path" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "uploadedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "File_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Proposal" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "budget" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "paymentCondition" TEXT NOT NULL,
    "periodicity" "Periodicity" NOT NULL,

    CONSTRAINT "Proposal_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_CustomerToProposal" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "_CustomerToProposal_AB_unique" ON "_CustomerToProposal"("A", "B");

-- CreateIndex
CREATE INDEX "_CustomerToProposal_B_index" ON "_CustomerToProposal"("B");

-- CreateIndex
CREATE INDEX "Customer_email_idx" ON "Customer"("email");

-- CreateIndex
CREATE INDEX "ServiceScope_proposalId_idx" ON "ServiceScope"("proposalId");

-- AddForeignKey
ALTER TABLE "Project" ADD CONSTRAINT "Project_fileId_fkey" FOREIGN KEY ("fileId") REFERENCES "File"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ServiceScope" ADD CONSTRAINT "ServiceScope_proposalId_fkey" FOREIGN KEY ("proposalId") REFERENCES "Proposal"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CustomerToProposal" ADD CONSTRAINT "_CustomerToProposal_A_fkey" FOREIGN KEY ("A") REFERENCES "Customer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CustomerToProposal" ADD CONSTRAINT "_CustomerToProposal_B_fkey" FOREIGN KEY ("B") REFERENCES "Proposal"("id") ON DELETE CASCADE ON UPDATE CASCADE;
