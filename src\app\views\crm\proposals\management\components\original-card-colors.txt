// Cores originais dos cards
switch(columnType) {
  case 'NEW': return {
    bg: 'bg-blue-50',
    hoverBg: 'hover:bg-blue-100',
    border: 'border-blue-200',
    shadow: 'shadow-blue-100',
    accent: 'bg-blue-500',
    text: 'text-blue-600',
    badge: 'status-badge-new'
  };
  case 'UNDER_ANALYSIS': return {
    bg: 'bg-purple-50',
    hoverBg: 'hover:bg-purple-100',
    border: 'border-purple-200',
    shadow: 'shadow-purple-100',
    accent: 'bg-purple-500',
    text: 'text-purple-600',
    badge: 'status-badge-analysis'
  };
  case 'PROPOSAL_SENT': return {
    bg: 'bg-indigo-50',
    hoverBg: 'hover:bg-indigo-100',
    border: 'border-indigo-200',
    shadow: 'shadow-indigo-100',
    accent: 'bg-indigo-500',
    text: 'text-indigo-600',
    badge: 'status-badge-sent'
  };
  case 'PROPOSAL_ACCEPTED': return {
    bg: 'bg-green-50',
    hoverBg: 'hover:bg-green-100',
    border: 'border-green-200',
    shadow: 'shadow-green-100',
    accent: 'bg-green-500',
    text: 'text-green-600',
    badge: 'status-badge-accepted'
  };
  case 'SIGN_REQUESTED': return {
    bg: 'bg-orange-50',
    hoverBg: 'hover:bg-orange-100',
    border: 'border-orange-200',
    shadow: 'shadow-orange-100',
    accent: 'bg-orange-500',
    text: 'text-orange-600',
    badge: 'status-badge-sign'
  };
  case 'SIGNED': return {
    bg: 'bg-emerald-50',
    hoverBg: 'hover:bg-emerald-100',
    border: 'border-emerald-200',
    shadow: 'shadow-emerald-100',
    accent: 'bg-emerald-500',
    text: 'text-emerald-600',
    badge: 'status-badge-signed'
  };
  case 'PROJECT_IN_PROGRESS': return {
    bg: 'bg-amber-50',
    hoverBg: 'hover:bg-amber-100',
    border: 'border-amber-200',
    shadow: 'shadow-amber-100',
    accent: 'bg-amber-500',
    text: 'text-amber-600',
    badge: 'status-badge-in-progress'
  };
  case 'PROJECT_FINISHED': return {
    bg: 'bg-teal-50',
    hoverBg: 'hover:bg-teal-100',
    border: 'border-teal-200',
    shadow: 'shadow-teal-100',
    accent: 'bg-teal-500',
    text: 'text-teal-600',
    badge: 'status-badge-finished'
  };
  case 'LOST': return {
    bg: 'bg-red-50',
    hoverBg: 'hover:bg-red-100',
    border: 'border-red-200',
    shadow: 'shadow-red-100',
    accent: 'bg-red-500',
    text: 'text-red-600',
    badge: 'status-badge-lost'
  };
  default: return {
    bg: 'bg-gray-50',
    hoverBg: 'hover:bg-gray-100',
    border: 'border-gray-200',
    shadow: 'shadow-gray-100',
    accent: 'bg-gray-500',
    text: 'text-gray-600',
    badge: ''
  };
}
