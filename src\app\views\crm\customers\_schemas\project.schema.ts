import { z } from "zod";

export const projectSchema = z.object({
  id: z.string().optional(),
  customerId: z.string().optional(),
  project: z
    .string()
    .refine((value) => value.trim().length, "Projeto é obrigatório"),
  startDate: z.date({ message: "Data de início inválida" }).optional(),
  endDate: z.date({ message: "Data de término inválida" }).optional(),
  area: z.number().min(0, "Área deve ser maior ou igual a zero").optional(),
  contractValue: z
    .number()
    .min(0, "Valor do contrato deve ser maior ou igual a zero")
    .optional(),
  serviceType: z.string().optional(),
  file: z.any().optional(), // Tornando opcional para edição
  files: z.array(z.any()).optional(), // Adicionando suporte para múltiplos arquivos
});

export type ProjectSchema = z.infer<typeof projectSchema>;
