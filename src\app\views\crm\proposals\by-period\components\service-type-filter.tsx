"use client";

import { useState, useEffect } from "react";
import { MultiSelect } from "@/components/ui/multi-select";
import "./service-type-filter.css";

interface ServiceTypeFilterProps {
  onServiceTypesChange: (serviceTypes: string[]) => void;
  className?: string;
}

export function ServiceTypeFilter({ onServiceTypesChange, className = "" }: ServiceTypeFilterProps) {
  const [selectedServiceTypes, setSelectedServiceTypes] = useState<string[]>([]);

  // Service type options
  const serviceTypeOptions = [
    { label: "Inspeção", value: "INSPECAO" },
    { label: "Fiscalização", value: "FISCALIZACAO" },
    { label: "Gerenciamento", value: "GERENCIAMENTO" },
    { label: "Consultoria", value: "CONSULTORIA" },
    { label: "Projeto", value: "PROJETO" },
  ];

  // Notify parent component when selected service types change
  useEffect(() => {
    onServiceTypesChange(selectedServiceTypes);
  }, [selectedServiceTypes, onServiceTypesChange]);

  // Função para lidar com a mudança de valores selecionados
  const handleValueChange = (values: string[]) => {
    setSelectedServiceTypes(values);
  };

  return (
    <div className={className}>
      <div
        className="service-type-filter"
        style={{
          "--placeholder-color": "#15803d",
          "--badge-text-color": "white",
          "--icon-color": "#15803d",
          "--chevron-color": "#15803d",
          "--x-icon-color": "#15803d"
        } as React.CSSProperties}
      >
        <MultiSelect
          options={serviceTypeOptions}
          onValueChange={handleValueChange}
          defaultValue={[]}
          placeholder="Filtrar por tipo"
          variant="primary"
          animation={0}
          maxCount={5}
          modalPopover={true}
          className="w-full border-green-100 bg-green-50 hover:bg-green-100 hover:border-green-200 [&_svg]:text-green-700"
        />
      </div>
    </div>
  );
}
