import AppConfirmationDialog from "@/src/components/app-confirmation-dialog";
import { CustomInput } from "@/src/components/app-input";
import { Button } from "@/src/components/ui/button";
import { Card } from "@/src/components/ui/card";
import { X } from "lucide-react";


type CardLaborEquipamentProps = {
    id: string
    title: string;
    description: string;
    nameInput: string;
    removeItem: (id: string) => void;
}
export default function CardLaborEquipament({ id, title, description, nameInput, removeItem }: CardLaborEquipamentProps) {
    return (
        <Card className="flex items-center justify-between p-2 w-90">
            <div>
                <p className="font-semibold">{title}</p>
                <p className="text-sm italic text-gray-500">{description}</p>
            </div>
            <div className="flex items-end gap-2">
                <CustomInput
                    type="number"
                    name={nameInput}
                    min={0}
                    className="max-w-[80px]"
                />
                <AppConfirmationDialog
                    title={`Excluir ${title}`}
                    description='Tem certeza que deseja deletar este serviço? A ação não poderá ser desfeita após clicar em salvar!'
                    onConfirmCallback={() => removeItem(id)}
                >
                    <Button variant="ghost" size="icon" className="text-red-500">
                        <X className="w-5 h-5" />
                    </Button>
                </AppConfirmationDialog>
            </div>
        </Card>
    )
}