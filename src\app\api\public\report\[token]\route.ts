import { NextRequest, NextResponse } from "next/server";
import { getInspectionReportData } from "@/src/actions/direct-inspection-report";
import { verifyReportToken } from "@/src/lib/report-token";
import { generateServerPdf } from "@/src/components/server-pdf-generator";

export async function GET(
  request: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const token = params.token;

    if (!token) {
      console.error("[API PUBLIC REPORT] Token não fornecido na URL do relatório público. Params:", params);
      return NextResponse.json(
        { error: "Token não fornecido na URL. O link pode estar incompleto ou corrompido." },
        { status: 400 }
      );
    }

    // Logar o token recebido para depuração
    console.log("[API PUBLIC REPORT] Token recebido:", token);

    // Verificar e decodificar o token para obter o ID do relatório
    console.log("Verificando token:", token);
    const decodedToken = verifyReportToken(token);
    console.log("Resultado da verificação:", decodedToken);

    if (!decodedToken || !decodedToken.id) {
      console.error("Token inválido ou expirado ao acessar relatório público:", { token, decodedToken });
      return NextResponse.json(
        { error: "Token inválido ou expirado. O link pode estar incorreto, expirado ou ter sido alterado." },
        { status: 401 }
      );
    }

    const inspectionId = decodedToken.id;

    // Buscar os dados do relatório
    console.log("Buscando dados do relatório para ID:", inspectionId);
    let data;
    try {
      data = await getInspectionReportData(inspectionId);
      console.log("Dados do relatório obtidos:", data ? "Sucesso" : "Nulo");
    } catch (error) {
      console.error("Erro ao buscar dados do relatório:", error);
      return NextResponse.json(
        {
          error:
            "Erro ao buscar dados do relatório: " +
            (error instanceof Error ? error.message : "Erro desconhecido"),
        },
        { status: 500 }
      );
    }

    if (!data) {
      console.error("Relatório não encontrado para ID:", inspectionId);
      return NextResponse.json(
        { error: "Relatório não encontrado. O relatório pode ter sido removido ou não existe mais." },
        { status: 404 }
      );
    }

    console.log("Estrutura dos dados do relatório:", {
      hasInspection: !!data.inspection,
      hasProposal: !!data.proposal,
      hasCustomer: !!data.customer,
      inspectionId: data.inspection?.id,
      proposalId: data.proposal?.id,
      customerId: data.customer?.id,
    });

    // Gerar o PDF usando a função do servidor
    console.log("Iniciando geração do PDF no servidor...");
    let result;
    try {
      result = await generateServerPdf(data);
      console.log("Resultado da geração do PDF no servidor:", {
        success: result.success,
        hasError: !!result.error,
        hasBuffer: !!result.buffer,
        hasFileName: !!result.fileName,
      });
    } catch (error) {
      console.error("Erro ao gerar PDF no servidor:", error);
      return NextResponse.json(
        {
          error:
            "Erro ao gerar PDF: " +
            (error instanceof Error ? error.message : "Erro desconhecido"),
        },
        { status: 500 }
      );
    }

    if (!result.success) {
      console.error("Falha na geração do PDF no servidor:", result.error);
      return NextResponse.json(
        { error: result.error || "Erro ao gerar o PDF" },
        { status: 500 }
      );
    }

    // Verificar se o buffer foi gerado
    if (!result.buffer) {
      console.error("Buffer não encontrado no resultado da geração do PDF");
      return NextResponse.json(
        { error: "Falha ao gerar o PDF: buffer não encontrado" },
        { status: 500 }
      );
    }

    console.log("Convertendo buffer para base64...");
    let base64;

    try {
      // Converter o buffer para base64
      base64 = result.buffer.toString("base64");
      console.log("Base64 criado com sucesso, tamanho:", base64.length);
    } catch (error) {
      console.error("Erro ao converter buffer para base64:", error);
      return NextResponse.json(
        {
          error:
            "Erro ao converter PDF para base64: " +
            (error instanceof Error ? error.message : "Erro desconhecido"),
        },
        { status: 500 }
      );
    }

    // Retornar os dados do PDF
    return NextResponse.json({
      success: true,
      pdfBase64: base64,
      fileName: result.fileName,
      contentType: "application/pdf",
    });
  } catch (error) {
    console.error("Erro ao processar relatório público:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor ao processar relatório público." },
      { status: 500 }
    );
  }
}
