/*
  Warnings:

  - You are about to drop the column `content` on the `ReportTemplate` table. All the data in the column will be lost.
  - You are about to drop the column `fileId` on the `ReportTemplate` table. All the data in the column will be lost.
  - Added the required column `fileEditorId` to the `ReportTemplate` table without a default value. This is not possible if the table is not empty.
  - Added the required column `type` to the `ReportTemplate` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "ReportType" AS ENUM ('REPORT', 'KPI', 'HISTOGRAM', 'CLIMATE_IMPACT');

-- DropForeignKey
ALTER TABLE "ReportTemplate" DROP CONSTRAINT "ReportTemplate_fileId_fkey";

-- AlterTable
ALTER TABLE "ReportTemplate" DROP COLUMN "content",
DROP COLUMN "fileId",
ADD COLUMN     "fileEditorId" TEXT NOT NULL,
ADD COLUMN     "type" "ReportType" NOT NULL;

-- AddF<PERSON>ignKey
ALTER TABLE "ReportTemplate" ADD CONSTRAINT "ReportTemplate_fileEditorId_fkey" FOREIGN KEY ("fileEditorId") REFERENCES "FileEditor"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
