import { ResourceControlInterface } from "../utils";
import { FileRelationInterface } from "./file";
import { ProposalRelationInterface } from "./proposal";

export interface ContractInterface {
  createdAt: string | Date;
  updatedAt: string | Date;
  fileEditorId?: string;
}

export interface ContractRelationInterface {
  contractId: string;
  contract: ContractInterface;
}

export type Contract = ContractInterface &
  Partial<ProposalRelationInterface> &
  Partial<FileRelationInterface> &
  ResourceControlInterface;
