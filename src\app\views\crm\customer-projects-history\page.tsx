"use client";

import * as React from "react";
import { useEffect, useState } from "react";
import ContentWrapper from "@/src/components/content-wrapper";
import { useToast } from "@/src/hooks/use-toast";
import { Proposal } from "@/src/types/core/proposal";
import { Search } from "lucide-react";
import { Input } from "@/src/components/ui/input";
import { Combobox, ComboboxOption } from "@/components/ui/combobox";
import ProjectCard from "./components/project-card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function CustomerProjectsHistory() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [customerOptions, setCustomerOptions] = useState<ComboboxOption[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<string>("");
  const [projects, setProjects] = useState<Proposal[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedServiceType, setSelectedServiceType] = useState<string>("ALL");

  // Função para carregar os clientes iniciais
  const fetchInitialCustomers = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/customers/search");
      if (!response.ok) {
        throw new Error("Falha ao carregar clientes");
      }
      const data = await response.json();
      // Garantir que o resultado seja sempre um array
      if (!Array.isArray(data)) {
        console.warn("API retornou dados não esperados:", data);
        setCustomerOptions([]);
      } else {
        setCustomerOptions(data);
      }
    } catch (error) {
      console.error("Erro ao carregar clientes:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar a lista de clientes",
        variant: "destructive",
      });
      setCustomerOptions([{ label: "Todos os clientes", value: "" }]);
    } finally {
      setLoading(false);
    }
  };

  // Função para buscar clientes com base no termo de pesquisa
  const searchCustomers = async (search: string): Promise<ComboboxOption[]> => {
    try {
      // Se a pesquisa estiver vazia, retornar todos os clientes
      if (!search.trim()) {
        const response = await fetch(`/api/customers/search`);
        if (!response.ok) {
          throw new Error("Falha ao buscar clientes");
        }
        const data = await response.json();
        return Array.isArray(data) ? data : [];
      }

      const response = await fetch(`/api/customers/search?search=${encodeURIComponent(search)}`);
      if (!response.ok) {
        throw new Error("Falha ao buscar clientes");
      }
      const data = await response.json();
      // Garantir que o resultado seja sempre um array
      if (!Array.isArray(data)) {
        console.warn("API retornou dados não esperados:", data);
        return [];
      }

      return data;
    } catch (error) {
      console.error("Erro ao buscar clientes:", error);
      toast({
        title: "Erro",
        description: "Não foi possível buscar clientes",
        variant: "destructive",
      });
      return [];
    }
  };

  // Função para carregar os projetos de um cliente
  const fetchCustomerProjects = async (customerId: string, serviceTypeFilter?: string) => {
    // Carregar projetos de um cliente específico
    if (!customerId) return;

    try {
      setLoading(true);
      const url = serviceTypeFilter && serviceTypeFilter !== "ALL"
        ? `/api/customer-projects/${customerId}?serviceType=${encodeURIComponent(serviceTypeFilter)}`
        : `/api/customer-projects/${customerId}`;

      console.log("Buscando projetos com URL:", url);

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error("Falha ao carregar projetos do cliente");
      }
      const data = await response.json();
      // Garantir que o resultado seja sempre um array
      if (!Array.isArray(data)) {
        console.warn("API retornou dados não esperados:", data);
        setProjects([]);
      } else {
        console.log("Projetos recebidos:", data);
        // Verificar se os projetos têm o campo serviceType
        if (data.length > 0) {
          data.forEach((project: any) => {
            console.log(`Projeto ${project.id} - serviceType:`, project.serviceType);
          });
        }
        setProjects(data);
      }
    } catch (error) {
      console.error("Erro ao carregar projetos:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os projetos deste cliente",
        variant: "destructive",
      });
      setProjects([]);
    } finally {
      setLoading(false);
    }
  };

  // Carregar clientes ao montar o componente
  useEffect(() => {
    fetchInitialCustomers();
  }, []);

  // Carregar projetos quando um cliente for selecionado ou o filtro de tipo de serviço mudar
  useEffect(() => {
    if (selectedCustomer) {
      fetchCustomerProjects(selectedCustomer, selectedServiceType);
    } else {
      setProjects([]);
    }
  }, [selectedCustomer, selectedServiceType]);

  // Filtrar projetos com base no termo de pesquisa (nome do projeto OU nome do cliente OU situação)
  const filteredProjects = React.useMemo(() => {
    if (!Array.isArray(projects)) return [];
    return projects.filter(project => {
      const matchesSearchTerm =
        (project.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
        (project.customer?.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
        (project.situation?.toLowerCase() || '').includes(searchTerm.toLowerCase());
      return matchesSearchTerm;
    });
  }, [projects, searchTerm]);

  return (
    <ContentWrapper
      title="Histórico de Projetos"
    >
      <div className="space-y-8">
        {/* Seleção de cliente */}
        <div className="flex flex-col md:flex-row justify-between items-center gap-6 p-6 mb-6 bg-gradient-to-r from-green-50 to-white rounded-lg shadow-md border border-green-100">
          <div>
            <h3 className="text-lg font-semibold mb-1 text-green-800">Filtrar projetos concluídos e anteriores</h3>
            <p className="text-sm text-green-600">Selecione um cliente para visualizar seus projetos concluídos e anteriores</p>
          </div>
          <div className="w-full md:w-[600px]">
            <Combobox
              options={customerOptions}
              value={selectedCustomer}
              onChange={setSelectedCustomer}
              placeholder="Selecione um cliente"
              searchPlaceholder="Digite o nome do cliente..."
              emptyMessage="Nenhum cliente encontrado"
              onSearch={searchCustomers}
              className="w-full text-green-700 font-medium"
              customStyles={{
                trigger: "border-green-100 bg-green-50 hover:bg-green-100 hover:border-green-200 text-green-700",
                search: "border-green-100 focus:border-green-300 focus:ring-green-200",
                searchIcon: "text-green-500",
                option: "hover:bg-green-50 hover:text-green-700 focus:bg-green-50 focus:text-green-700",
                selectedIcon: "text-green-600",
                content: "border-green-100",
                emptyMessage: "text-green-600"
              }}
            />
          </div>
        </div>

        {/* Lista de projetos */}
        {selectedCustomer && (
          <div className="bg-white p-8 rounded-xl shadow-md border border-gray-100 hover:shadow-lg transition-shadow duration-300">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
              <h2 className="text-xl font-medium text-green-800 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Projetos do Cliente
              </h2>
              <div className="flex flex-col md:flex-row gap-4 w-full md:w-auto">
                <div className="relative w-full md:w-80">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-green-500" />
                    <Input
                      placeholder="Buscar projetos..."
                      className="pl-10 py-6 border-green-200 rounded-lg focus:border-green-400 focus:ring focus:ring-green-200 focus:ring-opacity-50 transition-all duration-200 bg-green-50/50"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>

                <div className="w-full md:w-64">
                  <Select
                    value={selectedServiceType}
                    onValueChange={setSelectedServiceType}
                  >
                    <SelectTrigger className="h-12 border-green-200 rounded-lg focus:border-green-400 focus:ring focus:ring-green-200 focus:ring-opacity-50 transition-all duration-200 bg-green-50/50">
                      <SelectValue placeholder="Filtrar por tipo de serviço" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ALL">Todos os tipos</SelectItem>
                      <SelectItem value="INSPECAO">Inspeção</SelectItem>
                      <SelectItem value="FISCALIZACAO">Fiscalização</SelectItem>
                      <SelectItem value="GERENCIAMENTO">Gerenciamento</SelectItem>
                      <SelectItem value="CONSULTORIA">Consultoria</SelectItem>
                      <SelectItem value="PROJETO">Projeto</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {loading ? (
              <div className="text-center py-16 animate-fadeIn">
                <div className="inline-block h-12 w-12 animate-spin rounded-full border-4 border-solid border-green-500 border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" role="status">
                  <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">Carregando...</span>
                </div>
                <p className="text-gray-500 text-lg mt-4">Carregando projetos...</p>
              </div>
            ) : filteredProjects.length > 0 ? (
              <div className="grid grid-cols-1 gap-6 animate-fadeIn">
                {filteredProjects.map((project) => (
                  <ProjectCard key={project.id} project={project} />
                ))}
              </div>
            ) : (
              <div className="text-center py-16 animate-fadeIn">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-300 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <p className="text-gray-500 text-lg">Nenhum projeto encontrado para este cliente</p>
              </div>
            )}
          </div>
        )}
      </div>
    </ContentWrapper>
  );
}
