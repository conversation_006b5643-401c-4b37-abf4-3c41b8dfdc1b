"use server";

import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import { formatDate, toKebabCase } from "@/lib/utils";
import { prisma } from "@/lib/prisma";
import fs from "fs";
import path from "path";
import sizeOf from "image-size";

// Interfaces para tipagem
interface Inspection {
  inspectionDate: Date | string;
  numberInspection?: string;
  technicalData?: string;
  observation?: string;
  laborEquipament?: Array<{
    labor: {
      type: string;
      name: string;
    };
    laborId: string;
    amount: number;
  }>;
  photos?: Array<{
    fileId: string;
    description?: string;
  }>;
  videos?: Array<{
    fileId: string;
    description?: string;
  }>;
}

interface Proposal {
  name?: string;
  startDate?: Date | string;
  endDate?: Date | string;
  address?: string;
  city?: string;
  state?: string;
  situation?: "PROJECT_FINISHED" | "PROJECT_IN_PROGRESS";
}

interface Customer {
  name?: string;
}

interface PdfData {
  inspection: Inspection;
  proposal?: Proposal;
  customer?: Customer;
}

interface PdfResult {
  success: boolean;
  buffer?: Buffer;
  fileName?: string;
  error?: string;
}

interface ImageData {
  data: string;
  format: string;
}

// Função para obter dados de pluviosidade para uma inspeção
async function getPluviosityForInspection(city: string, inspectionDate: Date) {
  const dateKey = inspectionDate.toISOString().split("T")[0];
  const pluviosity = await prisma.pluviosity.findFirst({
    where: {
      dateKey,
      city: toKebabCase(city),
    },
    select: {
      value: true,
    },
  });

  return pluviosity?.value?.toString() ?? "0";
}

// Função para determinar a condição climática com base no valor de pluviosidade
function getWeatherCondition(pluviosityValue: string): string {
  const value = parseFloat(pluviosityValue);

  if (value === 0) return "Dia claro, sem precipitação";
  if (value <= 0.2) return "Garoa leve";
  if (value <= 2.5) return "Chuva fraca";
  if (value <= 7.6) return "Chuva moderada";
  if (value <= 50) return "Chuva forte";
  return "Chuva muito forte";
}

// Função para carregar uma imagem do sistema de arquivos
async function loadImageFromFile(filePath: string): Promise<ImageData> {
  try {
    // Verificar se o arquivo existe
    if (!fs.existsSync(filePath)) {
      throw new Error(`Arquivo não encontrado: ${filePath}`);
    }

    // Ler o arquivo como buffer
    const buffer = fs.readFileSync(filePath);

    // Determinar o formato da imagem com base na extensão
    const extension = path.extname(filePath).toLowerCase();
    let format = "JPEG";

    if (extension === ".png") {
      format = "PNG";
    } else if (extension === ".gif") {
      format = "GIF";
    }

    // Converter o buffer para base64
    const base64 = buffer.toString("base64");

    return {
      data: base64,
      format,
    };
  } catch (error) {
    console.error("Erro ao carregar imagem:", error);
    throw error;
  }
}

// Função para buscar imagem via HTTP (endpoint interno)
async function fetchImageFromApi(fileId: string): Promise<ImageData> {
  try {
    // Montar a URL do endpoint interno
    const baseUrl =
      process.env.NEXT_PUBLIC_API_BASE_URL ||
      process.env.NEXT_PUBLIC_BASE_URL ||
      process.env.NEXT_PUBLIC_APP_URL ||
      "http://localhost:3000";
    const url = `${baseUrl}/api/files/${encodeURIComponent(fileId)}`;
    const res = await fetch(url);
    if (!res.ok) throw new Error(`Erro ao buscar imagem: ${url}`);
    const arrayBuffer = await res.arrayBuffer();
    // Tentar detectar o formato
    const contentType = res.headers.get("content-type") || "";
    let format = "JPEG";
    if (contentType.includes("png")) format = "PNG";
    if (contentType.includes("gif")) format = "GIF";
    // Converter para base64
    const base64 = Buffer.from(arrayBuffer).toString("base64");
    return { data: base64, format };
  } catch (error) {
    console.error("Erro ao buscar imagem via API:", error);
    throw error;
  }
}

// Função para buscar thumbnail via HTTP (endpoint interno)
async function fetchThumbnailFromApi(fileId: string): Promise<ImageData> {
  try {
    const baseUrl =
      process.env.NEXT_PUBLIC_API_BASE_URL ||
      process.env.NEXT_PUBLIC_BASE_URL ||
      process.env.NEXT_PUBLIC_APP_URL ||
      "http://localhost:3000";
    const url = `${baseUrl}/api/thumbnails/${encodeURIComponent(fileId)}`;
    const res = await fetch(url);
    if (!res.ok) throw new Error(`Erro ao buscar thumbnail: ${url}`);
    const arrayBuffer = await res.arrayBuffer();
    const contentType = res.headers.get("content-type") || "";
    let format = "JPEG";
    if (contentType.includes("png")) format = "PNG";
    if (contentType.includes("gif")) format = "GIF";
    const base64 = Buffer.from(arrayBuffer).toString("base64");
    return { data: base64, format };
  } catch (error) {
    console.error("Erro ao buscar thumbnail via API:", error);
    throw error;
  }
}

export async function generateServerPdf(data: PdfData): Promise<PdfResult> {
  try {
    console.log("Iniciando geração do PDF no servidor...");
    const { inspection, proposal, customer } = data;

    // Criar um novo documento PDF
    const pdf = new jsPDF("p", "mm", "a4");
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // Configurações de margens
    const margin = 15;
    const contentWidth = pageWidth - 2 * margin;

    // Adicionar propriedade lastAutoTable ao objeto pdf para compatibilidade
    (pdf as any).lastAutoTable = { finalY: margin };

    // Título do relatório
    const inspectionDate = inspection.inspectionDate
      ? formatDate(inspection.inspectionDate, "DATE")
      : formatDate(new Date(), "DATE");

    // Função para adicionar cabeçalho consistente em todas as páginas
    const addHeader = () => {
      pdf.setFillColor(255, 255, 255);
      pdf.rect(margin, margin, contentWidth, 20, "F");

      pdf.setFontSize(11);
      pdf.setTextColor(0, 0, 0);
      pdf.setFont("helvetica", "bold");
      pdf.text(
        `Relatório ${inspectionDate} nº ${inspection.numberInspection || ""}`,
        pageWidth / 2,
        margin + 8,
        { align: "center" }
      );
    };

    // Adicionar cabeçalho na primeira página
    addHeader();

    // Tentar carregar o logo
    try {
      // Caminho para o logo (ajuste conforme necessário)
      const logoPath = path.join(process.cwd(), "public", "logos", "img8.jpg");

      // Carregar o logo
      const logoImage = await loadImageFromFile(logoPath);

      // Calcular a posição central para a logo
      const logoWidth = 80;
      const logoHeight = 30;
      const logoX = (pageWidth - logoWidth) / 2;

      // Adicionar a imagem ao PDF
      pdf.addImage(
        logoImage.data,
        logoImage.format,
        logoX,
        margin + 10,
        logoWidth,
        logoHeight,
        undefined,
        "FAST"
      );
    } catch (error) {
      console.error("Erro ao adicionar logo:", error);

      // Fallback: Adicionar um retângulo colorido como placeholder
      pdf.setFillColor(240, 240, 240);
      const logoWidth = 80;
      const logoHeight = 30;
      const logoX = (pageWidth - logoWidth) / 2;
      pdf.rect(logoX, margin + 10, logoWidth, logoHeight, "F");

      // Adicionar texto centralizado
      pdf.setFontSize(12);
      pdf.setFont("helvetica", "bold");
      pdf.text("LOGO", logoX + logoWidth / 2, margin + 25, { align: "center" });
    }

    // Informações do relatório
    let yPos = margin + 45;

    // Definir configurações comuns para todas as tabelas de informações
    const commonTableStyles = {
      fontSize: 9,
      cellPadding: 2,
      minCellHeight: 10, // Altura mínima fixa para todas as células
      valign: "middle" as const, // Alinhamento vertical centralizado
    };

    const commonHeadStyles = {
      fillColor: [220, 220, 220] as [number, number, number],
      textColor: [0, 0, 0] as [number, number, number],
      fontStyle: "bold" as const, // Usar 'as const' para o tipo literal
      minCellHeight: 10,
      valign: "middle" as const, // Especificar o tipo correto para valign
    };

    // Tabela de informações do relatório (largura completa)
    autoTable(pdf, {
      startY: yPos,
      head: [["", ""]],
      body: [
        ["Relatório nº", `${inspection.numberInspection || ""}`],
        ["Data do relatório", inspectionDate],
        [
          "Dia da semana",
          inspection.inspectionDate
            ? new Date(inspection.inspectionDate as Date).toLocaleDateString(
                "pt-BR",
                { weekday: "long" }
              )
            : "",
        ],
      ],
      theme: "grid",
      headStyles: commonHeadStyles,
      styles: commonTableStyles,
      columnStyles: {
        0: { fontStyle: "bold", cellWidth: 40 },
        1: { cellWidth: contentWidth - 40 },
      },
      tableWidth: contentWidth,
      margin: { left: margin },
    });

    // Título do relatório periódico - usar uma tabela para manter a consistência
    yPos = (pdf as any).lastAutoTable.finalY + 10;
    autoTable(pdf, {
      startY: yPos,
      head: [["Relatório Periódico de Obra (RPO)"]],
      body: [],
      theme: "grid",
      headStyles: {
        ...commonHeadStyles,
        fillColor: [240, 240, 240] as [number, number, number],
        fontSize: 10,
      },
      styles: {
        ...commonTableStyles,
      },
      columnStyles: { 0: { cellWidth: contentWidth } },
      tableWidth: contentWidth,
      margin: { left: margin },
    });

    // Calcular prazos com base nas datas da proposta
    let prazoContratual = "";
    let prazoDecorrido = "";
    let prazoVencer = "";
    let inicio = "";
    let fim = "";
    if (proposal?.startDate && proposal?.endDate) {
      const startDate =
        proposal.startDate instanceof Date
          ? proposal.startDate
          : new Date(proposal.startDate);
      const endDate =
        proposal.endDate instanceof Date
          ? proposal.endDate
          : new Date(proposal.endDate);
      const inspectionDateObj =
        inspection.inspectionDate instanceof Date
          ? inspection.inspectionDate
          : new Date(inspection.inspectionDate);
      const today = new Date();
      inicio = formatDate(startDate, "DATE");
      fim = formatDate(endDate, "DATE");
      // Calcular prazo contratual (total de dias do contrato)
      const diffTimeContratual = Math.abs(
        endDate.getTime() - startDate.getTime()
      );
      const diffDaysContratual = Math.ceil(
        diffTimeContratual / (1000 * 60 * 60 * 24)
      );
      prazoContratual = `${diffDaysContratual} dias`;
      // Calcular prazo decorrido (dias desde a data da inspeção até hoje)
      const diffTimeDecorrido = Math.abs(
        today.getTime() - inspectionDateObj.getTime()
      );
      const diffDaysDecorrido = Math.ceil(
        diffTimeDecorrido / (1000 * 60 * 60 * 24)
      );
      prazoDecorrido = `${diffDaysDecorrido} dias`;
      // Calcular prazo a vencer (dias restantes até o fim)
      const diffTimeVencer = endDate.getTime() - inspectionDateObj.getTime();
      const diffDaysVencer = Math.ceil(diffTimeVencer / (1000 * 60 * 60 * 24));
      prazoVencer = `${diffDaysVencer} dias`;
    }

    // Tabela de informações da obra (ajustada igual ao client)
    yPos = (pdf as any).lastAutoTable.finalY + 10;
    autoTable(pdf, {
      startY: yPos,
      head: [["Obra", "Início", "Fim", "Prazo contratual"]],
      body: [
        [
          proposal?.name || "",
          inicio || "Não informado",
          fim || "Não informado",
          prazoContratual,
        ],
      ],
      theme: "grid",
      headStyles: commonHeadStyles,
      styles: commonTableStyles,
      columnStyles: {
        0: { cellWidth: contentWidth - 120 },
        1: { cellWidth: 40 },
        2: { cellWidth: 40 },
        3: { cellWidth: 40 },
      },
      tableWidth: contentWidth,
      margin: { left: margin },
    });

    // Tabela de local (igual ao client)
    yPos = (pdf as any).lastAutoTable.finalY;
    autoTable(pdf, {
      startY: yPos,
      head: [["Local", "Prazo decorrido"]],
      body: [
        [
          proposal?.address && proposal?.city
            ? `${proposal.address}, ${proposal.city}${
                proposal.state ? `, ${proposal.state}` : ""
              }`
            : "",
          proposal?.situation === "PROJECT_FINISHED"
            ? "Contrato Concluído"
            : prazoDecorrido,
        ],
      ],
      theme: "grid",
      headStyles: commonHeadStyles,
      styles: commonTableStyles,
      columnStyles: {
        0: { cellWidth: contentWidth - 80 },
        1: { cellWidth: 80 },
      },
      tableWidth: contentWidth,
      margin: { left: margin },
    });

    // Tabela de contratante (igual ao client)
    yPos = (pdf as any).lastAutoTable.finalY;
    autoTable(pdf, {
      startY: yPos,
      head: [["Contratante", "Prazo a vencer"]],
      body: [
        [
          customer?.name || "",
          proposal?.situation === "PROJECT_FINISHED"
            ? "Contrato Concluído"
            : prazoVencer,
        ],
      ],
      theme: "grid",
      headStyles: commonHeadStyles,
      styles: commonTableStyles,
      columnStyles: {
        0: { cellWidth: contentWidth - 80 },
        1: { cellWidth: 80 },
      },
      tableWidth: contentWidth,
      margin: { left: margin },
    });

    // Tabela de condição climática
    yPos = (pdf as any).lastAutoTable.finalY + 10;

    // Obter dados de pluviosidade
    let pluviosityValue = "0";
    try {
      if (proposal?.city && inspection.inspectionDate) {
        // Garantir que inspectionDate seja uma instância de Date
        const inspectionDateObj =
          inspection.inspectionDate instanceof Date
            ? inspection.inspectionDate
            : new Date(inspection.inspectionDate);
        pluviosityValue = await getPluviosityForInspection(
          proposal.city,
          inspectionDateObj
        );
      }
    } catch (error) {
      console.error("Erro ao obter dados de pluviosidade:", error);
    }

    // Determinar condição climática
    const weatherCondition = getWeatherCondition(pluviosityValue);

    // Determinar se é praticável
    const isPracticable =
      parseFloat(pluviosityValue) <= 2.5 ? "Praticável" : "Não Praticável";

    pdf.setFontSize(11);
    pdf.setFont("helvetica", "bold");
    pdf.text("Condição Climática (1)", margin, yPos);

    yPos += 5;
    autoTable(pdf, {
      startY: yPos,
      head: [["Precipitação (mm)", "Condição Climática", "Situação"]],
      body: [[pluviosityValue, weatherCondition, isPracticable]],
      theme: "grid",
      headStyles: commonHeadStyles,
      styles: commonTableStyles,
      columnStyles: {
        0: { cellWidth: 40 },
        1: { cellWidth: contentWidth - 120 },
        2: { cellWidth: 80 },
      },
      tableWidth: contentWidth,
      margin: { left: margin },
    });

    // Tabela de mão de obra
    yPos = (pdf as any).lastAutoTable.finalY + 10;

    // Preparar dados de mão de obra
    const laborData =
      inspection.laborEquipament?.filter(
        (item): item is (typeof inspection.laborEquipament)[0] =>
          item.labor.type === "LABOR"
      ) || [];

    // Verificar se há dados de mão de obra
    if (laborData.length > 0) {
      // Obter categorias únicas de mão de obra dos dados
      const uniqueCategories = [
        ...new Set(laborData.map((item) => item.labor.name)),
      ] as string[];
      const laborCounts: Record<string, number> = {};

      // Inicializar contagens
      uniqueCategories.forEach((category) => {
        laborCounts[category] = 0;
      });

      // Contar mão de obra por categoria
      let totalLabor = 0;
      laborData.forEach((item) => {
        const category = item.labor.name;
        laborCounts[category] =
          (laborCounts[category] || 0) + (item.amount || 0);
        totalLabor += item.amount || 0;
      });

      // Criar linha para a tabela
      const laborRow = uniqueCategories.map(
        (category) => laborCounts[category] || 0
      );

      pdf.setFontSize(11);
      pdf.setFont("helvetica", "bold");
      pdf.text(`Mão de obra (${totalLabor})`, margin, yPos);

      yPos += 5;
      // Calcular largura igual para cada coluna
      const columnCount = uniqueCategories.length;
      const equalWidth = contentWidth / columnCount;

      // Criar objeto de estilos de coluna com larguras iguais
      const columnStylesObj = {};
      uniqueCategories.forEach((_, index) => {
        columnStylesObj[index] = { cellWidth: equalWidth };
      });

      autoTable(pdf, {
        startY: yPos,
        head: [uniqueCategories],
        body: [laborRow],
        theme: "grid",
        headStyles: {
          ...commonHeadStyles,
          halign: "center" as const,
        },
        styles: {
          ...commonTableStyles,
          halign: "center" as const,
        },
        columnStyles: columnStylesObj,
        tableWidth: contentWidth,
        margin: { left: margin },
      });
    } else {
      // Exibir mensagem quando não há dados de mão de obra
      pdf.setFontSize(11);
      pdf.setFont("helvetica", "bold");
      pdf.text("Mão de obra (0)", margin, yPos);

      yPos += 5;
      autoTable(pdf, {
        startY: yPos,
        head: [["Informação"]],
        body: [["Nenhuma mão de obra foi adicionada a esta inspeção."]],
        theme: "grid",
        headStyles: {
          ...commonHeadStyles,
          halign: "center" as const,
        },
        styles: {
          ...commonTableStyles,
          halign: "center" as const,
        },
        columnStyles: { 0: { cellWidth: contentWidth } },
        tableWidth: contentWidth,
        margin: { left: margin },
      });
    }

    // Verificar se há espaço suficiente para a tabela de equipamentos
    // Se não houver, adicionar uma nova página
    if ((pdf as any).lastAutoTable.finalY + 100 > pageHeight) {
      pdf.addPage();
      addHeader();
      yPos = margin + 30; // Posição inicial após o cabeçalho na nova página
    } else {
      yPos = (pdf as any).lastAutoTable.finalY + 15;
    }

    // Preparar dados de equipamentos
    const equipmentData =
      inspection.laborEquipament?.filter(
        (item): item is (typeof inspection.laborEquipament)[0] =>
          item.labor.type === "EQUIPAMENT"
      ) || [];

    // Remover duplicatas baseado no laborId
    const uniqueEquipmentMap = new Map<string, (typeof equipmentData)[0]>();
    equipmentData.forEach((item) => {
      if (!uniqueEquipmentMap.has(item.laborId)) {
        uniqueEquipmentMap.set(item.laborId, item);
      }
    });

    // Converter o Map de volta para array
    const uniqueEquipmentData = Array.from(uniqueEquipmentMap.values());

    // Exibir apenas uma tabela de equipamentos
    pdf.setFontSize(11);
    pdf.setFont("helvetica", "bold");
    pdf.text(
      `Equipamentos${
        uniqueEquipmentData.length > 0
          ? ` (${uniqueEquipmentData.length})`
          : " (0)"
      }`,
      margin,
      yPos
    );

    yPos += 5;

    // Verificar se há dados de equipamentos
    if (uniqueEquipmentData.length > 0) {
      // Obter categorias únicas de equipamentos dos dados
      const uniqueCategories = [
        ...new Set(uniqueEquipmentData.map((item) => item.labor.name)),
      ] as string[];
      const equipmentCounts: Record<string, number> = {};

      // Inicializar contagens
      uniqueCategories.forEach((category) => {
        equipmentCounts[category] = 0;
      });

      // Contar equipamentos por categoria
      uniqueEquipmentData.forEach((item) => {
        const category = item.labor.name;
        equipmentCounts[category] =
          (equipmentCounts[category] || 0) + (item.amount || 0);
      });

      // Criar linha para a tabela
      const equipmentRow = uniqueCategories.map(
        (category) => equipmentCounts[category] || 0
      );

      // Calcular largura igual para cada coluna
      const columnCount = uniqueCategories.length;
      const equalWidth = contentWidth / columnCount;

      // Criar objeto de estilos de coluna com larguras iguais
      const columnStylesObj = {};
      uniqueCategories.forEach((_, index) => {
        columnStylesObj[index] = { cellWidth: equalWidth };
      });

      autoTable(pdf, {
        startY: yPos,
        head: [uniqueCategories],
        body: [equipmentRow],
        theme: "grid",
        headStyles: {
          ...commonHeadStyles,
          halign: "center" as const,
        },
        styles: {
          ...commonTableStyles,
          halign: "center" as const,
        },
        columnStyles: columnStylesObj,
        tableWidth: contentWidth,
        margin: { left: margin },
      });
    } else {
      // Exibir mensagem quando não há dados de equipamentos
      autoTable(pdf, {
        startY: yPos,
        head: [["Informação"]],
        body: [["Nenhum equipamento foi adicionado a esta inspeção."]],
        theme: "grid",
        headStyles: {
          ...commonHeadStyles,
          halign: "center" as const,
        },
        styles: {
          ...commonTableStyles,
          halign: "center" as const,
        },
        columnStyles: { 0: { cellWidth: contentWidth } },
        tableWidth: contentWidth,
        margin: { left: margin },
      });
    }

    // Tabela de atividades (campo technicalData)
    yPos = (pdf as any).lastAutoTable.finalY + 15;
    pdf.setFontSize(11);
    pdf.setFont("helvetica", "bold");
    pdf.text("Atividades Técnicas (1)", margin, yPos);

    yPos += 5;
    autoTable(pdf, {
      startY: yPos,
      head: [["Dados Técnicos", "Status"]],
      body: [
        [inspection.technicalData || "Sem dados técnicos", "Em Andamento"],
      ],
      theme: "grid",
      headStyles: commonHeadStyles,
      styles: commonTableStyles,
      columnStyles: {
        0: { cellWidth: contentWidth - 80 },
        1: { cellWidth: 80 },
      },
      tableWidth: contentWidth,
      margin: { left: margin },
    });

    // Tabela de comentários (campo observation)
    yPos = (pdf as any).lastAutoTable.finalY + 15;
    pdf.setFontSize(11);
    pdf.setFont("helvetica", "bold");
    pdf.text("Observações (1)", margin, yPos);

    yPos += 5;
    autoTable(pdf, {
      startY: yPos,
      head: [["Comentários e Observações", ""]],
      body: [
        [
          `Cliente: ${customer?.name || "Não informado"}\nData: ${formatDate(
            inspection.inspectionDate,
            "DATETIME"
          )}\n\nObservação: ${inspection.observation || "Sem observações"}`,
          "",
        ],
      ],
      theme: "grid",
      headStyles: commonHeadStyles,
      styles: {
        ...commonTableStyles,
        minCellHeight: 20,
      },
      columnStyles: {
        0: { cellWidth: contentWidth - 10 },
        1: { cellWidth: 10 },
      },
      tableWidth: contentWidth,
      margin: { left: margin },
    });

    // Adicionar fotos se disponíveis
    if (inspection.photos && inspection.photos.length > 0) {
      pdf.addPage();
      addHeader();
      yPos = margin + 20;
      pdf.setFontSize(11);
      pdf.setFont("helvetica", "bold");
      pdf.text(`Fotos (${inspection.photos.length})`, margin, yPos);
      yPos += 15;
      // Adicionar fotos em grade 2x2
      const photoWidth = (contentWidth - 10) / 2;
      const photoHeight = photoWidth * 0.75;
      for (let i = 0; i < inspection.photos.length; i += 4) {
        if (yPos + photoHeight * 2 + 20 > pageHeight) {
          pdf.addPage();
          addHeader();
          yPos = margin + 20;
        }
        const photosInBatch = inspection.photos.slice(i, i + 4);
        for (let j = 0; j < photosInBatch.length; j++) {
          const photo = photosInBatch[j];
          const xPos = margin + (j % 2) * (photoWidth + 10);
          const yPhotoPos = yPos + Math.floor(j / 2) * (photoHeight + 20);
          try {
            // Buscar a imagem via HTTP do endpoint interno
            const imgData = await fetchImageFromApi(photo.fileId);
            // Obter dimensões da imagem
            const buffer = Buffer.from(imgData.data, "base64");
            let imgWidth = photoWidth;
            let imgHeight = photoHeight;
            let offsetX = 0;
            let offsetY = 0;
            try {
              const dimensions = sizeOf(buffer);
              if (dimensions.width && dimensions.height) {
                const imgAspect = dimensions.width / dimensions.height;
                const boxAspect = photoWidth / photoHeight;
                if (imgAspect > boxAspect) {
                  imgWidth = photoWidth;
                  imgHeight = photoWidth / imgAspect;
                  offsetY = (photoHeight - imgHeight) / 2;
                } else {
                  imgHeight = photoHeight;
                  imgWidth = photoHeight * imgAspect;
                  offsetX = (photoWidth - imgWidth) / 2;
                }
              }
            } catch (dimErr) {
              console.log(dimErr);
              // Se não conseguir obter dimensões, usa box padrão
            }
            pdf.addImage(
              imgData.data,
              imgData.format,
              xPos + offsetX,
              yPhotoPos + offsetY,
              imgWidth,
              imgHeight
            );
            if (photo.description) {
              pdf.setFontSize(8);
              pdf.setFont("helvetica", "normal");
              pdf.text(photo.description, xPos, yPhotoPos + photoHeight + 5, {
                maxWidth: photoWidth,
              });
            }
          } catch (err) {
            console.log(err);
            pdf.setFillColor(200, 200, 200);
            pdf.rect(xPos, yPhotoPos, photoWidth, photoHeight, "F");
            pdf.setFontSize(8);
            pdf.setFont("helvetica", "normal");
            pdf.text(
              "Foto não encontrada",
              xPos + 2,
              yPhotoPos + photoHeight / 2
            );
          }
        }
        yPos += photoHeight * 2 + 20;
      }
    }

    // Adicionar vídeos se disponíveis
    if (inspection.videos && inspection.videos.length > 0) {
      pdf.addPage();
      addHeader();
      yPos = margin + 20;
      pdf.setFontSize(11);
      pdf.setFont("helvetica", "bold");
      pdf.text(
        `Vídeos da Inspeção (${inspection.videos.length})`,
        margin,
        yPos
      );
      yPos += 10;
      const thumbSize = 40;
      const gap = 10;
      let col = 0;
      let row = 0;
      const thumbsPerRow = Math.floor(contentWidth / (thumbSize + gap));
      for (let idx = 0; idx < inspection.videos.length; idx++) {
        const video = inspection.videos[idx];
        const x = margin + col * (thumbSize + gap);
        const y = yPos + row * (thumbSize + 30);
        const baseUrl =
          process.env.NEXT_PUBLIC_API_BASE_URL ||
          process.env.NEXT_PUBLIC_BASE_URL ||
          process.env.NEXT_PUBLIC_APP_URL ||
          "http://localhost:3000";
        const linkInline = `${baseUrl}/api/files/${encodeURIComponent(
          video.fileId
        )}`;
        const linkDownload = `${baseUrl}/api/files/${encodeURIComponent(
          video.fileId
        )}?download=1`;
        try {
          // Buscar thumbnail via HTTP
          const imgData = await fetchThumbnailFromApi(video.fileId);
          // Obter dimensões da thumbnail
          const buffer = Buffer.from(imgData.data, "base64");
          let imgWidth = thumbSize;
          let imgHeight = thumbSize;
          let offsetX = 0;
          let offsetY = 0;
          try {
            const dimensions = sizeOf(buffer);
            if (dimensions.width && dimensions.height) {
              const imgAspect = dimensions.width / dimensions.height;
              const boxAspect = thumbSize / thumbSize;
              if (imgAspect > boxAspect) {
                imgWidth = thumbSize;
                imgHeight = thumbSize / imgAspect;
                offsetY = (thumbSize - imgHeight) / 2;
              } else {
                imgHeight = thumbSize;
                imgWidth = thumbSize * imgAspect;
                offsetX = (thumbSize - imgWidth) / 2;
              }
            }
          } catch (dimErr) {
            console.log(dimErr);
          }
          // Desenhar fundo quadrado cinza
          pdf.setFillColor(200, 200, 200);
          pdf.rect(x, y, thumbSize, thumbSize, "F");
          // Adicionar a imagem centralizada
          pdf.addImage(
            imgData.data,
            imgData.format,
            x + offsetX,
            y + offsetY,
            imgWidth,
            imgHeight
          );
          // Tornar a thumbnail clicável (link para visualizar vídeo)
          pdf.link(x, y, thumbSize, thumbSize, { url: linkInline });
        } catch (err) {
          console.log(err);
          pdf.setFillColor(200, 200, 200);
          pdf.rect(x, y, thumbSize, thumbSize, "F");
          pdf.setFontSize(8);
          pdf.setFont("helvetica", "normal");
          pdf.text("Sem thumb", x + 2, y + thumbSize / 2);
        }
        // Descrição abaixo da thumbnail
        pdf.setFontSize(8);
        pdf.setFont("helvetica", "normal");
        const descText = video.description
          ? video.description
          : `Vídeo ${idx + 1}`;
        pdf.text(descText, x, y + thumbSize + 6, { maxWidth: thumbSize });
        // Adicionar link de download abaixo da descrição
        const downloadText = "Clique aqui para baixar";
        const downloadY = y + thumbSize + 14;
        pdf.setTextColor(0, 0, 255);
        pdf.text(downloadText, x, downloadY, { maxWidth: thumbSize });
        const textWidth = pdf.getTextWidth(downloadText);
        pdf.link(x, downloadY - 3, textWidth, 8, { url: linkDownload });
        pdf.setTextColor(0, 0, 0);
        col++;
        if (col >= thumbsPerRow) {
          col = 0;
          row++;
          if (y + thumbSize + 30 > pageHeight - margin - 20) {
            pdf.addPage();
            addHeader();
            yPos = margin + 20;
            row = 0;
          }
        }
      }
      yPos += (row + 1) * (thumbSize + 30) + 10;
    }

    // Adicionar número de página
    const totalPages = pdf.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      pdf.setPage(i);
      pdf.setFontSize(8);
      pdf.setFont("helvetica", "normal");
      pdf.text(
        `${i} / ${totalPages}`,
        pageWidth - margin,
        pageHeight - margin - 2
      );
    }

    // Gerar o PDF como um buffer
    const pdfBuffer = Buffer.from(pdf.output("arraybuffer"));

    return {
      success: true,
      buffer: pdfBuffer,
      fileName: `relatorio_inspecao_${
        inspection.numberInspection || Date.now()
      }.pdf`,
    };
  } catch (error) {
    console.error("Erro ao gerar PDF no servidor:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro desconhecido",
    };
  }
}
