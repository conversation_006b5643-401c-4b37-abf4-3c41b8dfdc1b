import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  console.log("=== TESTE DE CALLBACK ===");
  console.log("Timestamp:", new Date().toISOString());
  console.log("Headers:", Object.fromEntries(request.headers.entries()));
  console.log("URL:", request.url);
  console.log("========================");
  
  return NextResponse.json({ 
    message: "Callback test endpoint working",
    timestamp: new Date().toISOString(),
    url: request.url
  });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    console.log("=== TESTE DE CALLBACK POST ===");
    console.log("Timestamp:", new Date().toISOString());
    console.log("Headers:", Object.fromEntries(request.headers.entries()));
    console.log("Body:", JSON.stringify(body, null, 2));
    console.log("==============================");
    
    return NextResponse.json({ 
      message: "Callback POST test successful",
      timestamp: new Date().toISOString(),
      receivedBody: body
    });
  } catch (error) {
    console.error("Erro no teste de callback POST:", error);
    return NextResponse.json({ 
      error: "Error in callback test",
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
