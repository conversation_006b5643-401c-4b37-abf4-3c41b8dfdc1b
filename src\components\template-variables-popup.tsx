"use client";

import { useState, useEffect, useRef } from "react";
import { X, GripVertical, Co<PERSON>, Check } from "lucide-react";
import { But<PERSON> } from "@/src/components/ui/button";
import { ScrollArea } from "@/src/components/ui/scroll-area";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/src/components/ui/tabs";
import { cn } from "@/src/lib/utils";
import { useToast } from "@/src/hooks/use-toast";

interface TemplateVariablesPopupProps {
  isOpen: boolean;
  onClose: () => void;
  onInsertVariable: (variable: string) => void;
}

interface VariableGroup {
  title: string;
  variables: Array<{
    name: string;
    description: string;
    example: string;
  }>;
}

export function TemplateVariablesPopup({
  isOpen,
  onClose,

}: TemplateVariablesPopupProps) {
  const [position, setPosition] = useState({ x: 20, y: 100 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [copiedVariable, setCopiedVariable] = useState<string | null>(null);
  const popupRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Grupos de variáveis
  const variableGroups: VariableGroup[] = [
    {
      title: "Proposta",
      variables: [
        { name: "proposta", description: "Nome da proposta", example: "Reforma do Edifício XYZ" },
        { name: "nome_proposta", description: "Nome da proposta (alternativo)", example: "Reforma do Edifício XYZ" },
        { name: "orcamento", description: "Valor total da proposta", example: "R$ 100.000,00" },
        { name: "valor_proposta", description: "Valor total da proposta (alternativo)", example: "R$ 100.000,00" },
        { name: "valor_proposta_extenso", description: "Valor total da proposta por extenso", example: "cem mil reais" },
        { name: "valor_80_porcento", description: "80% do valor da proposta", example: "R$ 80.000,00" },
        { name: "valor_80_porcento_extenso", description: "80% do valor da proposta por extenso", example: "oitenta mil reais" },
      ],
    },
    {
      title: "Pagamento",
      variables: [
        { name: "entrada", description: "Valor da entrada", example: "R$ 20.000,00" },
        { name: "valor_entrada", description: "Valor da entrada (alternativo)", example: "R$ 20.000,00" },
        { name: "valor_entrada_extenso", description: "Valor da entrada por extenso", example: "vinte mil reais" },
        { name: "num_parcelas", description: "Número de parcelas", example: "10" },
        { name: "quantidade_parcelas", description: "Número de parcelas (alternativo)", example: "10" },
        { name: "valor_parcela", description: "Valor de cada parcela", example: "R$ 8.000,00" },
        { name: "valor_parcela_extenso", description: "Valor de cada parcela por extenso", example: "oito mil reais" },
      ],
    },
    {
      title: "Prazo",
      variables: [
        { name: "prazo_dias", description: "Quantidade de dias entre início e fim", example: "90" },
        { name: "data_inicio", description: "Data de início do projeto", example: "01/01/2024" },
        { name: "data_fim", description: "Data de término do projeto", example: "01/04/2024" },
      ],
    },
    {
      title: "Cliente",
      variables: [
        { name: "nome_cliente", description: "Nome do cliente", example: "Empresa ABC Ltda" },
        { name: "nome_cliente_maiusculo", description: "Nome do cliente em maiúsculas", example: "EMPRESA ABC LTDA" },
        { name: "cpf_cnpj", description: "CPF ou CNPJ do cliente", example: "12.345.678/0001-90" },
        { name: "endereco", description: "Endereço completo do cliente", example: "Rua ABC, 123, CEP: 12345-678, São Paulo/SP" },
        { name: "endereco_completo", description: "Endereço completo do cliente (alternativo)", example: "Rua ABC, 123, CEP: 12345-678, São Paulo/SP" },
        { name: "endereco_maiusculo", description: "Endereço completo do cliente em maiúsculas", example: "RUA ABC, 123, CEP: 12345-678, SÃO PAULO/SP" },
      ],
    },
    {
      title: "Outros",
      variables: [
        { name: "data_hoje", description: "Data atual", example: "15/05/2024" },
        { name: "data_hoje_extenso", description: "Data atual por extenso", example: "15 de maio de 2024" },
      ],
    },
  ];

  // Iniciar o arrasto
  const handleMouseDown = (e: React.MouseEvent) => {
    if (popupRef.current) {
      const rect = popupRef.current.getBoundingClientRect();
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
      setIsDragging(true);
    }
  };

  // Atualizar posição durante o arrasto
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        setPosition({
          x: e.clientX - dragOffset.x,
          y: e.clientY - dragOffset.y,
        });
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    }

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isDragging, dragOffset]);

  // Copiar variável para a área de transferência
  const handleCopyVariable = (variable: string) => {
    const formattedVariable = `{{${variable}}}`;
    navigator.clipboard.writeText(formattedVariable)
      .then(() => {
        // Atualizar estado para mostrar feedback visual
        setCopiedVariable(variable);

        // Mostrar toast de sucesso
        toast({
          title: "Variável copiada!",
          description: `${formattedVariable} foi copiada para a área de transferência.`,
          variant: "default",
        });

        // Resetar o estado após 2 segundos
        setTimeout(() => {
          setCopiedVariable(null);
        }, 2000);
      })
      .catch(err => {
        console.error('Erro ao copiar para a área de transferência:', err);
        toast({
          title: "Erro ao copiar",
          description: "Não foi possível copiar a variável. Tente novamente.",
          variant: "destructive",
        });
      });
  };

  if (!isOpen) return null;

  return (
    <div
      ref={popupRef}
      className={cn(
        "fixed z-50 bg-white rounded-lg shadow-lg border border-gray-200 w-96 flex flex-col",
        isDragging && "cursor-grabbing"
      )}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
      }}
    >
      {/* Cabeçalho arrastável */}
      <div
        className="flex items-center justify-between p-3 border-b border-gray-200 cursor-grab"
        onMouseDown={handleMouseDown}
      >
        <div className="flex items-center gap-2">
          <GripVertical className="h-4 w-4 text-gray-500" />
          <h3 className="font-medium">Variáveis de Template</h3>
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6"
          onClick={onClose}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Conteúdo com abas */}
      <Tabs defaultValue={variableGroups[0].title.toLowerCase()} className="w-full">
        <TabsList className="w-full justify-start px-3 pt-2 bg-gray-50 overflow-x-auto flex-nowrap">
          {variableGroups.map((group) => (
            <TabsTrigger
              key={group.title}
              value={group.title.toLowerCase()}
              className="text-xs"
            >
              {group.title}
            </TabsTrigger>
          ))}
        </TabsList>

        {variableGroups.map((group) => (
          <TabsContent
            key={group.title}
            value={group.title.toLowerCase()}
            className="p-0"
          >
            <ScrollArea className="h-[300px] p-3">
              <div className="space-y-2">
                {group.variables.map((variable) => (
                  <div
                    id={`variable-${variable.name}`}
                    key={variable.name}
                    className="p-2 hover:bg-gray-100 rounded-md cursor-pointer transition-colors"
                    onClick={() => handleCopyVariable(variable.name)}
                  >
                    <div className="flex items-center justify-between">
                      <code className="text-sm font-mono bg-gray-100 px-1 py-0.5 rounded">
                        {`{{${variable.name}}}`}
                      </code>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-gray-500">{variable.example}</span>
                        {copiedVariable === variable.name ? (
                          <Check className="h-4 w-4 text-green-500" />
                        ) : (
                          <Copy className="h-4 w-4 text-gray-400" />
                        )}
                      </div>
                    </div>
                    <p className="text-xs text-gray-600 mt-1">{variable.description}</p>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>
        ))}
      </Tabs>

      <div className="p-3 border-t border-gray-200 text-xs text-gray-500">
        Clique em uma variável para copiá-la para a área de transferência
      </div>
    </div>
  );
}
