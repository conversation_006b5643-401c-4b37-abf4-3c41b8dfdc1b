"use server";
import { prisma } from "@/src/lib/prisma";
import { convertBigInt } from "@/src/lib/utils";
import { ProposalTemplateInterface } from "@/src/types/core/proposal-template";
import { deleteFileEditorById, persistFileEditor } from "./file-editor";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

export async function loadProposalTemplates(
  getFiles: boolean = true,
  page: number = 1,
  pageSize: number = 10,
  search: string = "",
  type: string = "",
  originalType: string = ""
) {
  try {
    const { organizationId } = await getCurrentOrganization();
    const skip = (page - 1) * pageSize;

    if (!organizationId) {
      throw new Error("Organization ID not found");
    }

    // Construir a condição where base
    const where: any = {
      organizationId,
      // Filtrar por termo de pesquisa se fornecido
      ...(search
        ? {
            OR: [
              { title: { contains: search, mode: "insensitive" } },
              { description: { contains: search, mode: "insensitive" } },
            ],
          }
        : {}),
    };

    // Adicionar filtro por tipo apenas se for um dos tipos válidos no banco de dados
    // IMPORTANTE: Não passar INSPECTION ou SUPERVISION diretamente para o Prisma
    if (type === "PROPOSAL" || type === "CONTRACT" || type === "PROJECT") {
      where.type = type;
    }
    // Para INSPECTION e SUPERVISION, não filtrar por tipo no banco de dados
    // Faremos a filtragem depois de obter os resultados

    const [total, data] = await Promise.all([
      prisma.proposalTemplate.count({ where }),
      prisma.proposalTemplate.findMany({
        where,
        orderBy: { createdAt: "desc" },
        include: { fileEditor: getFiles },
        skip,
        take: pageSize,
      }),
    ]);

    // Filtrar os resultados com base no tipo solicitado
    let filteredData = data;

    // Usar o tipo original para filtragem, se fornecido
    const filterType = originalType || type;

    if (filterType) {
      // Para PROPOSAL e CONTRACT, já filtramos no banco de dados se type não for vazio
      // Para INSPECTION, SUPERVISION e PROJECT, precisamos filtrar manualmente
      if (filterType === "INSPECTION") {
        console.log("Filtrando templates de inspeção e consultoria...");
        // Filtrar templates de inspeção (que contém "inspeção" no título ou descrição)
        filteredData = data.filter(
          (item) =>
            item.title.toLowerCase().includes("inspeção") ||
            item.description.toLowerCase().includes("inspeção")
        );
      } else if (filterType === "SUPERVISION") {
        console.log("Filtrando templates de fiscalização e gerenciamento...");
        // Filtrar templates de fiscalização (que contém "fiscalização" no título ou descrição)
        filteredData = data.filter(
          (item) =>
            item.title.toLowerCase().includes("fiscalização") ||
            item.description.toLowerCase().includes("fiscalização")
        );
      } else if (filterType === "PROJECT") {
        console.log("Filtrando templates de projeto...");
        // Filtrar templates de projeto (que contém "projeto" no título ou descrição)
        filteredData = data.filter(
          (item) =>
            item.title.toLowerCase().includes("projeto") ||
            item.description.toLowerCase().includes("projeto")
        );
      } else if (filterType === "PROPOSAL" && (!type || type === "")) {
        // Se estamos filtrando por PROPOSAL mas não passamos o tipo para o Prisma,
        // precisamos filtrar manualmente para excluir templates de INSPECTION, SUPERVISION e PROJECT
        console.log(
          "Filtrando templates de proposta (excluindo inspeção, fiscalização e projeto)..."
        );
        filteredData = data.filter(
          (item) =>
            item.type === "PROPOSAL" &&
            !(
              item.title.toLowerCase().includes("inspeção") ||
              item.description.toLowerCase().includes("inspeção") ||
              item.title.toLowerCase().includes("fiscalização") ||
              item.description.toLowerCase().includes("fiscalização") ||
              item.title.toLowerCase().includes("projeto") ||
              item.description.toLowerCase().includes("projeto")
            )
        );
      }

      console.log(
        `Filtragem concluída. Encontrados ${filteredData.length} templates.`
      );

      // Recalcular o total e totalPages
      const filteredTotal = filteredData.length;
      const filteredTotalPages = Math.ceil(filteredTotal / pageSize);

      return convertBigInt({
        data: filteredData,
        total: filteredTotal,
        page,
        pageSize,
        totalPages: filteredTotalPages,
      });
    }

    // Retornar os resultados normais para os outros casos
    return convertBigInt({
      data,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    });
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function findProposalTemplate(id: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const data = await prisma.proposalTemplate.findUnique({
      where: {
        id,
        organizationId,
      },
      include: { fileEditor: true },
    });

    return convertBigInt(data) as ProposalTemplateInterface;
  } catch (error) {
    console.error(error);
  }
}

export async function saveProposalTemplate(
  formData: FormData
): Promise<ProposalTemplateInterface | undefined> {
  try {
    const file = formData.get("file") as any;
    const fileName = formData.get("fileName") as string;
    const description = formData.get("description") as string;
    const id = formData.get("id") as string;
    const type = formData.get("type") as string;
    const fileKey = formData.get("fileKey") as string;
    const { organizationId } = await getCurrentOrganization();

    // Usar o tipo fornecido diretamente
    // Os tipos "INSPECTION" e "SUPERVISION" serão tratados como tipos distintos
    // mesmo que no banco de dados sejam armazenados como "PROPOSAL"
    const templateType = type;

    // Para compatibilidade com o banco de dados, converter para "PROPOSAL" se necessário
    let dbType = templateType;
    if (
      dbType !== "PROPOSAL" &&
      dbType !== "CONTRACT" &&
      dbType !== "PROJECT"
    ) {
      dbType = "PROPOSAL";
    }

    // Adicionar o tipo original como parte do título ou descrição para facilitar a filtragem
    let titleWithType = fileName;
    let descriptionWithType = description;

    // Se for um tipo especial (INSPECTION, SUPERVISION ou PROJECT), adicionar ao título e descrição
    if (templateType === "INSPECTION") {
      if (!titleWithType.toLowerCase().includes("inspeção")) {
        titleWithType = `[Inspeção e Consultoria] ${titleWithType}`;
      }
      if (!descriptionWithType.toLowerCase().includes("inspeção")) {
        descriptionWithType = `${descriptionWithType} (Template de Inspeção e Consultoria)`;
      }
    } else if (templateType === "SUPERVISION") {
      if (!titleWithType.toLowerCase().includes("fiscalização")) {
        titleWithType = `[Fiscalização e Gerenciamento] ${titleWithType}`;
      }
      if (!descriptionWithType.toLowerCase().includes("fiscalização")) {
        descriptionWithType = `${descriptionWithType} (Template de Fiscalização e Gerenciamento)`;
      }
    } else if (templateType === "PROJECT") {
      if (!titleWithType.toLowerCase().includes("projeto")) {
        titleWithType = `[Projeto] ${titleWithType}`;
      }
      if (!descriptionWithType.toLowerCase().includes("projeto")) {
        descriptionWithType = `${descriptionWithType} (Template de Projeto)`;
      }
    }

    let data: any = {
      title: titleWithType,
      description: descriptionWithType,
      type: dbType, // Usar o tipo compatível com o banco de dados
    };

    // Se temos um arquivo novo, sempre persistimos ele
    if (file && file.size > 0) {
      // Se estamos editando e temos um fileKey, atualizamos o arquivo existente
      const createdFile = await persistFileEditor(
        file,
        id ? fileKey : undefined
      );
      if (!createdFile?.id) throw Error("Failed to save file");

      data = {
        ...data,
        fileEditorId: createdFile.id,
        organizationId,
      };
    } else if (id) {
      // Se estamos editando e não temos arquivo novo, mantemos o fileEditorId existente
      // Verificar se já temos o fileEditorId no formData
      const fileEditorId = formData.get("fileEditorId");

      if (fileEditorId) {
        data = {
          ...data,
          fileEditorId: fileEditorId.toString(),
          organizationId,
        };
      } else {
        // Se não temos o fileEditorId no formData, buscamos do banco
        const existingTemplate = await prisma.proposalTemplate.findUnique({
          where: { id },
          select: { fileEditorId: true },
        });

        if (existingTemplate) {
          data = {
            ...data,
            fileEditorId: existingTemplate.fileEditorId,
            organizationId,
          };
        }
      }
    }

    // Remover organizationId do objeto data para update
    if (id) {
      const { organizationId, ...updateData } = data;
      const proposalTemplate = await prisma.proposalTemplate.update({
        where: { id, organizationId },
        data: {
          ...updateData,
        },
      });
      return convertBigInt(proposalTemplate) as ProposalTemplateInterface;
    } else {
      const proposalTemplate = await prisma.proposalTemplate.create({ data });
      return convertBigInt(proposalTemplate) as ProposalTemplateInterface;
    }
  } catch (error) {
    console.error(error);
    throw Error("Failed to save file");
  }
}

export async function removeProposalTemplate(id: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    // Buscar o template para obter o fileEditorId
    const template = await prisma.proposalTemplate.findUnique({
      where: { id, organizationId },
      select: { fileEditorId: true },
    });

    if (!template) {
      throw new Error("Template não encontrado");
    }

    // Desvincular o template das propostas associadas
    const proposalsUpdated = await prisma.proposal.updateMany({
      where: { proposalTemplateId: id },
      data: { proposalTemplateId: null },
    });

    // Excluir o template do banco de dados
    await prisma.proposalTemplate.delete({
      where: { id, organizationId },
    });

    // Excluir o arquivo do bucket se existir um fileEditorId
    if (template.fileEditorId) {
      await deleteFileEditorById(template.fileEditorId);
    }

    return {
      message: `Template de proposta removido com sucesso! ${proposalsUpdated.count} proposta(s) foram desvinculadas deste template.`,
    };
  } catch (error) {
    console.error(error);
    return { error: true, message: "Erro ao remover template de proposta" };
  }
}
