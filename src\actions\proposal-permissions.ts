"use server";

import { prisma } from "@/src/lib/prisma";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";
import { auth } from "@/src/providers/auth";

export async function addProposalPermission(
  proposalId: string,
  userId: string
) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const permission = await prisma.proposalPermission.create({
      data: {
        proposalId,
        userId,
        organizationId,
      },
    });

    return permission;
  } catch (error) {
    console.error(error);
    throw new Error("Erro ao adicionar permissão");
  }
}

export async function removeProposalPermission(
  proposalId: string,
  userId: string
) {
  try {
    await prisma.proposalPermission.delete({
      where: {
        proposalId_userId: {
          proposalId,
          userId,
        },
      },
    });
  } catch (error) {
    console.error(error);
    throw new Error("Erro ao remover permissão");
  }
}

export async function getProposalPermissions(proposalId: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const permissions = await prisma.proposalPermission.findMany({
      where: {
        proposalId,
        organizationId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return permissions;
  } catch (error) {
    console.error(error);
    throw new Error("Erro ao buscar permissões");
  }
}

export async function checkProposalPermission(proposalId: string) {
  try {
    const session = await auth();
    const userId = session?.user?.id;
    const { organizationId } = await getCurrentOrganization();

    if (!userId) {
      return false;
    }

    const permission = await prisma.proposalPermission.findFirst({
      where: {
        proposalId,
        userId,
        organizationId,
      },
    });

    return !permission;
  } catch (error) {
    console.error(error);
    return false;
  }
}
