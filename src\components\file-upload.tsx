"use client";

import { CheckCircle, Upload } from "lucide-react";
import { useState, useCallback, useEffect } from "react";
import { useToast } from "../hooks/use-toast";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";

type SelectItemType = {
  label: string;
  value: string;
};

interface FileUploadProps {
  fileTypes?: SelectItemType[];
  defaultFileType?: string;
  submitFormData: (data: FormData) => Promise<any>;
  onUploadSuccess?: (fileName: string) => void;
  editingTemplate?: any;
}

export default function FileUpload({
  submitFormData,
  onUploadSuccess,
  fileTypes,
  defaultFileType,
  editingTemplate
}: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [fileName, setFileName] = useState(editingTemplate?.title || "");
  const [description, setDescription] = useState(editingTemplate?.description || "");
  const [uploadType, setUploadType] = useState(defaultFileType || "");
  const [selectedFile, setSelectedFile] = useState<any | null>(editingTemplate?.fileEditor || null);
  const [isDragging, setIsDragging] = useState(false);
  const { toast } = useToast();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);

    if (event.dataTransfer.files.length > 0) {
      const file = event.dataTransfer.files[0];
      setSelectedFile(file);
    }
  }, []);

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);
  }, []);

  useEffect(() => {
    // Atualizar os campos quando o template de edição ou o tipo padrão mudar
    setFileName(editingTemplate?.title || "");
    setDescription(editingTemplate?.description || "");
    // Sempre usar o tipo padrão fornecido, que vem da tela atual
    setUploadType(defaultFileType || "");
    setSelectedFile(editingTemplate?.fileEditor || null);
  }, [editingTemplate, defaultFileType]);

  const handleUpload = async () => {
    if (!selectedFile || !fileName.trim()) {
      toast({
        title: "Erro",
        description: "Preencha o nome do modelo e selecione um arquivo.",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);

    const formData = new FormData();
    formData.append("file", selectedFile);
    formData.append("fileName", fileName);
    formData.append("description", description);
    if (editingTemplate) {
      formData.append("fileKey", editingTemplate.fileEditor?.key || "");
      formData.append("fileEditorId", editingTemplate.fileEditorId || "");
    }
    if (fileTypes) {
      formData.append("type", uploadType);
    }

    try {
      const response = await submitFormData(formData);
      if (!response?.id) throw new Error("Upload falhou");

      toast({
        title: "Sucesso",
        description: editingTemplate ? "Arquivo atualizado com sucesso!" : "Arquivo enviado com sucesso!",
        variant: "default"
      });

      setFileName("");
      setSelectedFile(null);

      onUploadSuccess?.(fileName);
    } catch (error) {
      console.error(error);
      toast({
        title: "Erro",
        description: "Falha ao enviar arquivo.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div
      className={`flex flex-col items-center justify-center p-6 border-2 border-dashed rounded-lg w-full max-w-md space-y-4
      ${isDragging ? "border-blue-500 bg-blue-50" : "border-gray-300"}`}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      onDragLeave={handleDragLeave}
    >
      <input
        type="file"
        id="file-upload"
        className="hidden"
        onChange={handleFileChange}
        disabled={isUploading}
      />
      <label htmlFor="file-upload" className="cursor-pointer">
        <div className="flex flex-col items-center">
          {selectedFile ? (
            <CheckCircle className="w-12 h-12 mb-2 text-green-500" />
          ) : (
            <Upload className="w-12 h-12 mb-2 text-gray-400" />
          )}
          <span className="text-sm">
            {selectedFile ? selectedFile.name || selectedFile.filename : "Arraste e solte um arquivo ou clique para selecionar"}
          </span>
        </div>
      </label>

      <Input
        type="text"
        value={fileName}
        onChange={(e) => setFileName(e.target.value)}
        placeholder="Nome do modelo*"
        disabled={isUploading}
        className="w-full"
      />

      <Input
        type="text"
        value={description}
        onChange={(e) => setDescription(e.target.value)}
        placeholder="Descrição do modelo"
        disabled={isUploading}
        className="w-full"
      />

      {fileTypes && (
        <Select
          onValueChange={(value) => setUploadType(value)}
          value={uploadType} // Usar o valor do estado
          defaultValue={defaultFileType} // Definir o valor padrão
        >
          <SelectTrigger>
            <SelectValue placeholder="Selecione um tipo" />
          </SelectTrigger>
          <SelectContent>
            {fileTypes?.map(({ label, value }, index) => (
              <SelectItem value={value} key={index}>
                {label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}

      <Button
        onClick={handleUpload}
        disabled={isUploading || !selectedFile || !fileName.trim()}
        className="w-full bg-green-500 hover:bg-green-600"
      >
        {isUploading ? "Enviando..." : editingTemplate ? "Atualizar" : "Enviar arquivo"}
      </Button>
    </div>
  );
}
