import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(
  date: string | Date | null | undefined,
  format: "DISPLAY" | "DATE" | "DATETIME" | "LONG" = "DISPLAY"
) {
  // Verificar se a data é válida
  if (date === null || date === undefined) {
    return "Data não informada";
  }

  try {
    // Verifica se a data é uma string e no formato YYYY-MM-DD
    let parsedDate: Date;
    if (typeof date === "string") {
      if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
        parsedDate = new Date(`${date}T00:00:00`);
      } else {
        parsedDate = new Date(date);
      }
    } else {
      parsedDate = date;
    }

    // Verificar se a data é válida
    if (isNaN(parsedDate.getTime())) {
      return "Data inválida";
    }

    if (format === "DATETIME") {
      return parsedDate.toLocaleString("pt-BR", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    }

    if (format === "LONG") {
      // Formato por extenso: 22 de novembro de 2023
      const day = parsedDate.getDate();
      const month = parsedDate.toLocaleString("pt-BR", { month: "long" });
      const year = parsedDate.getFullYear();
      return `${day} de ${month} de ${year}`;
    }

    return parsedDate.toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: format == "DISPLAY" ? "short" : "2-digit",
      year: "numeric",
    });
  } catch (e) {
    console.error("Erro ao formatar data:", e);
    return "Data inválida";
  }
}

export const formatStringDate = (dateStr: string): string => {
  const [year, month, day] = dateStr.split("-");
  return `${day}/${month}/${year}`;
};

export function parseObject(object: any) {
  if (object === null || object === undefined) {
    return object;
  }

  try {
    // Converter valores Decimal e Date para formatos adequados antes de serializar
    const processObject = (obj: any): any => {
      if (obj === null || obj === undefined) return obj;

      // Verificar se é um BigInt
      if (typeof obj === 'bigint') {
        return Number(obj);
      }

      // Verificar se é uma data
      if (obj instanceof Date) {
        try {
          // Verificar se a data é válida
          if (isNaN(obj.getTime())) {
            console.warn("Data inválida detectada:", obj);
            return null; // Retornar null para datas inválidas
          }
          return obj.toISOString(); // Converter para string ISO
        } catch (e) {
          console.error("Erro ao processar data:", e);
          return null;
        }
      }

      if (typeof obj === "object") {
        // Verificar se é um objeto Decimal
        if (obj && obj.constructor && obj.constructor.name === "Decimal") {
          try {
            const numValue = Number(obj.toString());
            // Verificar se o número é válido
            if (isNaN(numValue)) {
              console.warn("Valor decimal inválido detectado:", obj);
              return 0; // Valor padrão seguro
            }
            return numValue;
          } catch (e) {
            console.error("Erro ao converter Decimal:", e);
            return 0; // Valor padrão seguro
          }
        }

        // Verificar se é um objeto com formato Decimal do Prisma (s, e, d)
        if (
          obj &&
          typeof obj === "object" &&
          "s" in obj &&
          "e" in obj &&
          "d" in obj &&
          Array.isArray(obj.d)
        ) {
          try {
            // Tentar usar toString() primeiro
            if (typeof obj.toString === "function") {
              try {
                const numValue = Number(obj.toString());
                if (!isNaN(numValue)) {
                  return numValue;
                }
              } catch (e) {
                console.warn(
                  "Erro ao usar toString() do Decimal, tentando método alternativo",
                  e
                );
              }
            }

            // Método alternativo: extrair o valor diretamente da representação
            // Para o formato { s: 1, e: 1, d: [10] } que representa 1.0
            const sign = obj.s >= 0 ? 1 : -1;
            const digits = parseInt(obj.d.join(""), 10);

            // No formato do Prisma, e positivo significa mover a vírgula para a esquerda
            // e negativo significa mover para a direita
            let numValue: number;
            if (obj.e > 0) {
              // Dividir por 10^e (mover vírgula para a esquerda)
              numValue = sign * (digits / Math.pow(10, obj.e));
            } else if (obj.e < 0) {
              // Multiplicar por 10^(-e) (mover vírgula para a direita)
              numValue = sign * (digits * Math.pow(10, -obj.e));
            } else {
              // e = 0, não precisa ajustar a vírgula
              numValue = sign * digits;
            }

            if (isNaN(numValue)) {
              console.warn("Valor decimal inválido detectado:", obj);
              return 0;
            }
            return numValue;
          } catch (e) {
            console.error(
              "Erro ao converter objeto Decimal do Prisma:",
              e,
              obj
            );
            return 0;
          }
        }

        // Verificar se é um array
        if (Array.isArray(obj)) {
          return obj.map((item) => {
            try {
              return processObject(item);
            } catch (e) {
              console.error("Erro ao processar item do array:", e);
              return null;
            }
          });
        }

        // Processar objeto normal
        const result: any = {};
        for (const key in obj) {
          if (Object.prototype.hasOwnProperty.call(obj, key)) {
            try {
              result[key] = processObject(obj[key]);
            } catch (e) {
              console.error(`Erro ao processar propriedade ${key}:`, e);
              result[key] = null;
            }
          }
        }
        return result;
      }

      return obj;
    };

    const processed = processObject(object);

    // Usar um replacer personalizado para lidar com BigInt
    const jsonString = JSON.stringify(processed, (key, value) => {
      if (typeof value === 'bigint') {
        return Number(value);
      }
      return value;
    });

    if (!jsonString) {
      console.error("JSON.stringify retornou uma string vazia ou nula");
      return {}; // Retornar objeto vazio como fallback
    }

    return JSON.parse(jsonString);
  } catch (error) {
    console.error("Erro ao processar objeto:", error);
    return {}; // Retornar objeto vazio como fallback
  }
}

export function formatCurrency(
  value: number | string | null | undefined,
  locale = "pt-BR",
  currency = "BRL"
) {
  // Verificar se o valor é válido
  if (value === null || value === undefined) {
    return new Intl.NumberFormat(locale, {
      style: "currency",
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(0);
  }

  // Converter para número se for string
  let numValue: number;
  if (typeof value === "string") {
    try {
      // Remover caracteres não numéricos exceto ponto e vírgula
      const cleanValue = value.replace(/[^0-9.,]/g, "");
      // Substituir vírgula por ponto para conversão correta
      const normalizedValue = cleanValue.replace(",", ".");
      numValue = parseFloat(normalizedValue);
    } catch (e) {
      console.error("Erro ao converter string para número:", e);
      numValue = 0;
    }
  } else {
    numValue = value;
  }

  // Verificar se o número é válido
  if (isNaN(numValue)) {
    numValue = 0;
  }

  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(numValue);
}

export function formatFileName(fileName: string): string {
  // Extrai a extensão do arquivo
  const extension = fileName.substring(fileName.lastIndexOf("."));

  // // Gera um timestamp
  // const timestamp = Date.now();

  // Substitui espaços por hífens e remover caracteres especiais
  const baseName = fileName
    .substring(0, fileName.lastIndexOf("."))
    .toLowerCase()
    .replace(/\s+/g, "-") // Substitui espaços por hífens
    .replace(/[^a-z0-9.-]/g, "") // Remove caracteres especiais
    .replace(/-+/g, "-"); // Substitui múltiplos hífens por um só

  return `${baseName}${extension}`;
  // Combina o timestamp com o nome do arquivo formatado
  // return `${baseName}-${timestamp}${extension}`;
}

export function getPeriodCount(
  startDate: Date | string,
  endDate: Date | string,
  frequency: "MONTHLY" | "WEEKLY" | "NONE"
): number {
  // Ensure the end date is after the start date

  if (typeof startDate === "string") {
    startDate = new Date(startDate);
  }

  if (typeof endDate === "string") {
    endDate = new Date(endDate);
  }

  if (endDate < startDate) {
    throw new Error("End date must be after start date.");
  }

  // Calculate based on the frequency
  if (frequency === "MONTHLY") {
    const startYear = startDate.getFullYear();
    const startMonth = startDate.getMonth();
    const endYear = endDate.getFullYear();
    const endMonth = endDate.getMonth();

    // Calculate the total number of months between the two dates
    return (endYear - startYear) * 12 + (endMonth - startMonth) + 1;
  } else if (frequency === "WEEKLY") {
    const millisecondsPerWeek = 1000 * 60 * 60 * 24 * 7;

    // Calculate the total number of weeks between the two dates
    return Math.ceil(
      (endDate.getTime() - startDate.getTime()) / millisecondsPerWeek
    );
  } else if (frequency === "NONE") {
    return 0; //
  } else {
    throw new Error("Unsupported frequency. Use 'MONTHLY' or 'WEEKLY'.");
  }
}

export const parseCurrencyToNumber = (value: string | number): number => {
  // Se o valor for um número, retorna-o diretamente
  if (typeof value === "number") return value;

  // Se o valor for undefined, null ou string vazia, retorna 0
  if (!value) return 0;

  // Converte para string para garantir que podemos usar o método replace
  const valueStr = String(value);

  // Remove qualquer caractere que não seja número ou vírgula
  const cleanedValue = valueStr.replace(/[^0-9,]/g, "");

  // Substitui a vírgula decimal por ponto
  const normalizedValue = cleanedValue.replace(",", ".");

  // Converte para número e retorna 0 se for NaN
  const result = parseFloat(normalizedValue);
  return isNaN(result) ? 0 : result;
};

export function toKebabCase(str: string): string {
  return str
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "") // Remove accents
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-") // Replace non-alphanumeric characters with hyphens
    .replace(/^-+|-+$/g, ""); // Remove leading and trailing hyphens
}

export function convertBigInt(obj: any) {
  return JSON.parse(
    JSON.stringify(obj, (_, value) =>
      typeof value === "bigint" ? value.toString() : value
    )
  );
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
