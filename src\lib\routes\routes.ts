import {
  Bolt,
  BriefcaseBusiness,
  FileChartColumn,
  FileChartPie,
  FileText,
  FileType2,
  HardHat,
  Home,
  Kanban,
  LucideIcon,
  Settings,
  Shield,
  TrendingUp,
  UserPlus,
  Users,
  FileX2,
  FileClock,
  FileCheck2,
  FileBox,
  FileScan,
  FolderSearch,
} from "lucide-react";

export interface RouteDataInterface {
  title: string;
  url: string;
  icon: LucideIcon;
  items?: RouteDataInterface[];
  disableLink?: boolean;
  routerGroup?: boolean;
}

export const routeData: RouteDataInterface[] = [
  {
    title: "Painel de controle",
    url: "/views/control-panel",
    icon: Home,
  },
  {
    title: "Kanban de projetos",
    url: "/views/crm/proposals/management",
    icon: Kanban,
  },
  {
    title: "CRM",
    url: "",
    icon: BriefcaseBusiness,
    routerGroup: true,
    items: [
      {
        title: "Clientes",
        url: "", // Sem rota, apenas para expandir
        icon: Users,
        items: [
          {
            title: "Cadastrar cliente",
            url: "/views/crm/customers",
            icon: UserPlus,
          },
          {
            title: "Histórico de projetos por cliente",
            url: "/views/crm/customer-projects-history",
            icon: FileText,
          },
        ],
      },
      {
        title: "Propostas",
        url: "", // Sem rota, apenas para expandir
        icon: FileText,
        items: [
          {
            title: "Cadastrar proposta",
            url: "/views/crm/proposals",
            icon: FileText,
          },
          {
            title: "Propostas por período",
            url: "/views/crm/proposals/by-period",
            icon: Kanban,
          },
        ],
      },
      {
        title: "Contratos",
        url: "", // Sem rota, apenas para expandir
        icon: FileBox,
        items: [
          {
            title: "A iniciar",
            url: "/views/crm/proposals/to-start",
            icon: FileText,
          },
          {
            title: "Em andamento",
            url: "/views/crm/proposals/accepted",
            icon: FileClock,
          },
          {
            title: "Concluídos",
            url: "/views/crm/proposals/completed",
            icon: FileCheck2,
          },
          {
            title: "Perdidos",
            url: "/views/crm/proposals/lost",
            icon: FileX2,
          },
        ],
      },
      {
        title: "KPI",
        url: "/views/crm/kpi",
        icon: TrendingUp,
      },
    ],
  },
  {
    title: "Laudos e Relatórios",
    url: "",
    icon: FileChartPie,
    items: [
      {
        title: "Laudos",
        url: "", // Sem rota, apenas para expandir
        icon: FileChartColumn,
        items: [
          {
            title: "Inspeção",
            url: "/views/inspection-report",
            icon: FileChartColumn,
          },
          {
            title: "Projetos",
            url: "/views/project-report",
            icon: FileScan,
          },
        ],
      },
      {
        title: "Relatórios",
        url: "", // Sem rota, apenas para expandir
        icon: FileBox,
        items: [
          {
            title: "Fiscalização e Gerenciamento",
            url: "/views/construction-inspection",
            icon: FileBox,
          },
          {
            title: "Consultoria",
            url: "/views/consultancy-report",
            icon: FolderSearch,
          },
          // {
          //   title: "Impacto Climático",
          //   url: "/views/weather-impact",
          //   icon: CloudSunRain,
          // },
        ],
      },
    ],
  },

  {
    title: "Configuração",
    url: "",
    icon: Settings,
    items: [
      {
        title: "Escopo de serviços",
        url: "/views/services-scopes",
        icon: Bolt,
      },
      {
        title: "Templates do Sistema",
        url: "", // Sem rota, apenas para expandir
        icon: FileType2,
        items: [
          {
            title: "Proposta",
            url: "/views/proposal-templates?type=PROPOSAL",
            icon: FileText,
          },
          {
            title: "Contrato",
            url: "/views/proposal-templates?type=CONTRACT",
            icon: FileBox,
          },
          // {
          //   title: "Fiscalização e Gerenciamento",
          //   url: "/views/proposal-templates?type=SUPERVISION",
          //   icon: FileChartPie,
          // },
          {
            title: "Inspeção",
            url: "/views/proposal-templates?type=INSPECTION",
            icon: FileChartColumn,
          },
          {
            title: "Projetos",
            url: "/views/proposal-templates?type=PROJECT",
            icon: FileScan,
          },
          {
            title: "Consultoria",
            url: "/views/proposal-templates?type=CONSULTANCY",
            icon: FolderSearch,
          },
          // {
          //   title: "Outros Templates",
          //   url: "/views/report-templates",
          //   icon: Files,
          // },
        ],
      },

      {
        title: "Equipamentos e mão de obra",
        url: "/views/labor-equipaments",
        icon: HardHat,
      },
      {
        title: "Gerenciar membros",
        url: "/views/manage-members",
        icon: Users,
      },
      {
        title: "Permissões de acesso",
        url: "/views/manage-permissions",
        icon: Shield,
      },
    ],
  },
];
