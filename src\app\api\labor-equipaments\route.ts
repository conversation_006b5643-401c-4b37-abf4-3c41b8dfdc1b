import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

export const dynamic = "force-dynamic";

export async function GET(request: Request) {
  try {
    const { organizationId } = await getCurrentOrganization();
    const { searchParams } = new URL(request.url);

    const page = Number(searchParams.get("page")) || 1;
    const pageSize = Number(searchParams.get("pageSize")) || 10;
    const search = searchParams.get("search") || "";

    const skip = (page - 1) * pageSize;

    const where = {
      organizationId,
      ...(search
        ? {
            OR: [
              { name: { contains: search, mode: "insensitive" as any } },
              { description: { contains: search, mode: "insensitive" as any } },
            ],
          }
        : {}),
    };

    const [total, items] = await Promise.all([
      prisma.labor.count({ where }),
      prisma.labor.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: { createdAt: "desc" },
      }),
    ]);

    return NextResponse.json({
      data: items,
      page,
      pageSize,
      total,
      totalPages: Math.ceil(total / pageSize),
    });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Erro ao carregar equipamentos e mão de obra" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const { organizationId } = await getCurrentOrganization();
    const data = await request.json();

    const newLabor = await prisma.labor.create({
      data: {
        name: data.name,
        description: data.description,
        type: data.type,
        laborType: data.laborType,
        organizationId
      },
    });

    return NextResponse.json(newLabor);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Erro ao criar item" },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  try {
    const { organizationId } = await getCurrentOrganization();
    const data = await request.json();

    const updatedLabor = await prisma.labor.update({
      where: {
        id: data.id,
        organizationId
      },
      data: {
        name: data.name,
        description: data.description,
        type: data.type,
        laborType: data.laborType,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json(updatedLabor);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Erro ao atualizar item" },
      { status: 500 }
    );
  }
}