/*
  Warnings:

  - You are about to drop the column `periodicity` on the `RepairBudget` table. All the data in the column will be lost.
  - Added the required column `label` to the `PlanningFrequencyItem` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "ServiceType" AS ENUM ('PROPOSAL_SERVICE', 'REPAIR_SERVICE');

-- AlterTable
ALTER TABLE "PlanningFrequencyItem" ADD COLUMN     "label" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "Proposal" ADD COLUMN     "address" TEXT,
ADD COLUMN     "cep" TEXT,
ADD COLUMN     "city" TEXT,
ADD COLUMN     "state" TEXT;

-- AlterTable
ALTER TABLE "RepairBudget" DROP COLUMN "periodicity",
ADD COLUMN     "buildingPercentage" DECIMAL(65,30) NOT NULL DEFAULT 0,
ADD COLUMN     "description" TEXT,
ADD COLUMN     "equipmentAmount" INTEGER,
ADD COLUMN     "equipmentDescription" TEXT,
ADD COLUMN     "imp" DECIMAL(65,30) NOT NULL DEFAULT 0,
ADD COLUMN     "laborAmount" INTEGER;

-- AlterTable
ALTER TABLE "ServiceScope" ADD COLUMN     "types" "ServiceType"[];

-- CreateTable
CREATE TABLE "Productivity" (
    "id" TEXT NOT NULL,
    "order" INTEGER NOT NULL DEFAULT 0,
    "label" TEXT NOT NULL,
    "periodicity" TEXT NOT NULL,
    "serviceId" TEXT NOT NULL,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "buildingPercentage" DECIMAL(65,30) NOT NULL DEFAULT 0,
    "predictedPeriodPercentage" DECIMAL(65,30),
    "realPeriodPercentage" DECIMAL(65,30),
    "description" TEXT,
    "workersQuantity" INTEGER NOT NULL,
    "toolsQuantity" INTEGER,
    "toolsDescription" TEXT,
    "repairBudgetId" TEXT NOT NULL,
    "proposalId" TEXT NOT NULL,

    CONSTRAINT "Productivity_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Pluviosity" (
    "id" TEXT NOT NULL,
    "measurementDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "value" DECIMAL(65,30) NOT NULL,
    "city" TEXT NOT NULL,

    CONSTRAINT "Pluviosity_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_PlanningFrequencyItemToRepairBudget" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "_PlanningFrequencyItemToRepairBudget_AB_unique" ON "_PlanningFrequencyItemToRepairBudget"("A", "B");

-- CreateIndex
CREATE INDEX "_PlanningFrequencyItemToRepairBudget_B_index" ON "_PlanningFrequencyItemToRepairBudget"("B");

-- AddForeignKey
ALTER TABLE "Productivity" ADD CONSTRAINT "Productivity_proposalId_fkey" FOREIGN KEY ("proposalId") REFERENCES "Proposal"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Productivity" ADD CONSTRAINT "Productivity_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "ServiceScope"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Productivity" ADD CONSTRAINT "Productivity_repairBudgetId_fkey" FOREIGN KEY ("repairBudgetId") REFERENCES "RepairBudget"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_PlanningFrequencyItemToRepairBudget" ADD CONSTRAINT "_PlanningFrequencyItemToRepairBudget_A_fkey" FOREIGN KEY ("A") REFERENCES "PlanningFrequencyItem"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_PlanningFrequencyItemToRepairBudget" ADD CONSTRAINT "_PlanningFrequencyItemToRepairBudget_B_fkey" FOREIGN KEY ("B") REFERENCES "RepairBudget"("id") ON DELETE CASCADE ON UPDATE CASCADE;
