"use client";

import { ReportType } from "@prisma/client";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { fetchReportTemplateListByType } from "@/src/actions/report-template";
import { toast } from "../hooks/use-toast";
import { Button } from "./ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "./ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";

type SelectItemType = {
  label: string;
  value: string;
};

interface DownloadReportDialogProps {
  reportType: ReportType;
  openUploadDialog: boolean;
  additionalParamsOptional?: any;
  setOpenUploadDialog: (value: any) => void;
  reportMergeAction: (
    templateKey: string,
    additionalParams?: any
  ) => Promise<{ fileEditorId: string } | undefined>;
}

export default function DownloadReportDialog({
  reportType,
  reportMergeAction,
  openUploadDialog,
  setOpenUploadDialog,
  additionalParamsOptional,
}: DownloadReportDialogProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [reportTemplateKey, setReportTemplateKey] = useState<
    string | undefined
  >();
  const [reportTemplateList, setReportTemplateList] = useState<
    SelectItemType[]
  >([]);
  const router = useRouter();

  const fetchReportTemplateList = async () => {
    const templateList = await fetchReportTemplateListByType(reportType);
    if (templateList) {
      setReportTemplateList(
        templateList.map((t) => {
          return {
            value: t.id,
            label: t.title,
          };
        })
      );
    }
  };

  useEffect(() => {
    if (openUploadDialog) {
      fetchReportTemplateList();
    }
  }, [openUploadDialog]);

  const handleReportMerge = async () => {
    if (reportTemplateKey) {
      try {
        setIsGenerating(true);
        const response = await reportMergeAction(
          reportTemplateKey,
          additionalParamsOptional
        );
        if (response) {
          router.push(`/document-editor/${response.fileEditorId}`);
          setIsGenerating(false);
        }
      } catch (error) {
        console.error(error);
        toast({
          title: "Erro",
          description: "Falha ao gerar relatório.",
          variant: "destructive",
        });
        setIsGenerating(false);
      }
    }
  };

  return (
    <Dialog
      open={openUploadDialog}
      onOpenChange={(open) => setOpenUploadDialog(open)}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="font-bold text-xl">
            Gerar relatório pelo modelo
          </DialogTitle>
          <DialogDescription>
            Selecione o modelo de template para gerar o relatório
          </DialogDescription>
          <div className="flex flex-col items-center justify-center p-6 rounded-lg w-full max-w-md space-y-4">
            {reportTemplateList && (
              <Select
                onValueChange={(value) => setReportTemplateKey(value)}
                value={reportTemplateKey}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o modelo" />
                </SelectTrigger>

                <SelectContent>
                  {reportTemplateList?.map(({ value, label }, index) => (
                    <SelectItem value={value} key={index}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}

            <Button
              onClick={handleReportMerge}
              disabled={isGenerating || !reportTemplateKey}
              className="w-full"
            >
              {isGenerating ? "Gerando..." : "Gerar relatório"}
            </Button>
          </div>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}
