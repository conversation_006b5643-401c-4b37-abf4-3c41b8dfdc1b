"use client";

import { loadProposalsByStatus } from "@/src/actions/kpi";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/src/components/ui/chart";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card";
import { useEffect, useState } from "react";
import { Cell, Pie, PieChart } from "recharts";

interface StatusDistributionChartProps {
  startDate?: Date;
  endDate?: Date;
  isMobile?: boolean;
}

export default function StatusDistributionChart({ startDate, endDate, isMobile = false }: StatusDistributionChartProps) {
  const [statusData, setStatusData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  const chartConfig: ChartConfig = {
    value: {
      label: "Distribuição",
      color: "rgb(22 163 74)",
    },
  };

  const COLORS = [
    "#22c55e", // green-500
    "#3b82f6", // blue-500
    "#f97316", // orange-500
    "#8b5cf6", // violet-500
    "#ef4444", // red-500
  ];

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await loadProposalsByStatus(startDate, endDate);
        setStatusData(data);
      } catch (error) {
        console.error("Erro ao carregar dados de status:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [startDate, endDate]);

  // Calcular o total de propostas
  const totalProposals = statusData?.reduce((sum, item) => sum + item.count, 0) || 0;

  return (
    <Card className="w-full" id="status-distribution-chart">
      <CardHeader className="pb-2">
        <CardTitle className={`${isMobile ? 'text-lg' : 'text-xl'} font-bold`}>Distribuição por Status</CardTitle>
        <CardDescription className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
          <span className="text-xs sm:text-sm">Propostas por status</span>
          <span className="text-base sm:text-lg font-semibold text-gray-600 mt-1 sm:mt-0">
            Total: {totalProposals}
          </span>
        </CardDescription>
      </CardHeader>
      <CardContent className={`${isMobile ? 'h-60' : 'h-80'}`}>
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <span>Carregando...</span>
          </div>
        ) : (
          <ChartContainer
            config={chartConfig}
            className="h-full w-full"
          >
            <PieChart
              margin={isMobile ? { top: 0, right: 0, left: 0, bottom: 0 } : { top: 10, right: 10, left: 10, bottom: 10 }}
            >
              <Pie
                data={statusData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={isMobile ? 80 : 120}
                innerRadius={isMobile ? 40 : 60}
                fill="#8884d8"
                dataKey="count"
                nameKey="status"
                label={({ name, percent }) => {
                  const value = `${(percent * 100).toFixed(0)}%`;
                  return isMobile ?
                    value :
                    `${name}: ${value}`;
                }}
              >
                {statusData.map((_, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <ChartTooltip content={<ChartTooltipContent />} />
            </PieChart>
          </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
}
