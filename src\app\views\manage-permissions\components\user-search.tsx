"use client";

import { useState, useEffect } from "react";
import { Combobox, ComboboxOption } from "@/components/ui/combobox";
import { useToast } from "@/src/hooks/use-toast";

interface UserSearchProps {
  onUserChange: (userId: string) => void;
  className?: string;
}

export function UserSearch({ onUserChange, className = "" }: UserSearchProps) {
  const { toast } = useToast();
  const [userOptions, setUserOptions] = useState<ComboboxOption[]>([]);
  const [selectedUser, setSelectedUser] = useState<string>("");

  // Função para carregar os usuários iniciais
  const fetchInitialUsers = async () => {
    try {
      const response = await fetch("/api/users/search");
      if (!response.ok) {
        throw new Error("Falha ao carregar usuários");
      }
      const data = await response.json();
      // Garantir que o resultado seja sempre um array
      if (!Array.isArray(data)) {
        console.warn("API retornou dados não esperados:", data);
        setUserOptions([]);
      } else {
        setUserOptions(data);
      }
    } catch (error) {
      console.error("Erro ao carregar usuários:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar a lista de usuários",
        variant: "destructive",
      });
      setUserOptions([]);
    }
  };

  // Função para buscar usuários com base no termo de pesquisa
  const searchUsers = async (search: string): Promise<ComboboxOption[]> => {
    try {
      // Se a pesquisa estiver vazia, retornar todos os usuários
      if (!search.trim()) {
        const response = await fetch(`/api/users/search`);
        if (!response.ok) {
          throw new Error("Falha ao buscar usuários");
        }
        const data = await response.json();
        return Array.isArray(data) ? data : [];
      }

      // Limpar e normalizar o termo de busca
      const cleanSearch = search.trim();

      const response = await fetch(`/api/users/search?search=${encodeURIComponent(cleanSearch)}`);
      if (!response.ok) {
        throw new Error("Falha ao buscar usuários");
      }
      const data = await response.json();

      // Garantir que o resultado seja sempre um array
      if (!Array.isArray(data)) {
        console.warn("API retornou dados não esperados:", data);
        return [];
      }

      // Ordenar resultados por relevância (exato primeiro, depois parcial)
      const sortedResults = data.sort((a, b) => {
        const aLabel = a.label.toLowerCase();
        const bLabel = b.label.toLowerCase();
        const searchLower = cleanSearch.toLowerCase();

        // Priorizar correspondências exatas
        if (aLabel === searchLower && bLabel !== searchLower) return -1;
        if (bLabel === searchLower && aLabel !== searchLower) return 1;

        // Priorizar correspondências que começam com o termo
        if (aLabel.startsWith(searchLower) && !bLabel.startsWith(searchLower)) return -1;
        if (bLabel.startsWith(searchLower) && !aLabel.startsWith(searchLower)) return 1;

        // Ordenar alfabeticamente para o resto
        return aLabel.localeCompare(bLabel);
      });

      return sortedResults;
    } catch (error) {
      console.error("Erro ao buscar usuários:", error);
      toast({
        title: "Erro",
        description: "Não foi possível buscar usuários",
        variant: "destructive",
      });
      return [];
    }
  };

  // Carregar usuários ao montar o componente
  useEffect(() => {
    fetchInitialUsers();
  }, []);

  // Notificar o componente pai quando o usuário selecionado mudar
  useEffect(() => {
    onUserChange(selectedUser);
  }, [selectedUser, onUserChange]);

  return (
    <div className={className}>
      <Combobox
        options={userOptions}
        value={selectedUser}
        onChange={setSelectedUser}
        placeholder="Selecione um usuário"
        searchPlaceholder="Digite o nome do usuário..."
        emptyMessage="Nenhum usuário encontrado"
        onSearch={searchUsers}
        className="w-full text-green-700 font-medium"
        customStyles={{
          trigger: "border-green-100 bg-green-50 hover:bg-green-100 hover:border-green-200 text-green-700",
          search: "border-green-100 focus:border-green-300 focus:ring-green-200",
          searchIcon: "text-green-500",
          option: "hover:bg-green-50 hover:text-green-700 focus:bg-green-50 focus:text-green-700",
          selectedIcon: "text-green-600",
          content: "border-green-100",
          emptyMessage: "text-green-600"
        }}
      />
    </div>
  );
} 