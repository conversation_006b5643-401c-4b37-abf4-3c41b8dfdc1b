import KanbanBoard from "./components/kanban-board";

export default function Proposalsmanegement() {
	return (
		<section
			className="bg-white p-3 shadow-lg rounded-xl h-auto min-h-[calc(100vh-10rem)] max-w-full overflow-hidden"
		>
			<div className="flex flex-col h-full w-full p-2">
				<div className="flex items-center justify-between mb-4 pb-3 border-b">
					<div>
						<h1 className="text-3xl font-semibold text-green-500">
							Gerenciamento de Propostas
						</h1>
						<p className="text-gray-500 text-sm mt-1">
							<PERSON><PERSON><PERSON> e solte as propostas entre as colunas para atualizar seu status
						</p>
					</div>
				</div>
				<div className="w-full h-full overflow-hidden">
					<div className="md:hidden text-xs text-gray-500 mb-2 flex items-center justify-center">
						<span>← Deslize para navegar entre as colunas →</span>
					</div>
					<KanbanBoard />
				</div>
			</div>
		</section>
	);
}
