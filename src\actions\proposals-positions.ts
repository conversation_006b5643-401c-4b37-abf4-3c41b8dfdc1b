"use server";

import { prisma } from "@/src/lib/prisma";
import { Proposal, ProposalSituation } from "@/src/types/core/proposal";

export async function updateProposalsPositions(proposals: Partial<Proposal>[]) {
  try {
    // Atualizar cada proposta
    const updatePromises = proposals.map(async (proposal) => {
      if (!proposal.id) return null;

      return prisma.proposal.update({
        where: { id: proposal.id },
        data: {
          situation: proposal.situation as ProposalSituation,
          order: proposal.order || 0,
        },
      });
    });

    // Aguardar todas as atualizações
    const results = await Promise.all(updatePromises);
    return { success: true, results };
  } catch (error) {
    console.error("Erro ao atualizar posições das propostas:", error);
    return { success: false, error };
  }
}
