/*
  Warnings:

  - You are about to drop the column `content` on the `ProposalTemplate` table. All the data in the column will be lost.
  - You are about to drop the column `description` on the `ProposalTemplate` table. All the data in the column will be lost.
  - You are about to drop the column `fileId` on the `ProposalTemplate` table. All the data in the column will be lost.
  - You are about to drop the column `title` on the `ProposalTemplate` table. All the data in the column will be lost.
  - You are about to drop the `_PlanningFrequencyItemToRepairBudget` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[key]` on the table `ProposalTemplate` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `bucket` to the `ProposalTemplate` table without a default value. This is not possible if the table is not empty.
  - Added the required column `filename` to the `ProposalTemplate` table without a default value. This is not possible if the table is not empty.
  - Added the required column `key` to the `ProposalTemplate` table without a default value. This is not possible if the table is not empty.
  - Added the required column `mimetype` to the `ProposalTemplate` table without a default value. This is not possible if the table is not empty.
  - Added the required column `size` to the `ProposalTemplate` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `ProposalTemplate` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "LaborType" AS ENUM ('DIRECT', 'INDIRECT', 'OUTSOURCED');

-- CreateEnum
CREATE TYPE "LaborEquipmentType" AS ENUM ('EQUIPAMENT', 'LABOR');

-- DropForeignKey
ALTER TABLE "ProposalTemplate" DROP CONSTRAINT "ProposalTemplate_fileId_fkey";

-- DropForeignKey
ALTER TABLE "_PlanningFrequencyItemToRepairBudget" DROP CONSTRAINT "_PlanningFrequencyItemToRepairBudget_A_fkey";

-- DropForeignKey
ALTER TABLE "_PlanningFrequencyItemToRepairBudget" DROP CONSTRAINT "_PlanningFrequencyItemToRepairBudget_B_fkey";

-- AlterTable
ALTER TABLE "PlanningFrequencyItem" ADD COLUMN     "repairBudgetId" TEXT;

-- AlterTable
ALTER TABLE "ProposalTemplate" DROP COLUMN "content",
DROP COLUMN "description",
DROP COLUMN "fileId",
DROP COLUMN "title",
ADD COLUMN     "bucket" TEXT NOT NULL,
ADD COLUMN     "filename" TEXT NOT NULL,
ADD COLUMN     "isLocked" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "key" TEXT NOT NULL,
ADD COLUMN     "lockedBy" TEXT[],
ADD COLUMN     "mimetype" TEXT NOT NULL,
ADD COLUMN     "size" INTEGER NOT NULL,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "version" BIGINT;

-- DropTable
DROP TABLE "_PlanningFrequencyItemToRepairBudget";

-- CreateTable
CREATE TABLE "Labor" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "type" "LaborEquipmentType" NOT NULL,
    "laborType" "LaborType",
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Labor_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LaborEquipmentAmount" (
    "id" TEXT NOT NULL,
    "amount" INTEGER,
    "laborId" TEXT NOT NULL,
    "repairBudgetId" TEXT NOT NULL,

    CONSTRAINT "LaborEquipmentAmount_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ProposalTemplate_key_key" ON "ProposalTemplate"("key");

-- AddForeignKey
ALTER TABLE "PlanningFrequencyItem" ADD CONSTRAINT "PlanningFrequencyItem_repairBudgetId_fkey" FOREIGN KEY ("repairBudgetId") REFERENCES "RepairBudget"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LaborEquipmentAmount" ADD CONSTRAINT "LaborEquipmentAmount_repairBudgetId_fkey" FOREIGN KEY ("repairBudgetId") REFERENCES "RepairBudget"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LaborEquipmentAmount" ADD CONSTRAINT "LaborEquipmentAmount_laborId_fkey" FOREIGN KEY ("laborId") REFERENCES "Labor"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
