"use client";

import { loadKpiData } from "@/src/actions/kpi";
import {
	ChartConfig,
	ChartContainer,
	ChartLegend,
	ChartLegendContent,
	ChartTooltip,
	ChartTooltipContent,
} from "@/src/components/ui/chart";
import { useEffect, useState } from "react";
import { CartesianGrid, Line, LineChart, XAxis, YAxis } from "recharts";

export default function KpiCharts() {
	const [kpiData, setKpiData] = useState<any>()
	const [maxTotalBudget, setMaxTotalBudget] = useState<number>(0);

	const chartConfig: ChartConfig = {
		conversionRate: {
			label: "Taxa de Conversão de Propostas",
			color: "rgb(37 99 235)",
		},
		totalIncome: {
			label: "Valor Total de faturamento",
			color: "rgb(22 163 74)",
		},
		valuePerSquareMeter: {
			label: "Valor por m²",
			color: "rgb(255 153 0)",
		},
	};

	const fetchKpiData = async () => {
		try {
			const data = await loadKpiData()
			if (data) {
				const maxBudget = Math.max(...data.map((item: any) => item.totalIncome));
				setMaxTotalBudget(maxBudget);
				setKpiData(data);
			}
		} catch (error) {
			console.error(error)
		}
	}

	useEffect(() => {
		fetchKpiData()
	}, [])

	return (
		<div className="flex flex-col gap-4 w-full h-full">
			<ChartContainer
				config={chartConfig}
				className="min-h-[200px] w-full max-h-full"
			>
				<LineChart accessibilityLayer data={kpiData}>
					<CartesianGrid vertical={false} />S
					<XAxis
						dataKey="monthYear"
						tickLine={false}
						tickMargin={10}
						axisLine={true}
					/>
					<YAxis
						type="number"
						tickLine={true}
						axisLine={true}
						tickMargin={10}
						width={100}
						yAxisId="left"
						domain={[0, maxTotalBudget * 1.1]}
						tickFormatter={(value) => `R$ ${value.toLocaleString('pt-BR')}`}
					/>
					<YAxis
						type="number"
						tickLine={true}
						axisLine={true}
						tickMargin={10}
						yAxisId="right"
						orientation="right"
						domain={[0, 100]}
						unit="%"
						name="Taxa de conversão"
					/>

					<ChartTooltip content={<ChartTooltipContent currencyLines={['totalIncome', 'valuePerSquareMeter']} />} cursor={false} />
					<ChartLegend content={<ChartLegendContent />} />

					<Line
						yAxisId="right"
						dataKey="conversionRate"
						name="Taxa de conversão em %"
						fill="var(--color-conversionRate)"
						stroke="var(--color-conversionRate)"
						type="monotone"
						radius={4}
					/>
					<Line
						yAxisId="left"
						dataKey="valuePerSquareMeter"
						name="Valor por m²"
						fill="var(--color-valuePerSquareMeter)"
						stroke="var(--color-valuePerSquareMeter)"
						type="monotone"
						radius={4}
					/>
					<Line
						yAxisId="left"
						dataKey="totalIncome"
						name="Valor total de propostas"
						fill="var(--color-totalIncome)"
						stroke="var(--color-totalIncome)"
						type="monotone"
						radius={4}
					/>
				</LineChart>
			</ChartContainer>
		</div>
	);
}
