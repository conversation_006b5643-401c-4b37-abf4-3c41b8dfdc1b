"use client"
import { CustomInput } from "@/src/components/app-input";
import { Button } from "@/src/components/ui/button";
import { Dialog, DialogTitle, DialogContent, DialogHeader, DialogDescription, DialogFooter } from "@/src/components/ui/dialog";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { LaborEquipamentsSchema } from "../schemas/labor-equipament.schema";
import { useEffect } from "react";
import { saveLaborEquipament } from "@/src/actions/laborType";
import { toast } from "@/src/hooks/use-toast";


type LaborEquipamentsDialogProps = {
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
    loadLaborEquipament: () => Promise<void>;
    laborEquipament: any;
    currentPage?: number;
};
export default function DialogLaborEquipmentDetails({ isOpen, onOpenChange, laborEquipament, currentPage }: LaborEquipamentsDialogProps) {
    const methods = useForm<LaborEquipamentsSchema>({
        defaultValues: {
            name: "",
            description: "",
            laborType: "DIRECT",
            type: "LABOR"
        }
    })

    const { reset } = methods;

    useEffect(() => {
        if (laborEquipament) reset(laborEquipament);
        else
            reset({
                name: "",
                description: "",
                laborType: "DIRECT",
                type: "LABOR",
            });
    }, [isOpen, laborEquipament]);

    const onSubmit = methods.handleSubmit(async (laborEquipament: LaborEquipamentsSchema) => {
        try {
            const data = await saveLaborEquipament(laborEquipament);

            if (data?.error) {
                toast({
                    title: "Erro",
                    description: data.message || "Erro ao salvar item",
                    variant: "destructive"
                });
                return;
            }

            toast({
                title: "Sucesso",
                description: laborEquipament.id
                    ? "Item atualizado com sucesso!"
                    : "Item criado com sucesso!",
                variant: "default"
            });

            onOpenChange(false);
            reset();

            // Atualizar a tabela na página atual após salvar
            if (window && window.location) {
                const event = new CustomEvent('refreshLaborEquipaments', { detail: { page: currentPage } });
                window.dispatchEvent(event);
            }
        } catch (error) {
            console.error(error)
        }
    })

    useEffect(() => {
        if (!isOpen) {
            reset();
        }
    }, [isOpen]);

    return (
        <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent className="rounded-lg">
                <DialogHeader>
                    <DialogTitle>{laborEquipament ? "Editar" : "Adicionar"}</DialogTitle>
                    <DialogDescription>
                        Preencha as informações abaixo para
                        {laborEquipament ? " editar" : " adicionar"}
                    </DialogDescription>
                </DialogHeader>

                <Form {...methods}>
                    <form className="flex flex-col gap-6 mt-4" onSubmit={onSubmit}>
                        <CustomInput
                            name="type"
                            label="Escolha o tipo"
                            type="radio-group"
                            hideErrorMessage={true}
                            items={[
                                { label: "Mão de obra ", value: "LABOR" },
                                { label: "Equipamento", value: "EQUIPAMENT" },
                            ]}
                        />
                        <CustomInput
                            label="Nome"
                            name="name"
                            placeholder="Nome"
                        />

                        <CustomInput
                            label="Breve descrição"
                            name="description"
                            placeholder="Descrição"
                        />

                        <CustomInput
                            label="Selecione uma opção"
                            name="laborType"
                            placeholder="Selecione uma opção"
                            type="select"
                            items={[
                                { value: "DIRECT", label: "Direto" },
                                { value: "INDIRECT", label: "Indireto" },
                                { value: "OUTSOURCED", label: "Terceirizado" },
                            ]}
                        />

                        <DialogFooter className="flex gap-4 mt-6">
                            <Button
                                type="button"
                                variant="outline"
                                className="border-blue-500 text-blue-500 hover:text-blue-400 hover:border-blue-400"
                                onClick={() => onOpenChange(false)}
                            >
                                Cancelar
                            </Button>
                            <Button
                                type="submit"
                                variant="success"
                                disabled={methods.formState.isSubmitting}
                            >
                                {laborEquipament ? "Atualizar" : "Salvar"}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    )
}