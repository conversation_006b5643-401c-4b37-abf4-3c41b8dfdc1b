"use client"
import ContentWrapper from "@/src/components/content-wrapper";
import PrioritizationReportChart from "./components/prioritization-report-chart";
import { useState, useEffect } from "react";

export default function PrioritizationReport() {
    const [loading, setLoading] = useState(true);

    const fetchPrioritizationReports = async () => {
        try {

        } catch (e) {
            console.error(e)
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        fetchPrioritizationReports();
    }, [])

    return (
        <ContentWrapper title="Relatório de Priorização" loading={loading}>
            <PrioritizationReportChart />
        </ContentWrapper>
    );
}
