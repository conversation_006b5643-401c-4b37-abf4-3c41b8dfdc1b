"use client";

import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from "react";
import { TableGrid } from "@/src/components/ui/table-grid";
import { useToast } from "@/src/hooks/use-toast";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Eraser, Plus } from "lucide-react";
import { ProposalTemplateInterface } from "@/src/types/core/proposal-template";
import { ReportTemplateInterface } from "@/src/types/core/report-template";

interface ProposalTemplatesTableProps {
  columns: any[];
  onAddClick: () => void;
  onPageChange?: (page: number) => void;
  documentType?: string;
  isReport?: boolean; // Indica se estamos lidando com templates de relatório
}

export type ProposalTemplatesTableRef = {
  refresh: (page?: number) => void;
};

const ProposalTemplatesTable = forwardRef<ProposalTemplatesTableRef, ProposalTemplatesTableProps>(
  function ProposalTemplatesTable({ columns, onAddClick, onPageChange, documentType, isReport = false }, ref) {
    const [data, setData] = useState<(ProposalTemplateInterface | ReportTemplateInterface)[]>([]);
    const [loading, setLoading] = useState(false);
    const [search, setSearch] = useState("");
    const searchInputRef = useRef<HTMLInputElement>(null);
    const { toast } = useToast();
    const [pagination, setPagination] = useState({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });

    const fetchTemplates = useCallback(async (
      page?: number,
      pageSize: number = pagination.pageSize,
      searchTerm: string = search
    ) => {
      const currentPage = page || pagination.page;

      try {
        setLoading(true);
        const params = new URLSearchParams({
          page: String(currentPage),
          pageSize: String(pageSize),
          getFiles: "true"
        });

        if (searchTerm) {
          params.append('search', searchTerm);
        }

        let apiUrl = '/api/templates';

        if (isReport) {
          // Para templates de relatório
          apiUrl = '/api/report-templates';
          // Determinar o tipo de relatório com base no tipo de documento
          if (documentType === 'PROJECT') {
            params.append('type', 'PROJECT');
          } else if (documentType === 'INSPECTION') {
            params.append('type', 'REPORT');
          }
        } else {
          // Para templates de proposta, adicionar filtro por tipo
          if (documentType) {
            params.append('type', documentType);
          }
        }

        const response = await fetch(`${apiUrl}?${params}`);

        if (!response.ok) throw new Error(`Failed to fetch templates from ${apiUrl}`);

        const result = await response.json();

        setData(Array.isArray(result.data) ? result.data : []);
        setPagination(prev => ({
          ...prev,
          page: Number(result.page),
          pageSize: Number(result.pageSize),
          total: Number(result.total),
          totalPages: Number(result.totalPages)
        }));

      } catch (error) {
        console.error('Fetch error:', error);
        toast({
          title: "Erro",
          description: isReport
            ? "Erro ao carregar templates de relatórios"
            : "Erro ao carregar templates de propostas",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }, [pagination.pageSize, search, toast, documentType, isReport]);

    useImperativeHandle(ref, () => ({
      refresh: (page?: number) => fetchTemplates(page)
    }), [fetchTemplates]);

    // Carregar dados quando o componente for montado
    useEffect(() => {
      fetchTemplates(1);
    }, [fetchTemplates]);

    const handleSearch = (value: string) => {
      setSearch(value);
      fetchTemplates(1, pagination.pageSize, value);
    };

    const handleClearFilters = () => {
      // Limpar o estado de pesquisa
      setSearch("");

      // Resetar para a primeira página e buscar dados
      fetchTemplates(1, pagination.pageSize, "");

      // Atualizar a página atual no componente pai
      if (onPageChange) onPageChange(1);

      // Focar no campo de pesquisa após limpar
      setTimeout(() => {
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }, 0);
    };

    return (
      <TableGrid
        columns={columns}
        data={data}
        loading={loading}
        pagination={pagination}
        onPaginationChange={(newPage, newPageSize) => {
          fetchTemplates(newPage, newPageSize);
          if (onPageChange) onPageChange(newPage);
        }}
        onSearch={handleSearch}
        searchValue={search}
        searchInputRef={searchInputRef}
        buttonsTemplate={
          <>
            <Button
              className="w-full sm:w-auto bg-blue-500 hover:bg-blue-400"
              onClick={handleClearFilters}
            >
              Limpar filtros <Eraser className="ml-2 h-4 w-4" />
            </Button>
            <Button
              className="w-full sm:w-auto bg-green-500 hover:bg-green-400"
              onClick={onAddClick}
            >
              Adicionar <Plus className="ml-2 h-4 w-4" />
            </Button>
          </>
        }
      />
    );
  }
);

export default ProposalTemplatesTable;
