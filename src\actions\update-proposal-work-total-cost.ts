"use server";

import { prisma } from "@/src/lib/prisma";
import { parseCurrencyToNumber } from "@/src/lib/utils";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

export async function updateProposalWorkTotalCost(
  proposalId: string,
  totalCost: string | number
) {
  try {
    const { organizationId } = await getCurrentOrganization();

    // Verificar se a proposta pertence à organização
    const proposal = await prisma.proposal.findFirst({
      where: {
        id: proposalId,
        customer: {
          organizationId,
        },
      },
    });

    if (!proposal) {
      throw new Error(
        "Proposta não encontrada ou não pertence a sua organização"
      );
    }

    // Converter o totalCost para número
    const workTotalCost = parseCurrencyToNumber(totalCost.toString());

    // Atualizar o workTotalCost da proposta
    // const updatedProposal = await prisma.proposal.update({
    //   where: { id: proposalId },
    //   data: { workTotalCost },
    // });

    // Atualizar o totalCost de todos os orçamentos de reparo existentes
    // para garantir que todos tenham o mesmo valor
    try {
      await prisma.repairBudget.updateMany({
        where: { proposalId },
        data: { totalCost: workTotalCost },
      });

      console.log(
        `Atualizado totalCost de todos os orçamentos de reparo para ${workTotalCost}`
      );
    } catch (updateError) {
      console.error(
        "Erro ao atualizar totalCost dos orçamentos de reparo:",
        updateError
      );
      // Continua mesmo se falhar a atualização dos orçamentos
    }

    return { success: true, workTotalCost };
  } catch (error) {
    console.error("Erro ao atualizar workTotalCost da proposta:", error);
    return { success: false, error };
  }
}
