import { NextResponse } from "next/server";
import { loadCustomersPaginated } from "@/src/actions/customers";

export const dynamic = "force-dynamic";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const search = searchParams.get("search") || "";

    const result = await loadCustomersPaginated(page, pageSize, search);
    return NextResponse.json(result);
  } catch (error) {
    console.error("Erro ao carregar clientes:", error);
    return NextResponse.json({ error: "Erro ao carregar clientes" }, { status: 500 });
  }
}
