networks:
  app_network:
    driver: bridge # Indica que a rede foi criada manualmente

services:
  ageu:
    build: .
    container_name: ageu
    env_file:
      - .env
    environment:
      - SESSION_TOKEN=${SESSION_TOKEN}
      - NODE_ENV=development
      - TZ=America/Sao_Paulo
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - AUTH_GOOGLE_ID=${AUTH_GOOGLE_ID}
      - AUTH_GOOGLE_SECRET=${AUTH_GOOGLE_SECRET}
      - NEXT_PUBLIC_API_GOOGLE_MAPS=${NEXT_PUBLIC_API_GOOGLE_MAPS}
      - EMAIL_SERVER=${EMAIL_SERVER}
      - EMAIL_FROM=${EMAIL_FROM}
      - DATABASE_URL=${DATABASE_URL}
      - GOOGLE_APPLICATION_CREDENTIALS=${GOOGLE_APPLICATION_CREDENTIALS}
      - STORAGE_ENDPOINT=${STORAGE_ENDPOINT}
      - STORAGE_REGION=${STORAGE_REGION}
      - STORAGE_BUCKET=${STORAGE_BUCKET}
      - STORAGE_PATH=${STORAGE_PATH}
      - STORAGE_ACCESS_KEY_ID=${STORAGE_ACCESS_KEY_ID}
      - STORAGE_SECRET_ACCESS_KEY=${STORAGE_SECRET_ACCESS_KEY}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - NEXT_PUBLIC_OPENAI_API_KEY=${NEXT_PUBLIC_OPENAI_API_KEY}
      - API_ACCESS_TOKEN=${API_ACCESS_TOKEN}

    volumes:
      - ./.next_cache:/app/.next/cache
    ports:
      - "3000:3000"
    restart: unless-stopped
    networks:
      - app_network
