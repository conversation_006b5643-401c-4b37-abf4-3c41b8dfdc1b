import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";
import {
  S3Client,
  CopyObjectCommand,
  DeleteObjectCommand,
} from "@aws-sdk/client-s3";
import {
  MINIO_BUCKET_NAME,
  MINIO_ENDPOINT,
  MINIO_ROOT_USER,
  MINIO_ROOT_PASSWORD,
  STORAGE_REGION,
} from "@/src/lib/env/variables";
import { toKebabCase } from "@/src/lib/utils";
// import { auth } from "@/src/providers/auth";

const s3 = new S3Client({
  endpoint: MINIO_ENDPOINT!,
  region: STORAGE_REGION!,
  credentials: {
    accessKeyId: MINIO_ROOT_USER!,
    secretAccessKey: MINIO_ROOT_PASSWORD!,
  },
  forcePathStyle: true,
});

export async function POST() {
  try {
    // Só OWNER pode executar
    // const session = await auth();
    // if (session?.membership?.role !== "OWNER") {
    //   return NextResponse.json({ error: "Apenas administradores podem executar a migração." }, { status: 403 });
    // }

    // Buscar todas as organizações
    const orgs = await prisma.organization.findMany();
    let totalMigrated = 0;
    let totalErrors = 0;
    const logs: any[] = [];

    // Buscar todos os arquivos de file e fileEditor uma vez só para performance
    const allFiles = await prisma.file.findMany();
    const allFileEditors = await prisma.fileEditor.findMany({
      where: { bucket: MINIO_BUCKET_NAME! },
    });

    for (const org of orgs) {
      const orgSlug = toKebabCase(org.name);
      // Migrar arquivos da tabela file
      const files = allFiles.filter(
        (file) => !file.path.startsWith(`${orgSlug}/`)
      );
      const fileMigrations = files.map(async (file) => {
        try {
          const oldPath = file.path;
          const newPath = `${orgSlug}/${oldPath}`;
          await s3.send(
            new CopyObjectCommand({
              Bucket: MINIO_BUCKET_NAME!,
              CopySource: `${MINIO_BUCKET_NAME!}/${oldPath}`,
              Key: newPath,
              ACL: "public-read",
            })
          );
          await prisma.file.update({
            where: { id: file.id },
            data: { path: newPath },
          });
          await s3.send(
            new DeleteObjectCommand({
              Bucket: MINIO_BUCKET_NAME!,
              Key: oldPath,
            })
          );
          totalMigrated++;
          logs.push({
            org: org.name,
            fileId: file.id,
            oldPath,
            newPath,
            status: "ok",
          });
        } catch (err: any) {
          totalErrors++;
          logs.push({
            org: org.name,
            fileId: file.id,
            error: err?.message || String(err),
            status: "error",
          });
        }
      });
      await Promise.all(fileMigrations);

      // Migrar arquivos da tabela fileEditor
      const fileEditors = allFileEditors.filter(
        (fe) => !fe.key.startsWith(`${orgSlug}/`)
      );
      const fileEditorMigrations = fileEditors.map(async (fe) => {
        try {
          const oldKey = fe.key;
          const newKey = `${orgSlug}/${oldKey}`;
          await s3.send(
            new CopyObjectCommand({
              Bucket: MINIO_BUCKET_NAME!,
              CopySource: `${MINIO_BUCKET_NAME!}/${oldKey}`,
              Key: newKey,
              ACL: "public-read",
            })
          );
          await prisma.fileEditor.update({
            where: { id: fe.id },
            data: { key: newKey },
          });
          await s3.send(
            new DeleteObjectCommand({ Bucket: MINIO_BUCKET_NAME!, Key: oldKey })
          );
          totalMigrated++;
          logs.push({
            org: org.name,
            fileEditorId: fe.id,
            oldKey,
            newKey,
            status: "ok",
          });
        } catch (err: any) {
          totalErrors++;
          logs.push({
            org: org.name,
            fileEditorId: fe.id,
            error: err?.message || String(err),
            status: "error",
          });
        }
      });
      await Promise.all(fileEditorMigrations);

      // Migrar thumbnails (miniaturas)
      const thumbnails = allFiles.filter(
        (file) =>
          file.thumbnailPath && !file.thumbnailPath.startsWith(`${orgSlug}/`)
      );
      const thumbnailMigrations = thumbnails.map(async (file) => {
        try {
          const oldThumb = file.thumbnailPath!;
          const newThumb = `${orgSlug}/${oldThumb}`;
          await s3.send(
            new CopyObjectCommand({
              Bucket: MINIO_BUCKET_NAME!,
              CopySource: `${MINIO_BUCKET_NAME!}/${oldThumb}`,
              Key: newThumb,
              ACL: "public-read",
            })
          );
          await prisma.file.update({
            where: { id: file.id },
            data: { thumbnailPath: newThumb },
          });
          await s3.send(
            new DeleteObjectCommand({
              Bucket: MINIO_BUCKET_NAME!,
              Key: oldThumb,
            })
          );
          totalMigrated++;
          logs.push({
            org: org.name,
            fileId: file.id,
            oldThumb,
            newThumb,
            status: "ok-thumbnail",
          });
        } catch (err: any) {
          totalErrors++;
          logs.push({
            org: org.name,
            fileId: file.id,
            error: err?.message || String(err),
            status: "error-thumbnail",
          });
        }
      });
      await Promise.all(thumbnailMigrations);
    }
    return NextResponse.json({
      migrated: totalMigrated,
      errors: totalErrors,
      logs,
    });
  } catch (error: any) {
    return NextResponse.json(
      { error: error?.message || String(error) },
      { status: 500 }
    );
  }
}
