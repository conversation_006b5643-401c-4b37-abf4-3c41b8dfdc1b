"use server";
import { prisma } from "@/src/lib/prisma";
import { convertBigInt } from "@/src/lib/utils";
import { ReportTemplateInterface } from "@/src/types/core/report-template";
import { deleteFileEditorById, persistFileEditor } from "./file-editor";
import { ReportType } from "@prisma/client";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

export async function loadReportTemplates(
  page: number = 1,
  pageSize: number = 10,
  search: string = "",
  type?: string,
  excludeTypes: string[] = []
) {
  try {
    const { organizationId } = await getCurrentOrganization();
    const skip = (page - 1) * pageSize;

    const where = {
      organizationId,
      ...(search
        ? {
            OR: [
              { title: { contains: search, mode: "insensitive" as const } },
              {
                description: { contains: search, mode: "insensitive" as const },
              },
            ],
          }
        : {}),
      ...(type ? { type: type as ReportType } : {}),
      ...(excludeTypes.length > 0
        ? {
            NOT: {
              type: { in: excludeTypes as ReportType[] },
            },
          }
        : {}),
    };

    const [total, data] = await Promise.all([
      prisma.reportTemplate.count({ where }),
      prisma.reportTemplate.findMany({
        where,
        orderBy: { createdAt: "desc" },
        include: { fileEditor: true },
        skip,
        take: pageSize,
      }),
    ]);

    return convertBigInt({
      data,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    });
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function findReportTemplate(id: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const data = await prisma.reportTemplate.findUnique({
      where: {
        id,
        organizationId,
      },
      include: { fileEditor: true },
    });

    return convertBigInt(data) as ReportTemplateInterface;
  } catch (error) {
    console.error(error);
  }
}

export async function saveReportTemplate(
  formData: FormData
): Promise<ReportTemplateInterface | undefined> {
  try {
    const { organizationId } = await getCurrentOrganization();

    const file = formData.get("file") as any;
    const fileName = formData.get("fileName") as string;
    const description = formData.get("description") as string;
    const reportType = formData.get("type") as string;
    const fileKey = formData.get("fileKey") as string;
    const id = formData.get("id") as string;

    let data: any = {
      title: fileName,
      description: description,
      type: reportType,
      organizationId,
    };

    // Se temos um arquivo novo, sempre persistimos ele
    if (file && file.size > 0) {
      // Se estamos editando e temos um fileKey, atualizamos o arquivo existente
      const createdFile = await persistFileEditor(
        file,
        id ? fileKey : undefined
      );
      if (!createdFile?.id) throw Error("Failed to save file");

      data = {
        ...data,
        fileEditorId: createdFile.id,
      };
    } else if (id) {
      // Se estamos editando e não temos arquivo novo, mantemos o fileEditorId existente
      // Verificar se já temos o fileEditorId no formData
      const fileEditorId = formData.get("fileEditorId");

      if (fileEditorId) {
        data = {
          ...data,
          fileEditorId: fileEditorId.toString(),
        };
      } else {
        // Se não temos o fileEditorId no formData, buscamos do banco
        const existingTemplate = await prisma.reportTemplate.findUnique({
          where: { id },
          select: { fileEditorId: true },
        });

        if (existingTemplate) {
          data = {
            ...data,
            fileEditorId: existingTemplate.fileEditorId,
          };
        }
      }
    }

    // Remover organizationId do objeto data para update
    if (id) {
      const { organizationId, ...updateData } = data;
      const reportTemplate = await prisma.reportTemplate.update({
        where: {
          id,
          organizationId,
        },
        data: {
          ...updateData,
        },
      });
      return convertBigInt(reportTemplate) as ReportTemplateInterface;
    } else {
      const reportTemplate = await prisma.reportTemplate.create({ data });
      return convertBigInt(reportTemplate) as ReportTemplateInterface;
    }
  } catch (error) {
    console.error(error);
    throw Error("Failed to save file");
  }
}

export async function fetchReportTemplateListByType(reportType: ReportType) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const data = await prisma.reportTemplate.findMany({
      where: {
        type: reportType,
        organizationId,
      },
      orderBy: { title: "asc" },
    });

    return convertBigInt(data) as ReportTemplateInterface[];
  } catch (error) {
    console.error(error);
  }
}

export async function removeReportTemplate(id: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    // Buscar o template para obter o fileEditorId
    const template = await prisma.reportTemplate.findUnique({
      where: { id, organizationId },
      select: { fileEditorId: true },
    });

    if (!template) {
      throw new Error("Template não encontrado");
    }

    // Desvincular o template das propostas associadas
    await prisma.proposal.updateMany({
      where: { proposalTemplateId: id as any },
      data: { proposalTemplateId: null },
    });

    // Excluir o template do banco de dados
    await prisma.reportTemplate.delete({
      where: {
        id,
        organizationId,
      },
    });

    // Excluir o arquivo do bucket se existir um fileEditorId
    if (template.fileEditorId) {
      await deleteFileEditorById(template.fileEditorId);
    }

    return { message: "Template de relatório removido com sucesso!" };
  } catch (error) {
    console.error(error);
    throw new Error("Erro ao remover template de relatório");
  }
}
