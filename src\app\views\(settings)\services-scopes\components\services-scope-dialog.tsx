"use client";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { CustomInput } from "@/src/components/app-input";
import { But<PERSON> } from "@/src/components/ui/button";
import { toast } from "@/src/hooks/use-toast";
import { ServicesScope } from "@/src/types/core/services-scope";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import {
  servicesScopeSchema,
  ServicesScopeSchema,
} from "../schemas/services-scope.schema";
import { saveServiceScope } from "@/src/actions/services-scopes";

type ServicesScopeDialogProps = {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  service?: ServicesScope | null;
  currentPage?: number;
};

export default function ServicesScopeDialog({
  isOpen,
  onOpenChange,
  service,
  currentPage = 1,
}: ServicesScopeDialogProps) {
  const methods = useForm<ServicesScopeSchema>({
    resolver: zodResolver(servicesScopeSchema),
    defaultValues: {
      name: "",
      description: "",
      types: ["PROPOSAL_SERVICE"],
    },
  });

  const { reset } = methods;

  useEffect(() => {
    if (service) {
      reset({
        id: service.id,  // Importante: incluir o ID ao editar
        name: service.name,
        description: service.description,
        types: service.types,
      });
    } else {
      reset({
        name: "",
        description: "",
        types: ["PROPOSAL_SERVICE"],
      });
    }
  }, [isOpen, service, reset]);

  const onSubmit = methods.handleSubmit(async (data) => {
    try {
      // Usar server action em vez de fetch
      const result = await saveServiceScope(data);

      if (result?.error) {
        toast({
          title: "Erro",
          description: result.message || "Erro ao salvar escopo de serviço",
          variant: "destructive"
        });
        return;
      }

      toast({
        title: "Sucesso",
        description: service?.id
          ? "Escopo de serviço atualizado com sucesso!"
          : "Escopo de serviço criado com sucesso!",
        variant: "default"
      });

      onOpenChange(false);

      // Atualizar a tabela na página atual após salvar
      if (window && window.location) {
        const event = new CustomEvent('refreshServiceScopes', { detail: { page: currentPage } });
        window.dispatchEvent(event);
      }
    } catch (error) {
      console.error(error);
      toast({
        title: "Erro",
        description: "Erro ao salvar escopo de serviço",
        variant: "destructive"
      });
    }
  });

  useEffect(() => {
    if (!isOpen) {
      reset();
    }
  }, [isOpen, reset]);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="rounded-lg" onOpenAutoFocus={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>{service ? "Editar" : "Adicionar"} serviço</DialogTitle>
          <DialogDescription>
            Preencha as informações abaixo para
            {service ? " editar" : " adicionar"} o serviço.
          </DialogDescription>
        </DialogHeader>

        <Form {...methods}>
          <form className="flex flex-col gap-6 mt-4" onSubmit={onSubmit}>
            <CustomInput
              label="Nome do serviço"
              name="name"
              placeholder="Nome"
              autoFocus={false}
            />

            <CustomInput
              label="Descrição do serviço"
              name="description"
              placeholder="Descrição"
            />

            <CustomInput
              name="types"
              label="Tipo de serviço"
              type="checkbox-group"
              hideErrorMessage={true}
              items={[
                { label: "Serviço de proposta ", value: "PROPOSAL_SERVICE" },
                { label: "Serviço de reparo", value: "REPAIR_SERVICE" },
              ]}
            />

            <DialogFooter className="flex gap-4 mt-6">
              <Button
                type="button"
                variant="outline"
                className="border-blue-500 text-blue-500 hover:text-blue-400 hover:border-blue-400"
                onClick={() => onOpenChange(false)}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                variant="success"
                disabled={methods.formState.isSubmitting}
              >
                {service ? "Atualizar" : "Salvar"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
