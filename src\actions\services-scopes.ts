"use server";
import { prisma } from "@/src/lib/prisma";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";
import { ServicesScopeSchema } from "@/src/app/views/(settings)/services-scopes/schemas/services-scope.schema";
import { ServiceType } from "../types/common";
import { Prisma } from "@prisma/client";

export async function loadServicesScope(
  types: ServiceType[],
  page: number = 1,
  pageSize: number = 10,
  search?: string
) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const where = {
      organizationId,
      types: {
        hasSome: types,
      },
      ...(search && {
        OR: [
          { name: { contains: search, mode: "insensitive" as const } },
          { description: { contains: search, mode: "insensitive" as const } },
        ],
      }),
    } satisfies Prisma.ServiceScopeWhereInput;

    const [total, items] = await Promise.all([
      prisma.serviceScope.count({ where }),
      prisma.serviceScope.findMany({
        where,
        orderBy: [
          { createdAt: "desc" }, // Mantém ordenação consistente por data de criação
        ],
        skip: (page - 1) * pageSize,
        take: pageSize,
      }),
    ]);

    return {
      data: items,
      page,
      pageSize,
      total,
      totalPages: Math.ceil(total / pageSize),
    };
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function saveServiceScope(serviceScope: ServicesScopeSchema) {
  try {
    const { organizationId } = await getCurrentOrganization();

    if (serviceScope.id) {
      // Update existing service scope
      const updatedService = await prisma.serviceScope.update({
        where: {
          id: serviceScope.id,
          organizationId,
        },
        data: {
          name: serviceScope.name,
          description: serviceScope.description,
          types: serviceScope.types,
          updatedAt: new Date(),
        },
      });
      return {
        error: false,
        data: updatedService,
        message: "Escopo de serviço atualizado com sucesso!",
      };
    } else {
      // Create new service scope
      const newService = await prisma.serviceScope.create({
        data: {
          name: serviceScope.name,
          description: serviceScope.description,
          types: serviceScope.types,
          organizationId,
        },
      });
      return {
        error: false,
        data: newService,
        message: "Escopo de serviço criado com sucesso!",
      };
    }
  } catch (error) {
    console.error(error);
    return {
      error: true,
      message: serviceScope.id
        ? "Erro ao atualizar escopo de serviço"
        : "Erro ao criar escopo de serviço",
    };
  }
}

export async function removeServiceScope(id: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const proposals = await prisma.proposal.findMany({
      where: {
        serviceScopes: {
          some: {
            id: id,
            organizationId,
          },
        },
      },
    });

    if (proposals.length > 0) {
      return {
        error: true,
        title: "Erro ao remover escopo de serviço",
        message: "Existem propostas vinculadas ao escopo de serviço",
      };
    }

    await prisma.repairBudget.deleteMany({
      where: {
        serviceScopeId: id,
        serviceScope: {
          organizationId,
        },
      },
    });

    await prisma.productivity.deleteMany({
      where: {
        serviceId: id,
        service: {
          organizationId,
        },
      },
    });

    await prisma.serviceScope.delete({
      where: {
        id: id,
        organizationId,
      },
    });

    return {
      error: false,
      message: "Serviço excluído com sucesso!",
    };
  } catch (error) {
    console.error(error);
    return {
      error: true,
      title: "Erro ao remover escopo de serviço",
      message: "Ocorreu um erro ao tentar remover o escopo de serviço",
    };
  }
}
