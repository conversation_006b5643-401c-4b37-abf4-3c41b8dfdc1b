// Função para formatar data por extenso
function formatDate(date, format = "DISPLAY") {
  const parsedDate = new Date(date);
  
  // Verificar se a data é válida
  if (isNaN(parsedDate.getTime())) {
    return "Data inválida";
  }
  
  if (format === "LONG") {
    // Formato por extenso: 22 de novembro de 2023
    const day = parsedDate.getDate();
    const month = parsedDate.toLocaleString("pt-BR", { month: "long" });
    const year = parsedDate.getFullYear();
    return `${day} de ${month} de ${year}`;
  }
  
  // Formato padrão: DD/MM/YYYY
  return parsedDate.toLocaleDateString("pt-BR", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });
}

// Simular a função generateProposalTemplateVariables
function generateProposalTemplateVariables(proposal) {
  return {
    // Outras informações
    data_hoje: formatDate(Date.now()),
    data_hoje_extenso: formatDate(Date.now(), "LONG"),
  };
}

// Testar a função
const mockProposal = {
  // Dados fictícios de uma proposta
};

const variables = generateProposalTemplateVariables(mockProposal);
console.log('Data normal:', variables.data_hoje);
console.log('Data por extenso:', variables.data_hoje_extenso);
