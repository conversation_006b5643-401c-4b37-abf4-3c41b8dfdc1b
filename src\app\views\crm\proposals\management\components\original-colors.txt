// Cores originais das colunas
const getColumnColor = () => {
  switch(situation.value) {
    case 'NEW': return 'from-blue-50 to-blue-100 border-blue-200';
    case 'UNDER_ANALYSIS': return 'from-purple-50 to-purple-100 border-purple-200';
    case 'PROPOSAL_SENT': return 'from-indigo-50 to-indigo-100 border-indigo-200';
    case 'PROPOSAL_ACCEPTED': return 'from-green-50 to-green-100 border-green-200';
    case 'SIGN_REQUESTED': return 'from-orange-50 to-orange-100 border-orange-200';
    case 'SIGNED': return 'from-emerald-50 to-emerald-100 border-emerald-200';
    case 'PROJECT_IN_PROGRESS': return 'from-amber-50 to-amber-100 border-amber-200';
    case 'PROJECT_FINISHED': return 'from-teal-50 to-teal-100 border-teal-200';
    case 'LOST': return 'from-red-50 to-red-100 border-red-200';
    default: return 'from-gray-50 to-gray-100 border-gray-200';
  }
};

// Cores originais dos cabeçalhos
const getHeaderColor = () => {
  switch(situation.value) {
    case 'NEW': return 'bg-blue-800 text-white';
    case 'UNDER_ANALYSIS': return 'bg-purple-800 text-white';
    case 'PROPOSAL_SENT': return 'bg-indigo-800 text-white';
    case 'PROPOSAL_ACCEPTED': return 'bg-green-800 text-white';
    case 'SIGN_REQUESTED': return 'bg-orange-800 text-white';
    case 'SIGNED': return 'bg-emerald-800 text-white';
    case 'PROJECT_IN_PROGRESS': return 'bg-amber-800 text-white';
    case 'PROJECT_FINISHED': return 'bg-teal-800 text-white';
    case 'LOST': return 'bg-red-800 text-white';
    default: return 'bg-gray-800 text-white';
  }
};
