"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { Button } from "@/src/components/ui/button";
import { Download, Loader2 } from "lucide-react";
import { toast } from "@/src/hooks/use-toast";
import Image from "next/image";

interface ReportData {
  url: string;
  fileName: string;
  contentType: string;
}

export default function PublicReportPage() {
  const params = useParams();
  const token = params.token as string;

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [reportData, setReportData] = useState<ReportData | null>(null);

  // Efeito para limpar as URLs do Blob quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (reportData?.url) {
        URL.revokeObjectURL(reportData.url);
      }
    };
  }, [reportData?.url]);

  useEffect(() => {
    const fetchReport = async () => {
      if (!token) {
        setError("Token de relatório inválido");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Fazer a requisição para gerar o relatório usando o token
        const response = await fetch(`/api/public/report/${token}`);

        if (!response.ok) {
          const errorData = await response.json();
          console.error("Erro na resposta da API:", {
            status: response.status,
            statusText: response.statusText,
            error: errorData.error
          });
          throw new Error(errorData.error || "Erro ao gerar o relatório");
        }

        const data = await response.json();

        if (data.success && data.pdfBase64 && data.fileName && data.contentType) {
          console.log("Dados do PDF recebidos com sucesso");
          try {
            // Converter o base64 para um Blob
            const byteCharacters = atob(data.pdfBase64);
            console.log("Base64 decodificado, tamanho:", byteCharacters.length);

            const byteNumbers = new Array(byteCharacters.length);

            for (let i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i);
            }

            const byteArray = new Uint8Array(byteNumbers);
            console.log("Array de bytes criado, tamanho:", byteArray.length);

            const blob = new Blob([byteArray], { type: data.contentType });
            console.log("Blob criado, tamanho:", blob.size);

            // Criar uma URL para o Blob
            const blobUrl = URL.createObjectURL(blob);
            console.log("URL do blob criada:", blobUrl);

            setReportData({
              url: blobUrl,
              fileName: data.fileName,
              contentType: data.contentType
            });

            toast({
              title: "Relatório carregado",
              description: "O relatório foi carregado com sucesso.",
              duration: 3000
            });
          } catch (error) {
            console.error("Erro ao processar base64:", error);
            setError("Erro ao processar o PDF: " + (error instanceof Error ? error.message : "Erro desconhecido"));
          }
        } else {
          throw new Error("Dados do relatório incompletos");
        }
      } catch (error) {
        console.error("Erro ao buscar relatório:", error);
        setError(error instanceof Error ? error.message : "Erro desconhecido ao gerar o relatório");
      } finally {
        setLoading(false);
      }
    };

    fetchReport();
  }, [token]);

  const handleDownload = () => {
    if (reportData?.url) {
      // Criar um link temporário para download
      const link = document.createElement("a");
      link.href = reportData.url;
      link.download = reportData.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
        <div className="w-full max-w-4xl p-8 bg-white rounded-lg shadow-md">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="relative h-16 w-40 mb-4">
              <Image
                src="https://i.imgur.com/n5c1eTk.png"
                alt="Ageu Engenharia"
                fill
                style={{ objectFit: 'contain' }}
                priority
                unoptimized
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const parent = target.parentElement;
                  if (parent) {
                    if (parent.contains(target)) {
                      parent.removeChild(target);
                    }
                    parent.innerHTML = '<div class="flex items-center justify-center bg-blue-50 h-full w-full rounded"><span class="font-bold text-blue-700 text-sm">AGEU</span></div>';
                  }
                }}
              />
            </div>
            <Loader2 className="h-12 w-12 animate-spin text-blue-500" />
            <h1 className="text-xl font-semibold text-gray-700">Carregando relatório...</h1>
            <p className="text-gray-500">Aguarde enquanto geramos seu relatório.</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
        <div className="w-full max-w-4xl p-8 bg-white rounded-lg shadow-md">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="relative h-16 w-40 mb-4">
              <Image
                src="https://i.imgur.com/n5c1eTk.png"
                alt="Ageu Engenharia"
                fill
                style={{ objectFit: 'contain' }}
                priority
                unoptimized
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const parent = target.parentElement;
                  if (parent) {
                    if (parent.contains(target)) {
                      parent.removeChild(target);
                    }
                    parent.innerHTML = '<div class="flex items-center justify-center bg-blue-50 h-full w-full rounded"><span class="font-bold text-blue-700 text-sm">AGEU</span></div>';
                  }
                }}
              />
            </div>
            <h1 className="text-xl font-semibold text-red-600">Erro ao carregar relatório</h1>
            <p className="text-gray-500">{error}</p>
            <p className="text-gray-400 text-sm">Verifique se o link está correto, se o relatório ainda existe ou tente gerar um novo link.</p>
            <Button variant="outline" onClick={() => window.location.reload()}>Tentar novamente</Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center min-h-screen bg-gray-50 p-4">
      <div className="w-full max-w-6xl bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-4 border-b flex justify-between items-center">
          <div className="flex items-center gap-3">
            <div className="relative h-10 w-24">
              <Image
                src="https://i.imgur.com/n5c1eTk.png"
                alt="Ageu Engenharia"
                fill
                style={{ objectFit: 'contain' }}
                priority
                unoptimized
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const parent = target.parentElement;
                  if (parent) {
                    if (parent.contains(target)) {
                      parent.removeChild(target);
                    }
                    parent.innerHTML = '<div class="flex items-center justify-center bg-blue-50 h-full w-full rounded"><span class="font-bold text-blue-700 text-sm">AGEU</span></div>';
                  }
                }}
              />
            </div>
            <h1 className="text-xl font-semibold text-gray-700">Relatório de Fiscalização</h1>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
            className="flex items-center gap-1"
          >
            <Download className="h-4 w-4" />
            Salvar PDF
          </Button>
        </div>

        <div className="h-[80vh]">
          {reportData?.url ? (
            <iframe
              src={reportData.url}
              className="w-full h-full"
              title="Relatório de Fiscalização"
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500">Não foi possível carregar o relatório.</p>
            </div>
          )}
        </div>
      </div>

      <div className="mt-4 text-center text-sm text-gray-500">
        <p>© {new Date().getFullYear()} Ageu Engenharia. Todos os direitos reservados.</p>
      </div>
    </div>
  );
}
