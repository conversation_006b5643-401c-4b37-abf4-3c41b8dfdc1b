"use client";

import { useState, useEffect } from "react";
import { Combobox, ComboboxOption } from "@/components/ui/combobox";
import { useToast } from "@/src/hooks/use-toast";

interface CustomerSearchProps {
  onCustomerChange: (customerId: string) => void;
  className?: string;
}

export function CustomerSearch({ onCustomerChange, className = "" }: CustomerSearchProps) {
  const { toast } = useToast();
  const [customerOptions, setCustomerOptions] = useState<ComboboxOption[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<string>("");
  // const [loading, setLoading] = useState(false);

  // Função para carregar os clientes iniciais
  const fetchInitialCustomers = async () => {
    try {
      // setLoading(true);
      const response = await fetch("/api/customers/search");
      if (!response.ok) {
        throw new Error("Falha ao carregar clientes");
      }
      const data = await response.json();
      // Garantir que o resultado seja sempre um array
      if (!Array.isArray(data)) {
        console.warn("API retornou dados não esperados:", data);
        setCustomerOptions([{ label: "Todos os clientes", value: "" }]);
      } else {
        // Adicionar a opção "Todos os clientes" no início da lista
        setCustomerOptions([{ label: "Todos os clientes", value: "" }, ...data]);
      }
    } catch (error) {
      console.error("Erro ao carregar clientes:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar a lista de clientes",
        variant: "destructive",
      });
      setCustomerOptions([{ label: "Todos os clientes", value: "" }]);
    }
  };

  // Função para buscar clientes com base no termo de pesquisa
  const searchCustomers = async (search: string): Promise<ComboboxOption[]> => {
    try {
      // Se a pesquisa estiver vazia ou for "todos", retornar a opção "Todos os clientes"
      if (!search.trim() || search.toLowerCase() === "todos") {
        return [{ label: "Todos os clientes", value: "" }];
      }

      const response = await fetch(`/api/customers/search?search=${encodeURIComponent(search)}`);
      if (!response.ok) {
        throw new Error("Falha ao buscar clientes");
      }
      const data = await response.json();
      // Garantir que o resultado seja sempre um array
      if (!Array.isArray(data)) {
        console.warn("API retornou dados não esperados:", data);
        return [{ label: "Todos os clientes", value: "" }];
      }

      // Sempre incluir a opção "Todos os clientes" no início dos resultados
      return [{ label: "Todos os clientes", value: "" }, ...data];
    } catch (error) {
      console.error("Erro ao buscar clientes:", error);
      toast({
        title: "Erro",
        description: "Não foi possível buscar clientes",
        variant: "destructive",
      });
      return [{ label: "Todos os clientes", value: "" }];
    }
  };

  // Carregar clientes ao montar o componente
  useEffect(() => {
    fetchInitialCustomers();
  }, []);

  // Notificar o componente pai quando o cliente selecionado mudar
  useEffect(() => {
    onCustomerChange(selectedCustomer);
  }, [selectedCustomer, onCustomerChange]);

  return (
    <div className={className}>
      <Combobox
        options={customerOptions}
        value={selectedCustomer}
        onChange={setSelectedCustomer}
        placeholder="Selecione um cliente"
        searchPlaceholder="Digite o nome do cliente..."
        emptyMessage="Nenhum cliente encontrado"
        onSearch={searchCustomers}
        className="w-full text-green-700 font-medium"
        customStyles={{
          trigger: "border-green-100 bg-green-50 hover:bg-green-100 hover:border-green-200 text-green-700",
          search: "border-green-100 focus:border-green-300 focus:ring-green-200",
          searchIcon: "text-green-500",
          option: "hover:bg-green-50 hover:text-green-700 focus:bg-green-50 focus:text-green-700",
          selectedIcon: "text-green-600",
          content: "border-green-100",
          emptyMessage: "text-green-600"
        }}
      />
    </div>
  );
}
