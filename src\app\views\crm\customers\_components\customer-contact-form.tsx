import { CustomInput } from "@/src/components/app-input";
import { Button } from "@/src/components/ui/button";
import { FormProvider, UseFormReturn } from "react-hook-form";
import { ContactSchema } from "../_schemas/contact.schema";

interface CustomerContactFormProps {
	methods: UseFormReturn<ContactSchema>;
	onSubmit: () => void;
}

export default function CustomerContactForm({
	methods,
	onSubmit,
}: CustomerContactFormProps) {
	return (
		<FormProvider {...methods}>
			<form className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 ">
				<CustomInput label="Nome" name="name" placeholder="Nome" />

				<CustomInput
					label="Meio de contato"
					name="type"
					placeholder="Meio de contato"
					type="select"
					items={[
						{ value: "EMAIL", label: "Email" },
						{ value: "PHONE", label: "Telefone" },
					]}
				/>

				<CustomInput
					type="date"
					label="Data"
					name="date"
					placeholder="Data"
					hideErrorMessage={true}
				/>

				<Button
					className="bg-green-500 hover:bg-green-600 h-[40px] self-end font-bold"
					type="button"
					onClick={() => onSubmit()}
				>
					Adicionar
				</Button>
			</form>
		</FormProvider>
	);
}
