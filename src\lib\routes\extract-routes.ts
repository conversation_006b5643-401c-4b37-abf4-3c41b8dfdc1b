import { routeData, RouteDataInterface } from "./routes";

export interface ExtractedRoute {
  route: string;
  title: string;
}

function extractRoutesFromData(data: RouteDataInterface[], acc: ExtractedRoute[] = []): ExtractedRoute[] {
  for (const item of data) {
    if (item.url && item.url !== "") {
      acc.push({ route: item.url, title: item.title });
    }
    if (item.items && item.items.length > 0) {
      extractRoutesFromData(item.items, acc);
    }
  }
  return acc;
}

export function getAllRoutesWithTitles(): ExtractedRoute[] {
  return extractRoutesFromData(routeData);
} 