import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";
import { auth } from "@/src/providers/auth";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

export const dynamic = "force-dynamic";

export async function GET(request: Request) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get("search") || "";

    console.log("Buscando clientes com termo:", search);

    const { organizationId } = await getCurrentOrganization();

    const customers = await prisma.customer.findMany({
      where: {
        organizationId,
        OR: [
          { name: { contains: search, mode: "insensitive" } },
          { document: { contains: search, mode: "insensitive" } },
        ],
      },
      orderBy: {
        name: "asc",
      },
    });

    console.log("Clientes encontrados:", customers.length);

    // Formatar os resultados para o formato esperado pelo combobox
    const formattedResults = customers.map((customer) => ({
      label: customer.name,
      value: customer.id,
    }));

    return NextResponse.json(formattedResults);
  } catch (error) {
    console.error("Erro ao buscar clientes:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
