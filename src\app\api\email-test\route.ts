import { NextResponse } from "next/server";
import { auth } from "@/src/providers/auth";
import nodemailer from "nodemailer";

export const dynamic = "force-dynamic";
export const revalidate = 0;

interface SMTPError extends Error {
  code?: string;
  command?: string;
  response?: string;
}

// Função para verificar se o usuário está autenticado
async function isAuthenticated() {
  const session = await auth();
  return !!session?.user;
}

// Configuração do transporte de email
const createTransporter = () => {
  const host = process.env.EMAIL_HOST;
  const port = parseInt(process.env.EMAIL_PORT || "587", 10);
  const user = process.env.EMAIL_USER;
  const pass = process.env.EMAIL_PASS;
  const from = process.env.EMAIL_FROM;

  if (!host || !user || !pass || !from) {
    throw new Error("Configurações de email incompletas no arquivo .env");
  }

  return nodemailer.createTransport({
    host,
    port,
    secure: port === 465, // true para 465, false para outras portas
    auth: {
      user,
      pass,
    },
  });
};

export async function GET() {
  console.log("=== INICIANDO TESTE DE CONFIGURAÇÃO DE EMAIL ===");
  try {
    // Verificar autenticação
    console.log("Verificando autenticação...");
    const authenticated = await isAuthenticated();
    if (!authenticated) {
      console.log("Erro: Usuário não autenticado");
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }
    console.log("Autenticação verificada com sucesso");

    // Verificar configurações de email
    console.log("Verificando configurações de email...");
    const host = process.env.EMAIL_HOST;
    const port = process.env.EMAIL_PORT;
    const user = process.env.EMAIL_USER;
    // Verificar se a senha existe, mas não exibir por segurança
    const hasPassword = !!process.env.EMAIL_PASS;
    const from = process.env.EMAIL_FROM;

    if (!host || !user || !hasPassword || !from) {
      console.log("Erro: Configurações de email incompletas", {
        host,
        port,
        user,
        hasPassword,
        from,
      });
      return NextResponse.json(
        {
          error: "Configurações de email incompletas",
          config: { host, port, user, hasPassword, from },
        },
        { status: 400 }
      );
    }

    console.log("Configurações de email encontradas:", {
      host,
      port,
      user,
      hasPassword,
      from,
    });

    // Criar transporter
    console.log("Criando transporter para teste...");
    const transporter = createTransporter();

    // Verificar conexão com o servidor SMTP
    console.log("Verificando conexão com o servidor SMTP...");
    try {
      const verifyResult = await transporter.verify();
      console.log(
        "Conexão com servidor SMTP verificada com sucesso:",
        verifyResult
      );

      return NextResponse.json({
        success: true,
        message: "Configurações de email verificadas com sucesso",
        config: { host, port, user, hasPassword, from },
      });
    } catch (error: unknown) {
      const smtpError = error as SMTPError;
      console.error("Erro ao verificar conexão com servidor SMTP:", smtpError);
      console.error("Detalhes do erro SMTP:", {
        name: smtpError.name,
        message: smtpError.message,
        code: smtpError.code,
        command: smtpError.command,
        response: smtpError.response,
      });

      return NextResponse.json(
        {
          error: "Falha na conexão com o servidor SMTP",
          details: {
            message: smtpError.message,
            code: smtpError.code,
            response: smtpError.response,
          },
          config: { host, port, user, hasPassword, from },
        },
        { status: 500 }
      );
    }
  } catch (error: unknown) {
    console.error("=== ERRO GERAL NO TESTE DE CONFIGURAÇÃO DE EMAIL ===");
    console.error("Erro:", error);
    console.error("Detalhes do erro:", {
      name: error instanceof Error ? error.name : "UnknownError",
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : "No stack trace available",
    });

    return NextResponse.json(
      {
        error: "Erro ao testar configurações de email",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  } finally {
    console.log("=== FIM DO TESTE DE CONFIGURAÇÃO DE EMAIL ===");
  }
}
