import { z } from "zod";

export const inspectionParameterSchema = z.object({
  id: z.string().optional(),
  proposalName: z.string().optional(),
  customerName: z.string().optional(),
  proposalId: z.string().optional(),
  technicalData: z
    .string()
    .refine((value) => value.trim().length, "Preencha esse campo"),
  observation: z
    .string()
    .refine((value) => value.trim().length, "Preencha esse campo"),
  inspectionDate: z
    .date()
    .or(z.string())
    .refine((value) => value, "Data de inspeção é obrigatória"),
  numberInspection: z.number().optional(),
  laborEquipament: z.array(z.any()).optional(),
});

export type InspectionParameterSchema = z.infer<
  typeof inspectionParameterSchema
>;
