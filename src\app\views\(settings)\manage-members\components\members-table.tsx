"use client";

import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from "react";
import { TableGrid } from "@/src/components/ui/table-grid";
import { useToast } from "@/src/hooks/use-toast";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Eraser, RefreshCw } from "lucide-react";

interface Member {
  id: string;
  enabled: boolean;
  createdAt: Date;
  user: {
    name: string;
    email: string;
  };
}

interface MembersTableProps {
  columns: any[];
  onRefreshClick: () => void;
  onPageChange?: (page: number) => void;
  organizationId?: string;
}

export type MembersTableRef = {
  refresh: (page?: number) => void;
};

const MembersTable = forwardRef<MembersTableRef, MembersTableProps>(
  function MembersTable({ columns, onRefreshClick, onPageChange, organizationId }, ref) {
    const [data, setData] = useState<Member[]>([]);
    const [loading, setLoading] = useState(false);
    const [search, setSearch] = useState("");
    const searchInputRef = useRef<HTMLInputElement>(null);
    const { toast } = useToast();
    const [pagination, setPagination] = useState({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });

    const fetchMembers = useCallback(async (
      page?: number,
      pageSize: number = pagination.pageSize,
      searchTerm: string = search
    ) => {
      if (!organizationId) return;

      const currentPage = page || pagination.page;

      try {
        setLoading(true);
        const params = new URLSearchParams({
          organizationId,
          page: String(currentPage),
          pageSize: String(pageSize)
        });

        if (searchTerm) {
          params.append('search', searchTerm);
        }

        const response = await fetch(`/api/members?${params}`);

        if (!response.ok) throw new Error("Failed to fetch members");

        const result = await response.json();

        // Garantir que os dados tenham a estrutura correta
        const formattedData = Array.isArray(result.data) ? result.data.map(member => ({
          ...member,
          user: member.user || { name: '-', email: '-' }
        })) : [];

        setData(formattedData);
        setPagination(prev => ({
          ...prev,
          page: Number(result.page),
          pageSize: Number(result.pageSize),
          total: Number(result.total),
          totalPages: Number(result.totalPages)
        }));

      } catch (error) {
        console.error('Fetch error:', error);
        toast({
          title: "Erro",
          description: "Erro ao carregar membros",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }, [pagination.pageSize, search, toast, organizationId]);

    useImperativeHandle(ref, () => ({
      refresh: (page?: number) => fetchMembers(page)
    }), [fetchMembers]);

    // Carregar dados quando o componente for montado
    useEffect(() => {
      if (organizationId) {
        fetchMembers(1);
      }
    }, [fetchMembers, organizationId]);

    const handleSearch = (value: string) => {
      setSearch(value);
      fetchMembers(1, pagination.pageSize, value);
    };

    const handleClearFilters = () => {
      // Limpar o estado de pesquisa
      setSearch("");

      // Resetar para a primeira página e buscar dados
      fetchMembers(1, pagination.pageSize, "");

      // Atualizar a página atual no componente pai
      if (onPageChange) onPageChange(1);

      // Focar no campo de pesquisa após limpar
      setTimeout(() => {
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }, 0);
    };

    return (
      <TableGrid
        columns={columns}
        data={data}
        loading={loading}
        pagination={pagination}
        onPaginationChange={(newPage, newPageSize) => {
          fetchMembers(newPage, newPageSize);
          if (onPageChange) onPageChange(newPage);
        }}
        onSearch={handleSearch}
        searchValue={search}
        searchInputRef={searchInputRef}
        buttonsTemplate={
          <>
            <Button
              className="w-full sm:w-auto bg-blue-500 hover:bg-blue-400"
              onClick={handleClearFilters}
            >
              Limpar filtros <Eraser className="ml-2 h-4 w-4" />
            </Button>
            <Button
              className="w-full sm:w-auto bg-green-500 hover:bg-green-400"
              onClick={() => {
                onRefreshClick();
                fetchMembers(pagination.page);
              }}
            >
              Atualizar lista <RefreshCw className="ml-2 h-4 w-4" />
            </Button>
          </>
        }
      />
    );
  }
);

export default MembersTable;
