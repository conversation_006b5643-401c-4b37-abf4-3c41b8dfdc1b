import { ResourceControlInterface } from "../utils";
import { FileRelationInterface } from "./file";
import { ProposalRelationInterface } from "./proposal";

export type Photo = {
  description: string;
  order?: number;
  tempId?: string; // ID temporário para rastreamento durante o upload
} & ResourceControlInterface &
  FileRelationInterface;

export type LaborEquipament = {
  laborId: string;
  amount?: number;
  labor: {
    id: string;
    name: string;
    description?: string;
    type: string;
  };
};

export type Video = {
  description: string;
  order?: number;
  tempId?: string;
} & ResourceControlInterface & FileRelationInterface;

export interface InspectionParameterInterface {
  technicalData: string;
  observation: string;
  inspectionDate: string | Date;
  numberInspection?: number;

  photos?: Photo[];
  videos?: Video[];
  laborEquipament?: LaborEquipament[];
}

export type InspectionParameter = InspectionParameterInterface &
  ResourceControlInterface &
  ProposalRelationInterface;
