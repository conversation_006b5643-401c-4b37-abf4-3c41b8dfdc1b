import { useState, useEffect } from "react";
import { Button } from "@/src/components/ui/button";
import { X, Video as VideoIcon, ChevronUp, ChevronDown, GripVertical } from "lucide-react";
import { Label } from "@/src/components/ui/label";
import { Input } from "@/src/components/ui/input";
import { DragDropContext, Droppable, Draggable, DropResult } from "@hello-pangea/dnd";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/src/components/ui/dialog";
import { useRef } from "react";
import { useCenterToast } from "@/src/hooks/use-center-toast";

interface FileWithMetadata {
  file?: File; // Agora pode ser opcional
  url?: string; // Novo: para vídeos já existentes
  description: string;
  id: string;
  thumbnail?: Blob;
  thumbnailUrl?: string; // Novo: para vídeos já existentes
  uploading?: boolean;
  progress?: number;
}

interface ExistingVideo {
  id: string;
  url: string;
  description: string;
  thumbnailUrl?: string;
}

interface MultipleVideoUploadFormProps {
  onSave: (selectedFiles: { fileId?: string; description: string; thumbnailPath?: string; url?: string }[]) => void;
  onCancel: () => void;
  initialFiles?: ExistingVideo[]; // Novo: vídeos já existentes
  maxVideos?: number; // Novo: máximo de vídeos permitidos
}

// Removido MAX_VIDEOS fixo, agora usa maxVideos da prop
const MAX_SIZE_MB = 50;

// Novo componente para gravação de vídeo
function VideoRecorder({ onSave, onCancel, fullScreenCamera }: { onSave: (file: File) => void, onCancel: () => void, fullScreenCamera?: boolean }) {
  const [recording, setRecording] = useState(false);
  const [paused, setPaused] = useState(false);
  const [mediaStream, setMediaStream] = useState<MediaStream | null>(null);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [blobForSave, setBlobForSave] = useState<Blob | null>(null);
  const [time, setTime] = useState(0);
  const [timeLimitReached, setTimeLimitReached] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const chunksRef = useRef<Blob[]>([]);
  // Estado para controlar a câmera (frontal/traseira)
  const [facingMode, setFacingMode] = useState<"user" | "environment">("environment");
  const toast = useCenterToast();

  // Novo: Solicita o stream ao abrir ou ao trocar a câmera
  useEffect(() => {
    let active = true;
    async function getStream() {
      if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop());
      }
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: { facingMode }, audio: true });
        if (active) setMediaStream(stream);
      } catch (err) {
        toast.error('Não foi possível acessar a câmera.');
        console.error(err)
        onCancel();
      }
    }
    getStream();
    return () => {
      active = false;
      if (mediaStream) mediaStream.getTracks().forEach(track => track.stop());
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [facingMode]);

  useEffect(() => {
    if (mediaStream && videoRef.current && !previewUrl) {
      videoRef.current.srcObject = mediaStream;
    }
  }, [mediaStream, previewUrl]);

  useEffect(() => {
    if (recording && !paused) {
      timerRef.current = setInterval(() => {
        setTime((prev) => {
          if (prev >= 29) {
            stopRecording();
            setTimeLimitReached(true);
            return 30;
          }
          return prev + 1;
        });
      }, 1000);
    } else if (!recording || paused) {
      if (timerRef.current) clearInterval(timerRef.current);
    }
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, [recording, paused]);

  const startRecording = async () => {
    if (!mediaStream) return;
    setTime(0);
    setTimeLimitReached(false);
    const recorder = new MediaRecorder(mediaStream);
    setMediaRecorder(recorder);
    chunksRef.current = [];
    recorder.ondataavailable = (e) => {
      if (e.data.size > 0) chunksRef.current.push(e.data);
    };
    recorder.onstop = () => {
      const blob = new Blob(chunksRef.current, { type: 'video/webm' });
      setBlobForSave(blob);
      const url = URL.createObjectURL(blob);
      setPreviewUrl(url);
      if (mediaStream) mediaStream.getTracks().forEach(track => track.stop());
      setMediaStream(null);
    };
    recorder.onpause = () => setPaused(true);
    recorder.onresume = () => setPaused(false);
    recorder.start();
    setRecording(true);
    setPaused(false);
  };

  const stopRecording = () => {
    if (mediaRecorder && recording) {
      mediaRecorder.stop();
      setRecording(false);
      setPaused(false);
      if (timerRef.current) clearInterval(timerRef.current);
    }
  };

  const pauseRecording = () => {
    if (mediaRecorder && recording && !paused) {
      mediaRecorder.pause();
    }
  };

  const resumeRecording = () => {
    if (mediaRecorder && recording && paused) {
      mediaRecorder.resume();
    }
  };

  const handleSave = () => {
    if (blobForSave) {
      const file = new File([blobForSave], `video-${Date.now()}.webm`, { type: 'video/webm' });
      onSave(file);
      if (previewUrl) URL.revokeObjectURL(previewUrl);
    }
  };

  useEffect(() => {
    return () => {
      if (mediaStream) mediaStream.getTracks().forEach(track => track.stop());
      if (previewUrl) URL.revokeObjectURL(previewUrl);
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, [mediaStream, previewUrl]);

  // Novo: botão de virar câmera só funciona se não estiver gravando
  return (
    <div className="flex flex-col gap-4 items-center w-full">
      {/* Preview da câmera e gravação SEMPRE visível */}
      {!previewUrl && (
        <div className="flex flex-col gap-2 items-center w-full">
          <div className={`${fullScreenCamera ? "w-full max-w-[100vw] h-full max-h-[90vh]" : "w-full max-w-[360px] h-full max-h-[480px]"} bg-black rounded overflow-hidden mx-auto`}>
            <video ref={videoRef} autoPlay muted playsInline className="w-full h-full object-cover" />
          </div>
          {/* Botões variam conforme o estado */}
          {!recording && (
            <>
              <div className="w-full flex flex-row justify-start mb-2 pl-4">
                <Button
                  className="bg-gray-500 hover:bg-gray-600"
                  onClick={() => setFacingMode(facingMode === "user" ? "environment" : "user")}
                  type="button"
                >
                  Virar câmera ({facingMode === "user" ? "Frontal" : "Traseira"})
                </Button>
              </div>
              <div className="flex gap-2 mt-2">
                <Button className="bg-blue-500 hover:bg-blue-600" onClick={startRecording}>
                  Iniciar gravação
                </Button>
                <Button variant="outline" onClick={onCancel}>
                  Cancelar
                </Button>
              </div>
            </>
          )}
          {recording && (
            <>
              <div className="flex items-center gap-4 mt-2">
                <Button className="bg-red-500 hover:bg-red-600" onClick={stopRecording}>
                  Parar gravação
                </Button>
                {!paused ? (
                  <Button className="bg-yellow-500 hover:bg-yellow-600" onClick={pauseRecording}>
                    Pausar gravação
                  </Button>
                ) : (
                  <Button className="bg-green-500 hover:bg-green-600" onClick={resumeRecording}>
                    Retomar gravação
                  </Button>
                )}
              </div>
              <div className="flex flex-col items-center mt-1">
                <span className="text-lg font-mono text-gray-700">
                  {String(Math.floor(time / 60)).padStart(2, '0')}:{String(time % 60).padStart(2, '0')}
                </span>
                {timeLimitReached && (
                  <span className="text-red-600 font-semibold mt-1">Tempo máximo de 30 segundos atingido!</span>
                )}
              </div>
            </>
          )}
        </div>
      )}
      {/* Preview do vídeo gravado */}
      {previewUrl && (
        <div className="flex flex-col items-center gap-2 w-full">
          <div className={`${fullScreenCamera ? "w-full max-w-[100vw] h-full max-h-[90vh]" : "w-full max-w-[360px] h-full max-h-[480px]"} bg-black rounded overflow-hidden mx-auto`}>
            <video src={previewUrl} controls className="w-full h-full object-cover" />
          </div>
          <div className="flex gap-2">
            <Button className="bg-green-500 hover:bg-green-600" onClick={handleSave}>
              Usar vídeo
            </Button>
            <Button variant="outline" onClick={onCancel}>
              Cancelar
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

export default function MultipleVideoUploadForm({ onSave, onCancel, initialFiles = [], maxVideos = 2 }: MultipleVideoUploadFormProps) {
  // Novo: inicializa com vídeos já existentes
  const [selectedFiles, setSelectedFiles] = useState<FileWithMetadata[]>(() =>
    initialFiles.map((item) => ({
      id: item.id,
      url: item.url,
      description: item.description,
      thumbnailUrl: item.thumbnailUrl,
      uploading: false,
      progress: 100,
    }))
  );
  const [previewUrls, setPreviewUrls] = useState<{ [key: string]: string }>(() => {
    const obj: { [key: string]: string } = {};
    initialFiles.forEach((item) => {
      obj[item.id] = item.url;
    });
    return obj;
  });
  const [errors, setErrors] = useState<{ [key: number]: string }>({});

  // Novo: controla se já inicializou os arquivos
  const [initialized, setInitialized] = useState(false);
  const [recorderOpen, setRecorderOpen] = useState(false); // mover para cima
  const toast = useCenterToast();

  useEffect(() => {
    if (!initialized) {
      setSelectedFiles(
        (initialFiles || []).map((item) => ({
          id: item.id,
          url: item.url,
          description: item.description,
          thumbnailUrl: item.thumbnailUrl,
          uploading: false,
          progress: 100,
        }))
      );
      setPreviewUrls(() => {
        const obj: { [key: string]: string } = {};
        (initialFiles || []).forEach((item) => {
          obj[item.id] = item.url;
        });
        return obj;
      });
      setInitialized(true);
    }
  }, [initialFiles, initialized]);

  // Resetar initialized ao fechar o modal (quando recorderOpen for false)
  useEffect(() => {
    if (!recorderOpen) {
      setInitialized(false);
    }
  }, [recorderOpen]);

  const [isDragDropping, setIsDragDropping] = useState(false);
  const [globalDescription, setGlobalDescription] = useState<string>("");
  const [loading, setLoading] = useState(false); // Novo estado de loading

  useEffect(() => {
    return () => {
      Object.values(previewUrls).forEach(url => {
        if (url.startsWith('blob:')) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, [previewUrls]);

  const generateUniqueId = () => {
    return `file-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  };

  // const getFileDataUrl = (file: File): Promise<string> => {
  //   return new Promise((resolve, reject) => {
  //     const reader = new FileReader();
  //     reader.onload = e => resolve(e.target?.result as string);
  //     reader.onerror = reject;
  //     reader.readAsDataURL(file);
  //   });
  // };

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const filesArray = Array.from(e.target.files);
      await processFiles(filesArray);
    }
  };

  const processFiles = async (filesArray: File[]) => {
    setLoading(true); // Inicia o loading
    try {
      // Filtrar apenas arquivos de vídeo
      const videoFiles = filesArray.filter(file => file.type.startsWith('video/'));
      if (videoFiles.length === 0) {
        toast.error('Por favor, selecione apenas arquivos de vídeo.');
        setLoading(false);
        return;
      }
      // Limitar quantidade considerando já existentes
      if (selectedFiles.length + videoFiles.length > maxVideos) {
        toast.error(`Você pode adicionar no máximo ${maxVideos} vídeo${maxVideos > 1 ? 's' : ''} neste envio.`);
        setLoading(false);
        return;
      }
      // Limitar tamanho
      for (const file of videoFiles) {
        if (file.size > MAX_SIZE_MB * 1024 * 1024) {
          toast.error(`O vídeo "${file.name}" excede o tamanho máximo de ${MAX_SIZE_MB}MB.`);
          setLoading(false);
          return;
        }
      }
      // Geração de thumbnails
      // Função para detectar se o frame é "preto"
      const isFrameMostlyBlack = (canvas: HTMLCanvasElement, threshold = 30) => {
        const ctx = canvas.getContext('2d');
        if (!ctx) return true;
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        let blackPixels = 0;
        for (let i = 0; i < imageData.data.length; i += 4) {
          if (
            imageData.data[i] < threshold &&
            imageData.data[i + 1] < threshold &&
            imageData.data[i + 2] < threshold
          ) {
            blackPixels++;
          }
        }
        return blackPixels > (imageData.data.length / 4) * 0.8;
      };

      // Nova função de thumbnail: tenta múltiplos pontos
      const generateThumbnail = (file: File): Promise<Blob | undefined> => {
        return new Promise((resolve) => {
          const video = document.createElement('video');
          video.preload = 'metadata';
          video.src = URL.createObjectURL(file);
          video.muted = true;
          video.playsInline = true;

          // Pontos para tentar (em segundos ou porcentagem)
          const tryPoints: number[] = [0.1, 0.2, 0.3, 0.5]; // 10%, 20%, 30%, 50%
          let currentTry = 0;

          const trySeek = () => {
            if (currentTry >= tryPoints.length) {
              // Não achou frame bom, pega o último mesmo
              captureFrame();
              return;
            }
            // Verifica se duration é válido
            if (!isFinite(video.duration) || video.duration === 0) {
              video.currentTime = 0;
              return;
            }
            const time = Math.min(video.duration * tryPoints[currentTry], video.duration - 0.1);
            video.currentTime = time;
          };

          const captureFrame = () => {
            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const ctx = canvas.getContext('2d');
            ctx?.drawImage(video, 0, 0, canvas.width, canvas.height);
            // Checa se frame é preto
            if (!isFrameMostlyBlack(canvas) || currentTry === tryPoints.length) {
              canvas.toBlob((blob) => {
                resolve(blob || undefined);
                URL.revokeObjectURL(video.src);
              }, 'image/jpeg', 0.92);
            } else {
              // Tenta próximo ponto
              currentTry++;
              trySeek();
            }
          };

          video.onloadedmetadata = () => {
            trySeek();
          };
          video.onseeked = () => {
            captureFrame();
          };
          video.onerror = () => {
            resolve(undefined);
            URL.revokeObjectURL(video.src);
          };
        });
      };
      // Gera thumbnails para todos os vídeos
      const newFiles = await Promise.all(videoFiles.map(async (file) => {
        const thumb = await generateThumbnail(file);
        return {
          file,
          description: "",
          id: generateUniqueId(),
          thumbnail: thumb,
        };
      }));
      const newPreviewUrlsObj: { [key: string]: string } = {};
      newFiles.forEach(fileItem => {
        newPreviewUrlsObj[fileItem.id] = URL.createObjectURL(fileItem.file!);
      });
      setSelectedFiles([...selectedFiles, ...newFiles]);
      setPreviewUrls({ ...previewUrls, ...newPreviewUrlsObj });
    } finally {
      setLoading(false); // Finaliza o loading
    }
  };

  const handleDrop = async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragDropping(false);
    if (event.dataTransfer.files.length > 0) {
      const droppedFiles = Array.from(event.dataTransfer.files);
      await processFiles(droppedFiles);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragDropping(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragDropping(false);
  };

  const handleDragAreaClick = () => {
    document.getElementById("multiple-videos")?.click();
  };

  const handleDescriptionChange = (index: number, value: string) => {
    const updatedFiles = [...selectedFiles];
    updatedFiles[index].description = value;
    setSelectedFiles(updatedFiles);
    if (value.trim().length > 0 && errors[index]) {
      const newErrors = { ...errors };
      delete newErrors[index];
      setErrors(newErrors);
    }
  };

  const handleRemoveFile = (index: number) => {
    try {
      const fileToRemove = selectedFiles[index];
      // Só revoga se for blob local
      if (fileToRemove.file && previewUrls[fileToRemove.id]) {
        URL.revokeObjectURL(previewUrls[fileToRemove.id]);
      }
      const newFiles = selectedFiles.filter((_, i) => i !== index);
      const newPreviewUrlsObj = { ...previewUrls };
      delete newPreviewUrlsObj[fileToRemove.id];
      const newErrors = { ...errors };
      delete newErrors[index];
      const reindexedErrors: { [key: number]: string } = {};
      Object.keys(newErrors).forEach(key => {
        const numKey = parseInt(key);
        if (numKey > index) {
          reindexedErrors[numKey - 1] = newErrors[numKey];
        } else {
          reindexedErrors[numKey] = newErrors[numKey];
        }
      });
      setSelectedFiles(newFiles);
      setPreviewUrls(newPreviewUrlsObj);
      setErrors(reindexedErrors);
    } catch (error) {
      console.error("Erro ao remover arquivo:", error);
    }
  };

  const handleMoveUp = (index: number) => {
    if (index <= 0) return;
    try {
      const newFiles = [...selectedFiles];
      const itemToMove = newFiles[index];
      newFiles.splice(index, 1);
      newFiles.splice(index - 1, 0, itemToMove);
      setSelectedFiles(newFiles);
      if (Object.keys(errors).length > 0) {
        const newErrors: { [key: number]: string } = {};
        Object.entries(errors).forEach(([key, value]) => {
          const errorIndex = parseInt(key);
          if (errorIndex === index) {
            newErrors[index - 1] = value;
          } else if (errorIndex === index - 1) {
            newErrors[index] = value;
          } else {
            newErrors[errorIndex] = value;
          }
        });
        setErrors(newErrors);
      }
    } catch (error) {
      console.error("Erro ao mover vídeo para cima:", error);
    }
  };

  const handleMoveDown = (index: number) => {
    if (index >= selectedFiles.length - 1) return;
    try {
      const newFiles = [...selectedFiles];
      const itemToMove = newFiles[index];
      newFiles.splice(index, 1);
      newFiles.splice(index + 1, 0, itemToMove);
      setSelectedFiles(newFiles);
      if (Object.keys(errors).length > 0) {
        const newErrors: { [key: number]: string } = {};
        Object.entries(errors).forEach(([key, value]) => {
          const errorIndex = parseInt(key);
          if (errorIndex === index) {
            newErrors[index + 1] = value;
          } else if (errorIndex === index + 1) {
            newErrors[index] = value;
          } else {
            newErrors[errorIndex] = value;
          }
        });
        setErrors(newErrors);
      }
    } catch (error) {
      console.error("Erro ao mover vídeo para baixo:", error);
    }
  };

  // Validação: não permitir salvar sem legenda
  const validateForm = (): boolean => {
    const newErrors: { [key: number]: string } = {};
    let isValid = true;
    selectedFiles.forEach((item, index) => {
      if (!item.description.trim()) {
        newErrors[index] = "Legenda é obrigatória";
        isValid = false;
      }
    });
    setErrors(newErrors);
    return isValid;
  };

  const handleSave = () => {
    if (!validateForm()) return;
    // Apenas retorna os arquivos selecionados, sem upload
    onSave(selectedFiles.map(item => ({
      file: item.file,
      description: item.description,
      thumbnail: item.thumbnail,
      // outros campos necessários
    })));
  };

  const handleApplyGlobalDescription = () => {
    if (!globalDescription) return;
    const updated = selectedFiles.map(item => ({
      ...item,
      description: item.description?.trim() ? item.description : globalDescription
    }));
    setSelectedFiles(updated);
  };

  const handleAddRecordedVideo = (file: File) => {
    // Reutiliza a lógica de processFiles para adicionar o vídeo gravado
    processFiles([file]);
    setRecorderOpen(false);
  };

  return (
    <div className="flex flex-col gap-4">
      {/* Loading overlay */}
      {loading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="flex flex-col items-center gap-4 bg-white rounded-lg p-8 shadow-lg border border-gray-200">
            <VideoIcon className="animate-spin h-10 w-10 text-blue-500" />
            <span className="text-blue-700 font-semibold">Processando vídeos...</span>
          </div>
        </div>
      )}
      {/* Botões de upload e gravação */}
      <div className="flex gap-2 mb-2">
        <Button
          type="button"
          className="bg-blue-500 hover:bg-blue-600"
          onClick={handleDragAreaClick}
          disabled={selectedFiles.length >= maxVideos}
        >
          Selecionar vídeos
        </Button>
        <Button
          type="button"
          className="bg-orange-500 hover:bg-orange-600 sm:hidden"
          onClick={() => setRecorderOpen(true)}
          disabled={selectedFiles.length >= maxVideos}
        >
          Gravar vídeo
        </Button>
      </div>
      {/* Modal de gravação de vídeo */}
      <Dialog open={recorderOpen} onOpenChange={setRecorderOpen}>
        <DialogContent className="sm:max-w-[100vw] h-[100dvh] flex flex-col justify-center items-center p-0 border-0">
          <DialogHeader className="w-full text-center pt-6">
            <DialogTitle>Gravar vídeo</DialogTitle>
            <DialogDescription>Use sua câmera para gravar um vídeo de até 30 segundos e adicioná-lo à inspeção.</DialogDescription>
          </DialogHeader>
          <div className="flex-1 flex flex-col justify-center items-center w-full">
            <VideoRecorder
              onSave={handleAddRecordedVideo}
              onCancel={() => setRecorderOpen(false)}
              fullScreenCamera
            />
          </div>
        </DialogContent>
      </Dialog>
      {/* Input de arquivo para galeria (oculto, acionado pelo clique na área de drag and drop) */}
      <input
        type="file"
        id="multiple-videos"
        className="hidden"
        multiple
        accept="video/*"
        onChange={handleFileSelect}
      />
      {/* Área de drag and drop */}
      <div
        className={`border-2 border-dashed rounded-lg p-4 mt-2 min-h-[100px] ${isDragDropping ? 'border-blue-400 bg-blue-50' : 'border-gray-300 bg-white'}`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={handleDragAreaClick}
        style={{ cursor: 'pointer' }}
      >
        <div className="flex flex-col items-center justify-center h-full">
          <VideoIcon className="h-10 w-10 text-gray-400 mb-2" />
          <span className="text-gray-500">Arraste e solte vídeos aqui ou clique para selecionar</span>
          <span className="text-xs text-gray-400 mt-1">Máximo de {maxVideos} vídeo{maxVideos > 1 ? 's' : ''}, até 50MB cada</span>
        </div>
      </div>
      {/* Campo para legenda global com botão de aplicar */}
      <div className="flex gap-2 items-end">
        <div className="flex-1 min-w-0">
          <Label htmlFor="video-global-description">Legenda global para todos os vídeos</Label>
          <Input
            id="video-global-description"
            value={globalDescription}
            onChange={e => setGlobalDescription(e.target.value)}
            placeholder="Digite uma legenda para aplicar a todos os vídeos"
          />
        </div>
        <Button
          type="button"
          className="bg-blue-500 hover:bg-blue-600 h-10"
          onClick={handleApplyGlobalDescription}
          disabled={!globalDescription || selectedFiles.length === 0}
        >
          Aplicar
        </Button>
      </div>
      {/* Lista de vídeos */}
      {selectedFiles.length > 0 ? (
        <DragDropContext onDragEnd={(result: DropResult) => {
          if (!result.destination) return;
          const newFiles = Array.from(selectedFiles);
          const [removed] = newFiles.splice(result.source.index, 1);
          newFiles.splice(result.destination.index, 0, removed);
          setSelectedFiles(newFiles);
        }}>
          <Droppable droppableId="videos" direction="vertical">
            {(provided) => (
              <div ref={provided.innerRef} {...provided.droppableProps} className="flex flex-col gap-4 mt-4">
                {selectedFiles.map((item, index) => (
                  <Draggable key={item.id} draggableId={item.id} index={index}>
                    {(provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        className="flex items-center gap-4 border rounded-md p-2 bg-gray-50"
                      >
                        <div {...provided.dragHandleProps} className="drag-handle text-gray-400 hover:text-gray-600 cursor-grab">
                          <GripVertical className="w-5 h-5" />
                        </div>
                        {/* Barra de progresso se estiver fazendo upload */}
                        {item.uploading ? (
                          <div className="flex flex-col items-center w-32 h-20 justify-center">
                            <div className="w-full bg-gray-200 rounded h-2 overflow-hidden mt-2">
                              <div
                                className="bg-blue-500 h-2 transition-all duration-200"
                                style={{ width: `${item.progress ?? 0}%` }}
                              />
                            </div>
                            <span className="text-xs text-blue-700 mt-1">Enviando... {Math.round(item.progress ?? 0)}%</span>
                          </div>
                        ) : (
                          <video src={item.file ? previewUrls[item.id] : item.url} controls className="w-32 h-20 rounded-md bg-black" />
                        )}
                        <div className="flex-grow min-w-0">
                          <Label htmlFor={`video-caption-${index}`} className="font-bold text-gray-700 truncate">
                            Legenda
                          </Label>
                          <Input
                            id={`video-caption-${index}`}
                            value={item.description}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleDescriptionChange(index, e.target.value)}
                            placeholder="Digite uma legenda para este vídeo"
                            className={errors[index] ? "border-red-500" : ""}
                            disabled={item.uploading}
                          />
                          {errors[index] && (
                            <small className="text-xs font-semibold text-destructive">
                              {errors[index]}
                            </small>
                          )}
                        </div>
                        <div className="flex flex-col gap-1 self-start mt-6">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-blue-500 hover:bg-blue-50 hover:text-blue-600 h-8 w-8 flex-shrink-0"
                            onClick={() => handleMoveUp(index)}
                            disabled={index === 0 || item.uploading}
                            title="Mover para cima"
                          >
                            <ChevronUp className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-blue-500 hover:bg-blue-50 hover:text-blue-600 h-8 w-8 flex-shrink-0"
                            onClick={() => handleMoveDown(index)}
                            disabled={index === selectedFiles.length - 1 || item.uploading}
                            title="Mover para baixo"
                          >
                            <ChevronDown className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-red-500 hover:bg-red-50 hover:text-red-500 h-8 w-8 flex-shrink-0"
                            onClick={() => handleRemoveFile(index)}
                            title="Remover"
                            disabled={item.uploading}
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      ) : (
        <div className="text-center py-10 bg-white rounded-md border border-dashed border-gray-300 mt-4">
          <VideoIcon className="mx-auto h-12 w-12 text-gray-400" />
          <p className="text-gray-500 mt-2">Nenhum vídeo selecionado</p>
          <p className="text-sm text-gray-400 mt-1">Escolha uma das opções acima para adicionar vídeos</p>
        </div>
      )}
      {/* Botões de ação */}
      <div className="flex flex-col sm:flex-row justify-end gap-2 mt-4">
        <Button
          onClick={onCancel}
          className="bg-blue-500 hover:bg-blue-600 w-full sm:w-auto text-white"
        >
          Cancelar
        </Button>
        <Button
          className="bg-green-500 hover:bg-green-600 w-full sm:w-auto"
          onClick={handleSave}
          disabled={selectedFiles.length === 0}
        >
          Adicionar {selectedFiles.length} vídeo{selectedFiles.length !== 1 ? 's' : ''}
        </Button>
      </div>
    </div>
  );
} 