import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/src/providers/auth";
import nodemailer from "nodemailer";

export const dynamic = "force-dynamic";
export const revalidate = 0;

interface SMTPError extends Error {
  code?: string;
  command?: string;
  response?: string;
}

// Função para verificar se o usuário está autenticado
async function isAuthenticated() {
  const session = await auth();
  return !!session?.user;
}

export async function GET(request: NextRequest) {
  console.log("=== INICIANDO TESTE DE DEBUG DE EMAIL ===");
  try {
    // Verificar autenticação
    console.log("Verificando autenticação...");
    const authenticated = await isAuthenticated();
    if (!authenticated) {
      console.log("Erro: Usuário não autenticado");
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }
    console.log("Autenticação verificada com sucesso");

    // Verificar configurações de email
    console.log("Verificando configurações de email...");
    const host = process.env.EMAIL_HOST;
    const port = process.env.EMAIL_PORT;
    const user = process.env.EMAIL_USER;
    // const pass = process.env.EMAIL_PASS ? '***' : undefined; // Ocultar senha por segurança
    const from = process.env.EMAIL_FROM;

    if (!host || !user || !process.env.EMAIL_PASS || !from) {
      console.log("Erro: Configurações de email incompletas", {
        host,
        port,
        user,
        from,
      });
      return NextResponse.json(
        {
          error: "Configurações de email incompletas",
          config: { host, port, user, from },
        },
        { status: 400 }
      );
    }

    console.log("Configurações de email encontradas:", {
      host,
      port,
      user,
      from,
    });

    // Criar transporter
    console.log("Criando transporter para teste...");
    const transporter = nodemailer.createTransport({
      host,
      port: parseInt(port || "587", 10),
      secure: parseInt(port || "587", 10) === 465, // true para 465, false para outras portas
      auth: {
        user,
        pass: process.env.EMAIL_PASS,
      },
      debug: true, // Ativar logs de debug
      logger: true, // Ativar logger
    });

    // Verificar conexão com o servidor SMTP
    console.log("Verificando conexão com o servidor SMTP...");
    try {
      const verifyResult = await transporter.verify();
      console.log(
        "Conexão com servidor SMTP verificada com sucesso:",
        verifyResult
      );

      // Tentar enviar um email de teste
      console.log("Tentando enviar email de teste...");
      const testEmail = request.nextUrl.searchParams.get("email");

      if (!testEmail) {
        return NextResponse.json({
          success: true,
          message:
            "Configurações de email verificadas com sucesso. Para enviar um email de teste, adicione o parâmetro ?email=<EMAIL>",
          config: { host, port, user, from },
        });
      }

      const info = await transporter.sendMail({
        from: {
          name: "Teste de Email Ageu",
          address: from,
        },
        to: testEmail,
        subject: "Teste de Email - Ageu",
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h1 style="color: #333;">Teste de Email</h1>
            </div>
            <div style="color: #555; line-height: 1.5;">
              <p>Este é um email de teste enviado pelo sistema Ageu.</p>
              <p>Se você está recebendo este email, significa que a configuração de email está funcionando corretamente.</p>
              <p>Detalhes da configuração:</p>
              <ul>
                <li>Host: ${host}</li>
                <li>Porta: ${port}</li>
                <li>Usuário: ${user}</li>
                <li>Email de origem: ${from}</li>
              </ul>
              <p>Data e hora do envio: ${new Date().toLocaleString()}</p>
            </div>
            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>Este é um email automático de teste, por favor não responda.</p>
              <p>© ${new Date().getFullYear()} Ageu Engenharia. Todos os direitos reservados.</p>
            </div>
          </div>
        `,
      });

      console.log("Email de teste enviado com sucesso!");
      console.log("Detalhes do envio:", {
        messageId: info.messageId,
        response: info.response,
        accepted: info.accepted,
        rejected: info.rejected,
      });

      return NextResponse.json({
        success: true,
        message: "Email de teste enviado com sucesso",
        details: {
          messageId: info.messageId,
          accepted: info.accepted,
          rejected: info.rejected,
        },
        config: { host, port, user, from },
      });
    } catch (error: unknown) {
      const smtpError = error as SMTPError;
      console.error("Erro ao verificar conexão com servidor SMTP:", smtpError);
      console.error("Detalhes do erro SMTP:", {
        name: smtpError.name,
        message: smtpError.message,
        code: smtpError.code,
        command: smtpError.command,
        response: smtpError.response,
      });

      return NextResponse.json(
        {
          error: "Falha na conexão com o servidor SMTP",
          details: {
            message: smtpError.message,
            code: smtpError.code,
            response: smtpError.response,
          },
          config: { host, port, user, from },
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("=== ERRO GERAL NO TESTE DE DEBUG DE EMAIL ===");
    console.error("Erro:", error);
    console.error("Detalhes do erro:", {
      name: (error as Error).name,
      message: (error as Error).message,
      stack: error instanceof Error ? error.stack : undefined,
    });

    return NextResponse.json(
      {
        error: "Erro ao testar configurações de email",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  } finally {
    console.log("=== FIM DO TESTE DE DEBUG DE EMAIL ===");
  }
}
