"use client";

import dynamic from "next/dynamic";
import Loading from "./app-loading";

export const AppDocumentEditor = dynamic(() => import("react-quill"), {
	ssr: false,
	loading: () => <Loading />,
});

export const documentEditorModules = {
	toolbar: [
		[{ font: [] }],
		[{ header: [1, 2, false] }],
		["bold", "italic", "underline", "strike"],
		[{ color: [] }, { background: [] }],
		[{ script: "sub" }, { script: "super" }],
		[{ list: "ordered" }, { list: "bullet" }],
		[{ indent: "-1" }, { indent: "+1" }],
		[{ direction: "rtl" }],
		[{ align: [] }],
		["link", "image"],
		["clean"],
	],
};

export const documentEditorFormats = [
	"font",
	"header",
	"bold",
	"italic",
	"underline",
	"strike",
	"color",
	"background",
	"script",
	"list",
	"bullet",
	"indent",
	"direction",
	"align",
	"link",
	"image",
];
