"use server";
import { prisma } from "@/src/lib/prisma";
import { NextResponse } from "next/server";

export async function POST() {
  try {
    console.log("Iniciando atualização de thumbnailPath...");

    // Buscar todos os arquivos que têm thumbnailPath começando com "thumbnails/"
    const filesToUpdate = await prisma.file.findMany({
      where: {
        thumbnailPath: {
          startsWith: "thumbnails/",
        },
      },
      select: {
        id: true,
        name: true,
        thumbnailPath: true,
      },
    });

    console.log(
      `Encontrados ${filesToUpdate.length} arquivos com thumbnailPath para atualizar`
    );

    if (filesToUpdate.length === 0) {
      return NextResponse.json({
        success: true,
        message: "Nenhum thumbnailPath encontrado para atualizar",
        totalFiles: 0,
        updatedFiles: 0,
        logs: [],
      });
    }

    const logs: Array<{
      fileId: string;
      fileName: string;
      oldThumbnailPath: string;
      newThumbnailPath: string;
      status: string;
    }> = [];

    let updatedCount = 0;

    // Atualizar cada arquivo
    for (const file of filesToUpdate) {
      try {
        // Adicionar "matriz/" na frente do thumbnailPath
        const newThumbnailPath = `matriz/${file.thumbnailPath}`;

        await prisma.file.update({
          where: { id: file.id },
          data: { thumbnailPath: newThumbnailPath },
        });

        updatedCount++;
        logs.push({
          fileId: file.id,
          fileName: file.name,
          oldThumbnailPath: file.thumbnailPath!,
          newThumbnailPath: newThumbnailPath,
          status: "updated",
        });

        console.log(
          `ThumbnailPath atualizado: ${file.name} - ${file.thumbnailPath} -> ${newThumbnailPath}`
        );
      } catch (error) {
        console.error(`Erro ao atualizar arquivo ${file.id}:`, error);
        logs.push({
          fileId: file.id,
          fileName: file.name,
          oldThumbnailPath: file.thumbnailPath!,
          newThumbnailPath: `matriz/${file.thumbnailPath}`,
          status: "error",
        });
      }
    }

    console.log(
      `Atualização de thumbnailPath concluída. ${updatedCount} arquivos atualizados de ${filesToUpdate.length} encontrados.`
    );

    return NextResponse.json({
      success: true,
      message: `Atualização de thumbnailPath concluída com sucesso`,
      totalFiles: filesToUpdate.length,
      updatedFiles: updatedCount,
      logs: logs,
    });
  } catch (error) {
    console.error("Erro durante a atualização de thumbnailPath:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Erro interno do servidor",
        error: error instanceof Error ? error.message : "Erro desconhecido",
      },
      { status: 500 }
    );
  }
}
