"use client";

import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from "react";
import { TableGrid } from "@/src/components/ui/table-grid";
import { useToast } from "@/src/hooks/use-toast";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Eraser, Plus } from "lucide-react";
import { ReportTemplateInterface } from "@/src/types/core/report-template";

interface ReportTemplatesTableProps {
  columns: any[];
  onAddClick: () => void;
  onPageChange?: (page: number) => void;
}

export type ReportTemplatesTableRef = {
  refresh: (page?: number) => void;
};

const ReportTemplatesTable = forwardRef<ReportTemplatesTableRef, ReportTemplatesTableProps>(
  function ReportTemplatesTable({ columns, onAddClick, onPageChange }, ref) {
    const [data, setData] = useState<ReportTemplateInterface[]>([]);
    const [loading, setLoading] = useState(false);
    const [search, setSearch] = useState("");
    const searchInputRef = useRef<HTMLInputElement>(null);
    const { toast } = useToast();
    const [pagination, setPagination] = useState({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });

    const fetchReportTemplates = useCallback(async (
      page?: number,
      pageSize: number = pagination.pageSize,
      searchTerm: string = search
    ) => {
      const currentPage = page || pagination.page;

      try {
        setLoading(true);
        const params = new URLSearchParams({
          page: String(currentPage),
          pageSize: String(pageSize)
        });

        if (searchTerm) {
          params.append('search', searchTerm);
        }

        const response = await fetch(`/api/report-templates?${params}`);

        if (!response.ok) throw new Error("Failed to fetch report templates");

        const result = await response.json();

        setData(Array.isArray(result.data) ? result.data : []);
        setPagination(prev => ({
          ...prev,
          page: Number(result.page),
          pageSize: Number(result.pageSize),
          total: Number(result.total),
          totalPages: Number(result.totalPages)
        }));

      } catch (error) {
        console.error('Fetch error:', error);
        toast({
          title: "Erro",
          description: "Erro ao carregar templates de relatórios",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }, [pagination.pageSize, search, toast]);

    useImperativeHandle(ref, () => ({
      refresh: (page?: number) => fetchReportTemplates(page)
    }), [fetchReportTemplates]);

    // Carregar dados quando o componente for montado
    useEffect(() => {
      fetchReportTemplates(1);
    }, [fetchReportTemplates]);

    const handleSearch = (value: string) => {
      setSearch(value);
      fetchReportTemplates(1, pagination.pageSize, value);
    };

    const handleClearFilters = () => {
      // Limpar o estado de pesquisa
      setSearch("");

      // Resetar para a primeira página e buscar dados
      fetchReportTemplates(1, pagination.pageSize, "");

      // Atualizar a página atual no componente pai
      if (onPageChange) onPageChange(1);

      // Focar no campo de pesquisa após limpar
      setTimeout(() => {
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }, 0);
    };

    return (
      <TableGrid
        columns={columns}
        data={data}
        loading={loading}
        pagination={pagination}
        onPaginationChange={(newPage, newPageSize) => {
          fetchReportTemplates(newPage, newPageSize);
          if (onPageChange) onPageChange(newPage);
        }}
        onSearch={handleSearch}
        searchValue={search}
        searchInputRef={searchInputRef}
        buttonsTemplate={
          <>
            <Button
              className="w-full sm:w-auto bg-blue-500 hover:bg-blue-400"
              onClick={handleClearFilters}
            >
              Limpar filtros <Eraser className="ml-2 h-4 w-4" />
            </Button>
            <Button
              className="w-full sm:w-auto bg-green-500 hover:bg-green-400"
              onClick={onAddClick}
            >
              Adicionar <Plus className="ml-2 h-4 w-4" />
            </Button>
          </>
        }
      />
    );
  }
);

export default ReportTemplatesTable;
