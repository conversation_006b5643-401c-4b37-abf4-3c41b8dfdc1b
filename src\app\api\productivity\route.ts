import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";
import { parseObject } from "@/src/lib/utils";

// Configuração para marcar esta rota como dinâmica
export const dynamic = "force-dynamic";

// GET /api/productivity?proposalId=xxx&repairBudgetId=xxx&periodId=xxx
export async function GET(request: NextRequest) {
  try {
    // Obter parâmetros da URL
    const url = new URL(request.url);
    const proposalId = url.searchParams.get("proposalId");
    const repairBudgetId = url.searchParams.get("repairBudgetId");
    const periodId = url.searchParams.get("periodId");

    console.log("CRITICAL FIX: GET /api/productivity");
    console.log("- proposalId:", proposalId);
    console.log("- repairBudgetId:", repairBudgetId);
    console.log("- periodId:", periodId);

    // Verificar se os parâmetros obrigatórios estão presentes
    if (!proposalId || !repairBudgetId) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    // Verificar se o período é um mês/ano (formato "MM-YYYY")
    const isMonthYearPeriod = periodId && periodId.includes('-') && /^\d{1,2}-\d{4}$/.test(periodId);
    let month: number | null = null;
    let year: number | null = null;

    if (isMonthYearPeriod && periodId) {
      // Extrair mês e ano do período
      const [monthStr, yearStr] = periodId.split('-');
      month = parseInt(monthStr, 10); // Mês real (1-12)
      year = parseInt(yearStr, 10);
      console.log(`CRITICAL FIX: Month-year period: month=${month}, year=${year}`);
    }

    // Buscar o orçamento pelo ID
    const repairBudget = await prisma.repairBudget.findUnique({
      where: {
        id: repairBudgetId
      },
      include: {
        serviceScope: true,
        planningFrequencyItems: true
      }
    });

    if (!repairBudget) {
      return NextResponse.json(
        { error: `RepairBudget with ID ${repairBudgetId} not found` },
        { status: 404 }
      );
    }

    // Buscar TODAS as medições para este orçamento e proposta
    const allProductivities = await prisma.productivity.findMany({
      where: {
        repairBudgetId: repairBudgetId,
        proposalId: proposalId
      },
      include: {
        service: true,
        periodicity: true
      },
      orderBy: {
        startDate: 'desc'
      }
    });

    console.log(`CRITICAL FIX: Found ${allProductivities.length} productivity records for this repairBudget`);

    // Calcular o total já medido em todos os períodos
    let totalMedido = 0;
    allProductivities.forEach(p => {
      const realValue = Number(p.realPeriodPercentage) || 0;
      console.log(`CRITICAL FIX: Productivity ${p.id}: ${realValue}%`);
      totalMedido += realValue;
    });

    console.log('CRITICAL FIX: Total já medido em todos os períodos:', totalMedido);

    console.log(`CRITICAL FIX: Found ${allProductivities.length} productivity records`);

    // Encontrar a medição específica para o período selecionado
    let selectedProductivity: typeof allProductivities[0] | undefined = undefined;

    if (periodId) {
      // Primeiro, tentar encontrar pelo ID exato do período
      selectedProductivity = allProductivities.find(p =>
        p.periodicityId === periodId
      );

      // Se não encontrar e for um período de mês/ano, tentar pelo conteúdo
      if (!selectedProductivity && isMonthYearPeriod && month && year) {
        console.log(`CRITICAL FIX: Searching by content for month=${month}, year=${year}`);

        selectedProductivity = allProductivities.find(p =>
          p.periodicity &&
          p.periodicity.content &&
          p.periodicity.content.includes(`month=${month}`) &&
          p.periodicity.content.includes(`year=${year}`)
        );
      }
    }

    // Marcar a medição selecionada
    if (selectedProductivity) {
      console.log(`CRITICAL FIX: Found productivity for selected period:`, selectedProductivity.id);
      (selectedProductivity as any).isSelectedPeriod = true;
    } else {
      console.log(`CRITICAL FIX: No productivity found for selected period`);
    }

    // Preparar o resultado
    const result = {
      ...repairBudget,
      productivity: selectedProductivity ? [selectedProductivity] : [],
      allProductivities: allProductivities,
      // CRITICAL FIX: Adicionar o total já medido em todos os períodos
      totalMeasured: totalMedido,
      // Adicionar informações úteis para depuração
      debug: {
        proposalId,
        repairBudgetId,
        periodId,
        isMonthYearPeriod,
        month,
        year,
        selectedProductivityId: selectedProductivity?.id || null,
        totalMeasured: totalMedido
      }
    };

    console.log("CRITICAL FIX: Returning totalMeasured in API response:", totalMedido);

    // Converter para JSON seguro
    const safeResult = parseObject(result);

    return NextResponse.json(safeResult);
  } catch (error) {
    console.error("Error in GET /api/productivity:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
