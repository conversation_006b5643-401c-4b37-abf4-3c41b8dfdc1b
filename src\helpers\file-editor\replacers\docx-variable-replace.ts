import Docxtemplater from "docxtemplater";
import <PERSON><PERSON><PERSON><PERSON> from "pizzip";

import docxtemplaterImageModuleFree from "docxtemplater-image-module-free";

export async function mergeVariablesDocx(
  bufferFile: Buffer<ArrayBufferLike>,
  variables: Record<string, unknown>
) {
  console.log(`[DOCX MERGE] Iniciando merge de variáveis DOCX...`);
  const startTime = Date.now();

  // Log para depuração - verificar se as variáveis contêm imagens
  if (variables.items && Array.isArray(variables.items)) {
    console.log(
      `[DOCX MERGE] Processando ${variables.items.length} items com imagens no docx:`,
      variables.items.map((item) => ({
        temImagem: !!(item as any).image,
        url: (item as any).image
          ? (item as any).image.substring(0, 100) + "..."
          : "sem imagem",
        numero: (item as any).numero,
        legenda: (item as any).legenda,
      }))
    );

    // Verificar se há algum item sem imagem
    const itemsSemImagem = variables.items.filter(
      (item) => !(item as any).image
    );
    if (itemsSemImagem.length > 0) {
      console.warn(
        "[DOCX MERGE] ATENÇÃO: Existem items sem imagem:",
        itemsSemImagem
      );
    }
  }
  // Configurar o módulo de imagem (versão default)
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const imageModule = new (docxtemplaterImageModuleFree as any)({
    getImage: async (tagValue: string) => {
      console.log("Processing tag:", tagValue);

      if (!tagValue) {
        console.error("Tag value is empty");
        // Retornar uma imagem vazia em vez de lançar erro
        return Buffer.from([]);
      }

      // Se a URL for um data URI (base64), converte diretamente
      if (tagValue.startsWith("data:")) {
        console.log("Processing base64 image");
        return Buffer.from(tagValue.split(",")[1], "base64");
      }

      // Se não for uma URL HTTP válida, lança erro
      if (!tagValue.startsWith("http")) {
        console.error(`URL de imagem inválida: ${tagValue}`);
        // Retornar uma imagem vazia em vez de lançar erro
        return Buffer.from([]);
      }

      try {
        // Para URLs HTTP, baixa a imagem
        console.log("Downloading image:", tagValue);

        // Verificar se a URL contém "undefined" ou "null"
        if (tagValue.includes("undefined") || tagValue.includes("null")) {
          console.error(`URL contém undefined ou null: ${tagValue}`);
          return Buffer.from([]);
        }

        const response = await fetch(tagValue);

        if (!response.ok) {
          console.error(
            `Erro na resposta HTTP: ${response.status} ${response.statusText}`
          );
          // Retornar uma imagem vazia em vez de lançar erro
          return Buffer.from([]);
        }

        const arrayBuffer = await response.arrayBuffer();
        console.log(
          `Imagem baixada com sucesso: ${tagValue} (${arrayBuffer.byteLength} bytes)`
        );
        return Buffer.from(arrayBuffer);
      } catch (error) {
        console.error(`Erro ao processar imagem: ${tagValue}`, error);
        // Retornar uma imagem vazia em vez de lançar erro
        return Buffer.from([]);
      }
    },
    getSize: async () => {
      //getSize: async (imgBuffer: Buffer) => {
      //const metadata = await sharp(imgBuffer).metadata();
      //return [metadata.width || 100, metadata.height || 100];
      return [200, 200];
    },
  });

  try {
    console.log(`[DOCX MERGE] Carregando arquivo no PizZip...`);
    // Carregar no PizZip
    const zip = new PizZip(bufferFile);

    console.log(`[DOCX MERGE] Configurando Docxtemplater...`);
    // V4: passar modules no construtor (não chamar attachModule depois)
    const doc = new Docxtemplater(zip, {
      paragraphLoop: true,
      linebreaks: true,
      delimiters: { start: "{{", end: "}}" },
      modules: [imageModule],
      // Adicionar configurações para melhor tratamento de erros
      errorLogging: true,
      nullGetter: () => "", // Retorna string vazia para variáveis não encontradas
    });

    console.log(`[DOCX MERGE] Iniciando renderização assíncrona...`);
    await doc.renderAsync(variables);

    console.log(`[DOCX MERGE] Gerando arquivo final...`);
    // Gera o arquivo final em buffer
    const output = doc.getZip().generate({ type: "nodebuffer" });

    const processingTime = Date.now() - startTime;
    console.log(
      `[DOCX MERGE] Merge concluído com sucesso em ${processingTime}ms, tamanho final: ${output.length} bytes`
    );

    return output;
  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error(
      `[DOCX MERGE] Erro durante merge após ${processingTime}ms:`,
      error
    );

    // Verificar se é erro específico do template
    if (error && typeof error === "object" && "error" in error) {
      const templateErrors = (error as any).error;
      if (Array.isArray(templateErrors) && templateErrors.length > 0) {
        const firstError = templateErrors[0];

        // Erro de tag de imagem fora de parágrafo
        if (firstError.properties?.id === "raw_tag_outerxml_invalid") {
          const tagName = firstError.properties.xtag;
          throw new Error(
            `Erro no template: A tag "${tagName}" não está dentro de um parágrafo válido. ` +
              `${
                tagName === "image"
                  ? "DICA: Se você está usando {{#primeiraImagem}}{{%image}}{{/primeiraImagem}}, " +
                    "tente usar apenas {{primeiraImagem}} (sem o loop) ou mova a tag para fora do loop. "
                  : ""
              }` +
              `Por favor, verifique se as tags de imagem estão inseridas corretamente no documento Word.`
          );
        }

        // Erro de tag XML não encontrada
        if (firstError.properties?.id === "no_xml_tag_found_at_left") {
          const tagValue = firstError.properties.part?.value;
          throw new Error(
            `Erro no template: Tag "${tagValue}" mal formatada. ` +
              `${
                tagValue === "image"
                  ? "DICA: Se você está usando loops com imagens ({{#primeiraImagem}}{{%image}}{{/primeiraImagem}}), " +
                    "isso pode causar conflitos. Tente simplificar usando apenas {{primeiraImagem}}. "
                  : ""
              }` +
              `Verifique se a tag está dentro de um parágrafo de texto no documento Word.`
          );
        }

        // Outros erros de template
        if (firstError.name === "TemplateError") {
          throw new Error(
            `Erro no template DOCX: ${firstError.message}. ` +
              `Verifique a formatação das tags no documento Word.`
          );
        }
      }
    }

    // Verificar se é erro de timeout ou memória
    if (error instanceof Error) {
      if (error.message.includes("timeout") || error.message.includes("time")) {
        throw new Error(
          `Timeout durante processamento do template DOCX: ${error.message}`
        );
      }
      if (error.message.includes("memory") || error.message.includes("heap")) {
        throw new Error(
          `Erro de memória durante processamento do template DOCX: ${error.message}`
        );
      }
    }

    throw error;
  }
}
