import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";
import { auth } from "@/src/providers/auth";

export const dynamic = "force-dynamic";
export const revalidate = 0;

// Função para verificar se o usuário está autenticado
async function isAuthenticated() {
  const session = await auth();
  return !!session?.user;
}

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticação
    const authenticated = await isAuthenticated();
    if (!authenticated) {
      console.log("Erro: Usuário não autenticado");
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    // Obter o ID do arquivo da query string
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      console.log("Erro: ID do arquivo não fornecido");
      return NextResponse.json(
        { error: "ID do arquivo não fornecido" },
        { status: 400 }
      );
    }

    console.log(`Buscando informações do arquivo com ID: ${id}`);

    // Buscar o arquivo pelo ID
    const file = await prisma.fileEditor.findUnique({
      where: { id },
      select: {
        id: true,
        key: true,
        filename: true,
        mimetype: true,
        bucket: true,
        version: true,
      },
    });

    if (!file) {
      console.log(`Arquivo com ID ${id} não encontrado`);
      return NextResponse.json(
        { error: "Arquivo não encontrado" },
        { status: 404 }
      );
    }

    console.log(`Arquivo encontrado:`, {
      id: file.id,
      key: file.key,
      filename: file.filename,
      mimetype: file.mimetype,
    });

    // Retornar as informações do arquivo
    return NextResponse.json({
      id: file.id,
      key: file.key,
      filename: file.filename,
      mimetype: file.mimetype,
      bucket: file.bucket,
      version: file.version?.toString(),
    });
  } catch (error) {
    console.error("Erro ao buscar informações do arquivo:", error);
    return NextResponse.json(
      { error: "Erro ao buscar informações do arquivo" },
      { status: 500 }
    );
  }
}
