import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";

// Configuração para marcar esta rota como dinâmica
export const dynamic = "force-dynamic";

export async function GET(request: Request) {
  try {
    // Obter os parâmetros da URL
    const url = new URL(request.url);
    const serviceId = url.searchParams.get("serviceId");
    const proposalId = url.searchParams.get("proposalId");
    const excludeId = url.searchParams.get("excludeId");

    console.log("API check-total-percentage chamada com parâmetros:");
    console.log("- serviceId:", serviceId);
    console.log("- proposalId:", proposalId);
    console.log("- excludeId:", excludeId || "não fornecido");

    if (!serviceId || !proposalId) {
      return NextResponse.json(
        { error: "serviceId e proposalId são obrigatórios" },
        { status: 400 }
      );
    }

    // IMPORTANTE: Verificar se o serviceId é na verdade um repairBudgetId
    // Isso pode estar causando confusão na consulta
    console.log("NOTA: Na implementação atual, o parâmetro 'serviceId' deve ser o ID do repairBudget");

    // Buscar todos os registros de produtividade para este serviço e proposta
    // Excluindo o registro atual se excludeId for fornecido
    // Usando Prisma em vez de SQL raw para evitar erros
    let productivityRecords;

    try {
      // Vamos usar diretamente o Prisma normal em vez da consulta SQL raw
      // para evitar problemas de sintaxe SQL
      console.log("Usando Prisma normal diretamente");
      console.log("Parâmetros da consulta:");
      console.log("- serviceId:", serviceId);
      console.log("- proposalId:", proposalId);
      console.log("- excludeId:", excludeId || "não fornecido");

      // Construir a consulta
      // CORREÇÃO: Usar repairBudgetId em vez de serviceId
      const whereClause = {
        ...(serviceId ? { repairBudgetId: serviceId } : {}), // serviceId é na verdade o repairBudgetId
        ...(proposalId ? { proposalId } : {}),
        ...(excludeId ? { NOT: { id: excludeId } } : {}),
      };

      console.log("Cláusula WHERE:", JSON.stringify(whereClause, null, 2));

      // Buscar todos os registros de produtividade para este serviço e proposta
      const allRecords = await prisma.productivity.findMany({
        where: whereClause,
        select: {
          id: true,
          repairBudgetId: true,
          realPeriodPercentage: true,
          startDate: true,
        },
        orderBy: {
          startDate: 'desc',
        },
      });

      console.log("Registros encontrados (brutos):", allRecords.length);

      // Verificar se há registros
      if (allRecords.length === 0) {
        console.log("ALERTA: Nenhum registro de produtividade encontrado com os parâmetros fornecidos");

        // Tentar uma consulta mais ampla para depuração
        console.log("Tentando uma consulta mais ampla para depuração...");
        const allProductivityRecords = await prisma.productivity.findMany({
          where: {
            proposalId,
          },
          select: {
            id: true,
            serviceId: true,
            repairBudgetId: true,
            realPeriodPercentage: true,
            startDate: true,
          },
          take: 10, // Limitar a 10 registros para não sobrecarregar os logs
        });

        console.log("Registros de produtividade para esta proposta:", allProductivityRecords.length);
        if (allProductivityRecords.length > 0) {
          console.log("Amostra de registros:", allProductivityRecords);
        }

        // Verificar se o repairBudgetId (passado como serviceId) está correto
        console.log("Verificando se o repairBudgetId existe...");
        const repairBudget = await prisma.repairBudget.findUnique({
          where: {
            id: serviceId, // serviceId é na verdade o repairBudgetId
          },
          select: {
            id: true,
            description: true, // Usando description em vez de name, que não existe no modelo
            serviceScopeId: true, // Usando serviceScopeId em vez de serviceId
          },
        });

        if (repairBudget) {
          console.log("RepairBudget encontrado:", repairBudget);

          // Verificar se existem registros de produtividade para este repairBudget
          const productivityCount = await prisma.productivity.count({
            where: {
              repairBudgetId: serviceId,
            },
          });

          console.log(`Total de registros de produtividade para este repairBudget: ${productivityCount}`);
        } else {
          console.log("ALERTA: RepairBudget não encontrado com ID:", serviceId);
        }
      }

      // CORREÇÃO: Não agrupar por repairBudgetId, usar todos os registros
      // Isso é necessário porque precisamos somar todos os registros de produtividade
      // para calcular o total já medido
      console.log("CORREÇÃO: Usando todos os registros encontrados sem agrupamento");

      // Usar diretamente todos os registros encontrados
      productivityRecords = allRecords;
      console.log("Total de registros a serem considerados:", productivityRecords.length);
    } catch (error) {
      console.error("Erro ao buscar registros de produtividade:", error);

      // Em caso de erro, inicializar com array vazio
      productivityRecords = [];
    }

    // Converter o resultado para o formato esperado
    const formattedRecords = Array.isArray(productivityRecords)
      ? productivityRecords.map((record: any) => ({
          id: record.id,
          realPeriodPercentage: record.realPeriodPercentage
        }))
      : [];

    console.log("Productivity records found:", formattedRecords);

    // Exibir detalhes de cada registro para depuração
    if (formattedRecords.length > 0) {
      console.log("Detalhes dos registros encontrados:");
      formattedRecords.forEach((record, index) => {
        console.log(`Registro ${index + 1}:`, {
          id: record.id,
          realPeriodPercentage: record.realPeriodPercentage
        });
      });
    }

    // Calcular a soma total de realPeriodPercentage
    let totalPercentage = 0;

    if (formattedRecords.length > 0) {
      totalPercentage = formattedRecords.reduce((sum, record) => {
        // Converter para número e garantir que não seja NaN
        const percentage = Number(record.realPeriodPercentage) || 0;
        console.log(`Somando percentual: ${percentage}`);
        return sum + percentage;
      }, 0);
    }

    // Arredondar para 2 casas decimais para evitar problemas de precisão
    totalPercentage = Math.round(totalPercentage * 100) / 100;

    // CRITICAL FIX: Verificar se o total está correto
    console.log('CRITICAL FIX: Total calculado a partir dos registros filtrados:', totalPercentage);

    // CRITICAL FIX: Buscar todas as produtividades para este repairBudget para verificação
    const allProductivities = await prisma.productivity.findMany({
      where: {
        repairBudgetId: serviceId,
        proposalId: proposalId,
      },
      select: {
        id: true,
        realPeriodPercentage: true,
      },
    });

    console.log(`CRITICAL FIX: Total de produtividades encontradas para verificação: ${allProductivities.length}`);

    // CRITICAL FIX: Calcular o total de todas as produtividades
    let totalFromAll = 0;
    allProductivities.forEach(p => {
      // Verificar se este registro deve ser excluído do cálculo
      if (excludeId && p.id === excludeId) {
        console.log(`CRITICAL FIX: Excluindo registro ${p.id} do cálculo total`);
        return;
      }

      const realValue = Number(p.realPeriodPercentage) || 0;
      console.log(`CRITICAL FIX: Produtividade ${p.id}: ${realValue}%`);
      totalFromAll += realValue;
    });

    // CRITICAL FIX: Arredondar para 2 casas decimais
    totalFromAll = Math.round(totalFromAll * 100) / 100;
    console.log('CRITICAL FIX: Total calculado a partir de todas as produtividades:', totalFromAll);

    // CRITICAL FIX: Se houver diferença significativa entre os totais, usar o maior
    if (Math.abs(totalFromAll - totalPercentage) > 0.01) {
      console.log('CRITICAL FIX: Diferença significativa entre os totais calculados');
      console.log(`CRITICAL FIX: Diferença: ${Math.abs(totalFromAll - totalPercentage)}`);
      console.log('CRITICAL FIX: Usando o maior valor entre os totais calculados');

      totalPercentage = Math.max(totalFromAll, totalPercentage);
      console.log('CRITICAL FIX: Total final a ser usado:', totalPercentage);
    }

    console.log("Total percentage calculated:", totalPercentage);
    console.log(`Soma de ${formattedRecords.length} registros = ${totalPercentage}%`);

    return NextResponse.json({
      success: true,
      totalPercentage,
      records: formattedRecords,
    });
  } catch (error) {
    console.error("Erro ao calcular percentual total:", error);
    return NextResponse.json(
      { error: "Erro ao calcular percentual total" },
      { status: 500 }
    );
  }
}
