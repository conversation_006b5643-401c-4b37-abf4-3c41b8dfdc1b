import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";

export async function POST(request: Request) {
  try {
    const body = await request.json();

    // Validar os campos obrigatórios
    if (!body.proposalId || !body.content || !body.label) {
      return NextResponse.json(
        { error: "Campos obrigatórios ausentes" },
        { status: 400 }
      );
    }

    // Verificar se o conteúdo está no formato mês-ano (MM-YYYY)
    let content = body.content;

    // Se o conteúdo estiver no formato MM-YYYY, convertê-lo para o formato estruturado
    if (body.content.includes('-') && /^\d{1,2}-\d{4}$/.test(body.content)) {
      const [month, year] = body.content.split('-').map(Number);
      content = `month=${month};year=${year}`;
      console.log(`Converting content from ${body.content} to structured format: ${content}`);
    }

    // Criar um novo PlanningFrequencyItem
    const planningItem = await prisma.planningFrequencyItem.create({
      data: {
        proposalId: body.proposalId,
        content: content, // Usar o conteúdo estruturado
        label: body.label,
        order: body.order || 0,
      },
    });

    return NextResponse.json(planningItem);
  } catch (error) {
    console.error("Erro ao criar PlanningFrequencyItem:", error);
    return NextResponse.json(
      { error: "Erro ao criar item de planejamento" },
      { status: 500 }
    );
  }
}
