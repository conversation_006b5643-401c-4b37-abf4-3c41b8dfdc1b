import KanbanByPeriod from "./components/kanban-by-period";

export default function ProposalsByPeriod() {
	return (
		<section
			className="bg-white p-3 shadow-lg rounded-xl h-auto min-h-[calc(100vh-10rem)] max-w-full"
		>
			<div className="flex flex-col h-full w-full p-2">
				<div className="flex items-center justify-between mb-4 pb-3 border-b">
					<div>
						<h1 className="text-3xl font-semibold text-green-500">
							Propostas por Período
						</h1>
						<p className="text-gray-500 text-sm mt-1">
							Visualize e gerencie propostas por período de criação
						</p>
					</div>
				</div>
				<div className="w-full h-full">
					<KanbanByPeriod />
				</div>
			</div>
		</section>
	);
}
