"use client";

import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from "react";
import { TableGrid } from "@/src/components/ui/table-grid";
import { Proposal } from "@/src/types/core/proposal";
import { useToast } from "@/src/hooks/use-toast";
import { Button } from "@/src/components/ui/button";
import { Eraser } from "lucide-react";

interface ContractsInProgressTableProps {
  columns: any[];
  onPageChange?: (page: number) => void;
}

export type ContractsInProgressTableRef = {
  refresh: (page?: number) => void;
};

const ContractsInProgressTable = forwardRef<ContractsInProgressTableRef, ContractsInProgressTableProps>(
  function ContractsInProgressTable({ columns, onPageChange }, ref) {
    const [data, setData] = useState<Proposal[]>([]);
    const [loading, setLoading] = useState(false);
    const [search, setSearch] = useState("");
    const searchInputRef = useRef<HTMLInputElement>(null);
    const { toast } = useToast();
    const [pagination, setPagination] = useState({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });

    const fetchProposals = useCallback(async (
      page?: number,
      pageSize: number = pagination.pageSize,
      searchTerm: string = search
    ) => {
      const currentPage = page || pagination.page;
      const currentPageSize = Number(pageSize) || 10;

      try {
        setLoading(true);
        const params = new URLSearchParams({
          page: String(currentPage),
          pageSize: String(currentPageSize)
        });

        // Adicionar situações
        ["PROJECT_IN_PROGRESS"].forEach(situation => {
          params.append('situation', situation);
        });

        // Adicionar tipos de serviço
        [
          "CONSULTANCY",
          "PROJECT",
          "FISCALIZACAO",
          "INSPECAO",
          "GERENCIAMENTO",
          "CONSULTORIA"
        ].forEach(serviceType => {
          params.append('serviceType', serviceType);
        });

        if (searchTerm) {
          params.append('search', searchTerm);
        }

        const url = `/api/proposals?${params}`;
        console.log("URL da requisição:", url);

        const response = await fetch(url);
        console.log("Status da resposta:", response.status);

        if (!response.ok) throw new Error("Failed to fetch proposals");

        const result = await response.json();
        console.log("Dados recebidos:", result);

        setData(Array.isArray(result.items) ? result.items : []);
        setPagination(prev => ({
          ...prev,
          page: Number(result.page) || 1,
          pageSize: Number(result.pageSize) || 10,
          total: Number(result.total) || 0,
          totalPages: Math.ceil((Number(result.total) || 0) / (Number(result.pageSize) || 10))
        }));

      } catch (error) {
        console.error('Fetch error:', error);
        toast({
          title: "Erro",
          description: "Erro ao carregar contratos em andamento",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }, [pagination.pageSize, search, toast]);

    useImperativeHandle(ref, () => ({
      refresh: (page?: number) => fetchProposals(page)
    }), [fetchProposals]);

    // Carregar dados quando o componente for montado
    useEffect(() => {
      fetchProposals(1);
    }, []);

    // Escutar o evento refreshContracts para atualizar a tabela na página correta
    useEffect(() => {
      const handleRefresh = (event: any) => {
        const { page } = event.detail || {};
        if (page) {
          fetchProposals(page);
        }
      };

      window.addEventListener('refreshContracts', handleRefresh);

      return () => {
        window.removeEventListener('refreshContracts', handleRefresh);
      };
    }, [fetchProposals]);

    const handleSearch = (value: string) => {
      setSearch(value);
      fetchProposals(1, pagination.pageSize, value);
    };

    const handleClearFilters = () => {
      // Limpar o estado de pesquisa
      setSearch("");

      // Resetar para a primeira página e buscar dados
      fetchProposals(1, pagination.pageSize, "");

      // Atualizar a página atual no componente pai
      if (onPageChange) onPageChange(1);

      // Focar no campo de pesquisa após limpar
      setTimeout(() => {
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }, 0);
    };

    return (
      <TableGrid
        columns={columns}
        data={data}
        loading={loading}
        pagination={pagination}
        onPaginationChange={(newPage, newPageSize) => {
          fetchProposals(newPage, newPageSize);
          if (onPageChange) onPageChange(newPage);
        }}
        onSearch={handleSearch}
        searchValue={search}
        searchInputRef={searchInputRef}
        buttonsTemplate={
          <>
            <Button
              className="w-full sm:w-auto bg-blue-500 hover:bg-blue-400"
              onClick={handleClearFilters}
            >
              Limpar filtros <Eraser className="ml-2 h-4 w-4" />
            </Button>
          </>
        }
      />
    );
  }
);

export default ContractsInProgressTable;
