import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/src/providers/auth";
import nodemailer from "nodemailer";
import {
  getProposalEmailTemplate,
  getReportEmailTemplate,
} from "@/src/utils/email-templates";
import { convertDocxToPdfBuffer } from "@/src/helpers/convertDocxToPdf";

// Função para verificar se o usuário está autenticado
async function isAuthenticated() {
  const session = await auth();
  return !!session?.user;
}

// Configuração do transporte de email
const createTransporter = () => {
  const host = process.env.EMAIL_HOST;
  const port = parseInt(process.env.EMAIL_PORT || "587", 10);
  const user = process.env.EMAIL_USER;
  const pass = process.env.EMAIL_PASS;
  const from = process.env.EMAIL_FROM;

  if (!host || !user || !pass || !from) {
    throw new Error("Configurações de email incompletas no arquivo .env");
  }

  return nodemailer.createTransport({
    host,
    port,
    secure: port === 465, // true para 465, false para outras portas
    auth: {
      user,
      pass,
    },
  });
};

export async function POST(request: NextRequest) {
  console.log("=== INÍCIO DO PROCESSAMENTO DE ENVIO DE EMAIL ===");
  try {
    // Verificar autenticação
    console.log("Verificando autenticação...");
    const authenticated = await isAuthenticated();
    if (!authenticated) {
      console.log("Erro: Usuário não autenticado");
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }
    console.log("Autenticação verificada com sucesso");

    // Obter dados do formulário
    console.log("Obtendo dados do formulário...");
    const formData = await request.formData();
    const type = formData.get("type") as string;
    console.log("Tipo de email:", type);

    // Verificar o tipo de email
    if (type === "proposal" || type === "contract" || type === "report") {
      const to = formData.get("to") as string;
      const proposalName = formData.get("proposalName") as string;
      const proposalLink = formData.get("proposalLink") as string;
      const customerName = formData.get("customerName") as string;
      const file = formData.get("file") as File;
      const isContract =
        type === "contract" || formData.get("isContract") === "true";
      const isReport = type === "report";
      const reportType =
        (formData.get("reportType") as "inspection" | "project") ||
        "inspection";

      console.log("Dados recebidos:", {
        to,
        proposalName,
        proposalLink,
        customerName,
        file: file
          ? { name: file.name, type: file.type, size: file.size }
          : "Nenhum arquivo",
      });

      if (!to || !proposalName || !proposalLink || !customerName || !file) {
        console.log("Erro: Parâmetros incompletos", {
          to,
          proposalName,
          proposalLink,
          customerName,
          file: !!file,
        });
        return NextResponse.json(
          { error: "Parâmetros incompletos" },
          { status: 400 }
        );
      }

      // Converter o arquivo para buffer
      console.log("Convertendo arquivo para buffer...");
      console.log("Informações do arquivo:", {
        name: file.name,
        type: file.type,
        size: file.size,
        lastModified: new Date(file.lastModified).toISOString(),
        extension: file.name.split(".").pop()?.toLowerCase(),
      });

      // Verificar se o arquivo é um DOCX
      const isDocx =
        file.type ===
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
      const extension = file.name.split(".").pop()?.toLowerCase();

      if (!isDocx) {
        console.warn(
          "Aviso: O arquivo não tem o tipo MIME de um DOCX. Tipo atual:",
          file.type
        );
        console.warn(
          "Continuando o processamento, mas isso pode causar problemas no email."
        );
      }

      if (extension !== "docx") {
        console.warn(
          "Aviso: A extensão do arquivo não é .docx. Extensão atual:",
          extension
        );
        console.warn(
          "Continuando o processamento, mas isso pode causar problemas no email."
        );
      }

      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      console.log(
        "Arquivo convertido para buffer com sucesso. Tamanho:",
        buffer.length,
        "bytes"
      );

      // --- INÍCIO DA CONVERSÃO DOCX -> PDF (LibreOffice local) ---
      let pdfBuffer: Buffer | null = null;
      const tipoDoc = isContract ? "Contrato" : "Proposta";
      const pdfFileName = `${proposalName} - ${tipoDoc}.pdf`;
      if (
        (type === "proposal" || type === "contract") &&
        (file.type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
         file.name.endsWith(".docx"))
      ) {
        try {
          pdfBuffer = await convertDocxToPdfBuffer(buffer);
          console.log(`[CONVERSÃO] PDF convertido com sucesso via LibreOffice. Tamanho: ${pdfBuffer.length} bytes.`);
        } catch (err) {
          console.error('[CONVERSÃO] Erro ao converter DOCX para PDF via LibreOffice:', err);
          return NextResponse.json({ error: 'Erro ao converter DOCX para PDF' }, { status: 500 });
        }
      } else {
        console.log('[CONVERSÃO] Arquivo não é DOCX ou não é proposta/contrato. Enviando anexo original.');
      }
      // --- FIM DA CONVERSÃO ---

      // Criar o transporter
      console.log("Criando transporter de email...");
      console.log("Configurações de email:", {
        host: process.env.EMAIL_HOST,
        port: process.env.EMAIL_PORT,
        user: process.env.EMAIL_USER ? "***" : "não definido", // Ocultar senha por segurança
        from: process.env.EMAIL_FROM || "<EMAIL>",
      });

      const transporter = createTransporter();
      console.log("Transporter criado com sucesso");

      // Verificar se é um contrato ou uma proposta (já definido acima)
      console.log("Tipo de documento:", isContract ? "Contrato" : "Proposta");

      // Definir o assunto do email com base no tipo de documento
      const subject = isContract
        ? `Contrato: ${proposalName}`
        : `Proposta Comercial: ${proposalName}`;
      console.log("Assunto do email:", subject);

      // Verificar se o link da proposta é válido
      console.log("Link do documento recebido:", proposalLink);

      // Verificar se o link contém '/document-editor/' (formato correto)
      if (!proposalLink.includes("/document-editor/")) {
        console.warn(
          'Aviso: O link do documento não contém "/document-editor/". Formato possivelmente incorreto.'
        );
      }

      // Usar o template de email apropriado
      let html;
      if (isReport) {
        html = getReportEmailTemplate(
          customerName,
          proposalName,
          proposalLink,
          reportType
        );
        console.log("Template de email de relatório criado com sucesso");
      } else {
        html = getProposalEmailTemplate(
          customerName,
          proposalName,
          "", // Não enviar o link para proposta/contrato
          isContract
        );
        console.log(
          "Template de email de proposta/contrato criado com sucesso"
        );
      }

      console.log("Template de email criado com sucesso");
      console.log("Preparando para enviar email...");

      // Configuração do email
      console.log('[EMAIL] Preparando anexo para envio...');
      const mailOptions = {
        from: {
          name: "Administração de Contratos Ageu",
          address: process.env.EMAIL_FROM || "<EMAIL>",
        },
        to,
        subject,
        html,
        text: html.replace(/<[^>]*>/g, ""), // Versão texto plano
        attachments: [
          pdfBuffer
            ? {
                filename: pdfFileName,
                content: pdfBuffer,
                contentType: 'application/pdf',
              }
            : {
                filename: file.name,
                content: buffer,
                contentType: file.type,
              },
        ],
        replyTo: "<EMAIL>",
      };

      console.log("Configurações do email preparadas:", {
        from: mailOptions.from,
        to: mailOptions.to,
        subject: mailOptions.subject,
        attachments: mailOptions.attachments.map((a) => ({
          filename: a.filename,
          contentType: a.contentType,
          size: a.content.length,
          extension: a.filename.split(".").pop()?.toLowerCase(),
        })),
      });

      // Verificar se o tipo de conteúdo do anexo é DOCX
      const attachment = mailOptions.attachments[0];
      if (attachment) {
        const isDocx =
          attachment.contentType ===
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        const extension = attachment.filename.split(".").pop()?.toLowerCase();

        console.log("Verificação do anexo:", {
          isDocx,
          contentType: attachment.contentType,
          extension,
          filename: attachment.filename,
          size: attachment.content.length,
        });

        if (!isDocx) {
          console.warn(
            "Aviso: O anexo não tem o tipo MIME de um arquivo DOCX!"
          );
        }

        if (extension !== "docx") {
          console.warn("Aviso: A extensão do arquivo não é .docx!");
        }
      } else {
        console.error("Erro: Nenhum anexo encontrado nas opções de email!");
      }

      // Enviar o email
      console.log('[EMAIL] Enviando e-mail...');
      try {
        const info = await transporter.sendMail(mailOptions);
        console.log('[EMAIL] E-mail enviado com sucesso!');
        console.log("Detalhes do envio:", {
          messageId: info.messageId,
          response: info.response,
          accepted: info.accepted,
          rejected: info.rejected,
        });
        return NextResponse.json({
          success: true,
          messageId: info.messageId,
          accepted: info.accepted,
          rejected: info.rejected,
        });
      } catch (emailError: unknown) {
        console.error("Erro específico ao enviar email:", emailError);
        const typedError = emailError as {
          name?: string;
          message?: string;
          code?: string;
          command?: string;
          stack?: string;
        };
        console.error("Detalhes do erro:", {
          name: typedError.name,
          message: typedError.message,
          code: typedError.code,
          command: typedError.command,
          stack: typedError.stack,
        });
        return NextResponse.json(
          {
            error: "Falha ao enviar email",
            details: {
              message:
                (emailError as { message?: string }).message ||
                "Erro desconhecido",
              code: (emailError as { code?: string }).code,
            },
          },
          { status: 500 }
        );
      }
    } else {
      console.log(
        `Erro: Tipo de email não suportado: ${type}. Tipos suportados: 'proposal', 'contract' e 'report'`
      );
      return NextResponse.json(
        {
          error: `Tipo de email não suportado: ${type}. Tipos suportados: 'proposal', 'contract' e 'report'`,
        },
        { status: 400 }
      );
    }
  } catch (error: unknown) {
    console.error('[ERRO GERAL]', error);
    console.error("=== ERRO GERAL AO PROCESSAR REQUISIÇÃO DE EMAIL ===");
    console.error("Erro ao processar requisição de email com anexo:", error);
    const typedError = error as {
      name?: string;
      message?: string;
      stack?: string;
    };
    console.error("Detalhes do erro:", {
      name: typedError.name,
      message: typedError.message,
      stack: typedError.stack,
    });
    return NextResponse.json(
      {
        error: "Erro interno do servidor",
        details: (error as { message?: string }).message || "Erro desconhecido",
      },
      { status: 500 }
    );
  } finally {
    console.log("=== FIM DO PROCESSAMENTO DE ENVIO DE EMAIL ===");
  }
}
