"use client";

import { <PERSON><PERSON> } from "@/src/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog";
import { Checkbox } from "@/src/components/ui/checkbox";
import { Label } from "@/src/components/ui/label";
import { useState } from "react";
import { DateRange } from "react-day-picker";
import { exportKpiToExcel } from "@/src/actions/kpi";
import { useToast } from "@/src/hooks/use-toast";
import { FileSpreadsheet, Loader2 } from "lucide-react";
import { generateExcelFile } from "@/src/lib/generate-excel";
import { downloadBlob } from "@/src/lib/excel-utils";

interface ExportReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  dateRange?: DateRange;
}

export function ExportReportDialog({
  open,
  onOpenChange,
  dateRange,
}: ExportReportDialogProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [selectedReports, setSelectedReports] = useState({
    revenue: true,
    squareMeter: true,
    conversionRate: true,
    contractsCount: true,
    statusDistribution: true,
  });

  const handleExport = async () => {
    try {
      setLoading(true);

      // Chamar a API para exportar os dados
      const result = await exportKpiToExcel(
        dateRange?.from,
        dateRange?.to,
        selectedReports
      );

      if (result.success && result.data) {
        // Gerar o arquivo Excel no lado do cliente
        const excelBlob = await generateExcelFile(result.data, selectedReports);

        // Criar nome do arquivo com data atual
        const today = new Date();
        const fileName = `indicadores_${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}.xlsx`;

        // Fazer o download do arquivo
        downloadBlob(excelBlob, fileName);

        toast({
          title: "Sucesso",
          description: "Relatório exportado com sucesso!",
          variant: "default",
          duration: 3000 // 3 segundos em vez do padrão
        });
        onOpenChange(false);
      } else {
        throw new Error("Falha ao gerar dados para o relatório");
      }
    } catch (error) {
      console.error("Erro ao exportar relatório:", error);
      toast({
        title: "Erro",
        description: "Erro ao exportar relatório. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-green-700 flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5" />
            Exportar Relatório
          </DialogTitle>
          <DialogDescription className="text-green-600">
            Selecione os indicadores que deseja incluir no relatório Excel.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="bg-green-50 p-4 rounded-lg border border-green-100 mb-4">
            <h3 className="font-medium text-green-800 mb-2">Período do relatório</h3>
            <p className="text-sm text-green-700">
              {dateRange?.from && dateRange?.to
                ? `${dateRange.from.toLocaleDateString()} a ${dateRange.to.toLocaleDateString()}`
                : "Todo o período disponível"}
            </p>
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="revenue"
                checked={selectedReports.revenue}
                onCheckedChange={(checked) =>
                  setSelectedReports({ ...selectedReports, revenue: !!checked })
                }
                className="border-green-400 data-[state=checked]:bg-green-600 data-[state=checked]:text-white"
              />
              <Label htmlFor="revenue" className="font-medium">
                Faturamento Mensal
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="squareMeter"
                checked={selectedReports.squareMeter}
                onCheckedChange={(checked) =>
                  setSelectedReports({ ...selectedReports, squareMeter: !!checked })
                }
                className="border-green-400 data-[state=checked]:bg-green-600 data-[state=checked]:text-white"
              />
              <Label htmlFor="squareMeter" className="font-medium">
                Valor por Metro Quadrado
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="conversionRate"
                checked={selectedReports.conversionRate}
                onCheckedChange={(checked) =>
                  setSelectedReports({
                    ...selectedReports,
                    conversionRate: !!checked,
                  })
                }
                className="border-green-400 data-[state=checked]:bg-green-600 data-[state=checked]:text-white"
              />
              <Label htmlFor="conversionRate" className="font-medium">
                Taxa de Conversão de Propostas
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="contractsCount"
                checked={selectedReports.contractsCount}
                onCheckedChange={(checked) =>
                  setSelectedReports({
                    ...selectedReports,
                    contractsCount: !!checked,
                  })
                }
                className="border-green-400 data-[state=checked]:bg-green-600 data-[state=checked]:text-white"
              />
              <Label htmlFor="contractsCount" className="font-medium">
                Quantidade de Contratos
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="statusDistribution"
                checked={selectedReports.statusDistribution}
                onCheckedChange={(checked) =>
                  setSelectedReports({
                    ...selectedReports,
                    statusDistribution: !!checked,
                  })
                }
                className="border-green-400 data-[state=checked]:bg-green-600 data-[state=checked]:text-white"
              />
              <Label htmlFor="statusDistribution" className="font-medium">
                Distribuição por Status
              </Label>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="border-gray-300"
          >
            Cancelar
          </Button>
          <Button
            onClick={handleExport}
            disabled={loading || !Object.values(selectedReports).some(Boolean)}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Exportando...
              </>
            ) : (
              "Exportar Excel"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
