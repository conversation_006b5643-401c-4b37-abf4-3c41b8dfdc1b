"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Building2, ChevronDown } from "lucide-react";
import { useEffect, useState } from "react";
import { setMemberOrganization } from "../actions/membership";
import { loadOrganizations } from "../actions/organization";
import { toast } from "../hooks/use-toast";

interface Organization {
  id: string;
  name: string;
}

interface OrganizationSwitcherProps {
  currentOrganizationId: string;
  currentOrganizationName: string;
  isCollapsed?: boolean;
}

export function OrganizationSwitcher({
  currentOrganizationId,
  currentOrganizationName,
  isCollapsed = false,
}: OrganizationSwitcherProps) {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchOrganizations();
  }, []);

  const fetchOrganizations = async () => {
    try {
      const data = await loadOrganizations();
      if (data) {
        setOrganizations(data);
      }
    } catch (error) {
      console.error("Error loading organizations:", error);
    }
  };

  const handleOrganizationSwitch = async (organizationId: string) => {
    try {
      setLoading(true);
      await setMemberOrganization(organizationId);
      toast({
        title: "Sucesso",
        description: "Filial alterada com sucesso!",
      });
      setTimeout(() => {
        window.location.reload();
      }, 100);
    } catch (error) {
      console.error("Error switching organization:", error);
      toast({
        title: "Erro",
        description: "Não foi possível trocar de organização",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (isCollapsed) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="w-full flex justify-center items-center p-2"
            disabled={loading}
            title="Trocar filial"
          >
            <Building2 className="h-5 w-5 text-gray-600 hover:text-green-500 transition-colors" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-[200px]" align="start" sideOffset={5}>
          <DropdownMenuLabel>Trocar filial</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem disabled className="opacity-75">
            <span className="text-xs">Filial atual:</span>
          </DropdownMenuItem>
          <DropdownMenuItem disabled className="font-medium text-green-600 mb-1">
            {currentOrganizationName}
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          {organizations.map((org) => (
            <DropdownMenuItem
              key={org.id}
              className={org.id === currentOrganizationId ? "bg-gray-100" : ""}
              onClick={() =>
                org.id !== currentOrganizationId &&
                handleOrganizationSwitch(org.id)
              }
            >
              <span className="flex-1">{org.name}</span>
              {org.id === currentOrganizationId && (
                <span className="text-xs text-green-500">Atual</span>
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="w-full justify-start gap-2 px-2"
          disabled={loading}
        >
          <Building2 className="h-4 w-4" />
          <span className="flex-1 text-left text-sm truncate">
            {currentOrganizationName}
          </span>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-[200px]" align="start">
        <DropdownMenuLabel>Trocar filial</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {organizations.map((org) => (
          <DropdownMenuItem
            key={org.id}
            className={org.id === currentOrganizationId ? "bg-gray-100" : ""}
            onClick={() =>
              org.id !== currentOrganizationId &&
              handleOrganizationSwitch(org.id)
            }
          >
            <span className="flex-1">{org.name}</span>
            {org.id === currentOrganizationId && (
              <span className="text-xs text-green-500">Atual</span>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
