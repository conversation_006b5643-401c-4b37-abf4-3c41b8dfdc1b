{"name": "ageu", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next dev", "lint": "next lint", "deploy": "git pull && yarn && yarn prisma db push && yarn build && pm2 restart ageu", "seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.693.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@hello-pangea/dnd": "^17.0.0", "@hookform/resolvers": "^3.9.1", "@onlyoffice/document-editor-react": "^1.5.1", "@prisma/client": "^5.21.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-table": "^8.20.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^3.6.0", "docxtemplater": "^3.59.0", "docxtemplater-image-module-free": "^1.1.1", "exceljs": "^4.4.0", "framer-motion": "^11.11.10", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "libphonenumber-js": "^1.11.12", "lucide-react": "^0.462.0", "next": "14.2.15", "next-auth": "^5.0.0-beta.25", "nodemailer": "^6.10.0", "pizzip": "^3.1.8", "quill": "^2.0.2", "react": "^18", "react-datepicker": "^7.5.0", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-dropzone": "^14.3.5", "react-hook-form": "^7.53.1", "react-imask": "^7.6.1", "react-number-format": "^5.4.3", "react-phone-number-input": "^3.4.9", "react-quill": "^2.0.0", "recharts": "^2.13.3", "sharp": "^0.33.5", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "xlsx-template": "^1.4.4", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^9.0.8", "eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8", "prisma": "^5.21.1", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5"}}