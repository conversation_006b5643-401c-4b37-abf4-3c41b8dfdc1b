import { Toaster } from "@/components/ui/toaster";
import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
// import { Inter, Zen_Kaku_Gothic_Antique } from "next/font/google";
import Loading from "@/src/components/app-loading";
import { Suspense } from "react";
import "./globals.css";
import ViewportFix from "./viewport-fix";

const fontSans = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: "cover"
};

export const metadata: Metadata = {
  title: "Ageu",
  description: "Ageu CRM"
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${fontSans.variable} custom-scrollbar`}
      // className={`${fontSans.variable} ${zenKakyGothicAntiqueFont.variable} antialiased`}
      >
        <ViewportFix />
        <Suspense fallback={<Loading />}>{children}</Suspense>
        <Toaster />
      </body>
    </html>
  );
}
