-- Adicionar restrição de unicidade para garantir que cada combinação de proposalId, repairBudgetId e periodicityId seja única
ALTER TABLE "Productivity" ADD CONSTRAINT "Productivity_proposalId_repairBudgetId_periodicityId_unique" UNIQUE ("proposalId", "repairBudgetId", "periodicityId");

-- <PERSON><PERSON>r índices para melhorar o desempenho das consultas
CREATE INDEX "Productivity_proposalId_repairBudgetId_idx" ON "Productivity"("proposalId", "repairBudgetId");
CREATE INDEX "Productivity_periodicityId_idx" ON "Productivity"("periodicityId");
