import { parseCurrencyToNumber } from "@/src/lib/utils";
import { z } from "zod";

export const repairBudgetSchema = z
  .object({
    id: z.string().optional(),
    proposalId: z.string(),
    serviceScopeId: z
      .string({ required_error: "Selecione um serviço" })
      .refine((value) => value.trim().length, "Selecione um serviço"),
    measurementDate: z
      .date()
      .transform((val) => new Date(val))
      .optional(),
    gravity: z
      .string({ required_error: "Preencha o campo de Gravidade" })
      .or(z.number({ required_error: "Preencha o campo de Gravidade" }))
      .refine(
        (val) => Number(val) > 0 && Number(val) <= 5,
        "Gravidade deve estar entre 1 e 5"
      ),
    urgency: z
      .string({ required_error: "Preencha o campo de Urgência" })
      .or(z.number({ required_error: "Preencha o campo de Urgência" }))
      .refine(
        (val) => Number(val) > 0 && Number(val) <= 5,
        "Urgência deve estar entre 1 e 5"
      ),
    tendency: z
      .string({ required_error: "Preencha o campo de Tendência" })
      .or(z.number({ required_error: "Preencha o campo de Tendência" }))
      .refine(
        (val) => Number(val) > 0 && Number(val) <= 5,
        "Tendência deve estar entre 1 e 5"
      ),
    gut: z.number().or(z.string()),
    imp: z.string().or(z.number()).optional(),
    serviceCost: z
      .string({ required_error: "Informe o custo do serviço" })
      .or(z.number({ required_error: "Informe o custo do serviço" }))
      .transform((val) => Number(val).toFixed(2)),
    totalCost: z
      .string({ required_error: "Informe o custo total da obra" })
      .or(z.number({ required_error: "Informe o custo total da obra" }))
      .transform((val) => {
        const number = parseCurrencyToNumber(val.toString());
        // Só valida se o valor for maior que zero quando for salvar o formulário
        // Isso evita o erro durante a inicialização do formulário
        return number.toFixed(2);
      })
      .refine((val) => Number(val) > 0, {
        message: "Custo total deve ser maior que 0",
      }),
    financialWeight: z
      .string()
      .or(z.number())
      .transform((val) => Number(val).toFixed(2)),
    igrf: z
      .string()
      .or(z.number())
      .transform((val) => Number(val).toFixed(2)),
    startDate: z.date().transform((val) => new Date(val)),
    endDate: z.date().transform((val) => new Date(val)),
    laborAmount: z.string().or(z.number()).optional(),
    equipmentAmount: z.string().or(z.number()).optional(),
    equipmentDescription: z.string().optional(),
    buildingPercentage: z.string().or(z.number()).optional(),
  })
  .refine(
    (data) => {
      // Verificar se ambas as datas estão preenchidas antes de comparar
      if (!data.startDate || !data.endDate) {
        return true; // Não validar se alguma das datas estiver vazia
      }

      const startDate = new Date(data.startDate);
      const endDate = new Date(data.endDate);

      // Verificar se as datas são válidas antes de comparar
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return true; // Não validar se alguma das datas for inválida
      }

      // Verifica se a data de início é anterior à data de fim
      return startDate <= endDate;
    },
    {
      message: "A data de início deve ser anterior ou igual à data de término",
      path: ["startDate"], // Erro associado ao campo startDate
    }
  )
  .refine(
    (data) => {
      // Verificar se o custo total da obra é maior ou igual ao custo do serviço
      const totalCost = parseCurrencyToNumber(data.totalCost.toString());
      const serviceCost = parseCurrencyToNumber(data.serviceCost.toString());

      return totalCost >= serviceCost;
    },
    {
      message:
        "O valor do Custo do serviço é maior que o Custo Total da Obra, corrija para salvar.",
      path: ["totalCost"], // Erro associado ao campo totalCost
    }
  );

export type RepairBudgetSchema = z.infer<typeof repairBudgetSchema>;
