"use client";
import {

  removeReportTemplate,
  saveReportTemplate,
} from "@/src/actions/report-template";
import ContentWrapper from "@/src/components/content-wrapper";
import FileUpload from "@/src/components/file-upload";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog";
import { reportTypes } from "@/src/constants";

import { useToast } from "@/src/hooks/use-toast";
import { constructActionColumn, contructColumn } from "@/src/lib/table/columns";
import { ReportTemplateInterface } from "@/src/types/core/report-template";
import { FileText } from "lucide-react";
import { useRouter } from "next/navigation";
import { useRef, useState } from "react";
import ReportTemplatesTable, { ReportTemplatesTableRef } from "./components/report-templates-table";

export default function ReportTemplates() {
  const { toast } = useToast();
  const router = useRouter();
  const [openUploadDialog, setOpenUploadDialog] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<ReportTemplateInterface | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const tableRef = useRef<ReportTemplatesTableRef>(null);



  const deletereportTemplates = async (id: string) => {
    try {
      const data = await removeReportTemplate(id);
      if (data) {
        toast({
          title: "Sucesso",
          description: data.message,
          variant: "default"
        });
        // Recarregar a tabela mantendo a página atual
        tableRef.current?.refresh(currentPage);
      }
    } catch (error) {
      console.error(error);
      toast({
        title: "Erro",
        description: "Erro ao excluir template",
        variant: "destructive"
      });
    }
  };

  const columns = [
    contructColumn("title", "Titulo"),
    contructColumn("description", "Descrição"),
    contructColumn("type", "Tipo", (row: any) => {
      const typeKey = row.type;
      const typeLabel = reportTypes.find((r) => r.value == typeKey);
      return typeLabel?.label || "";
    }),
    contructColumn("createdAt", "Criado em", (row: any) =>
      new Date(row.createdAt).toLocaleDateString("pt-BR")
    ),
    constructActionColumn<ReportTemplateInterface>([
      {
        action: "view",
        customIcon: FileText,
        customIconClass: "text-amber-500",
        callback: ({ fileEditorId }) => {
          return router.push(`/document-editor/${fileEditorId}`);
        },
      },
      {
        action: "edit",
        callback: (template) => {
          setEditingTemplate(template); // Definir o template que será editado
          setOpenUploadDialog(true);
        },
      },
      {
        action: "delete",
        dialogTitle: "Deseja realmente excluir o template?",
        dialogDescription: "Esta operação não poderá ser desfeita",
        callback: ({ id }) => deletereportTemplates(id!),
      },
    ]),
  ];



  function submitFormData(formData: FormData, id?: string) {
    if (id) {
      formData.append("id", id);
    }
    return saveReportTemplate(formData);
  }

  return (
    <>
      <ContentWrapper title="Templates de relatórios">
        <ReportTemplatesTable
          ref={tableRef}
          columns={columns}
          onAddClick={() => {
            setOpenUploadDialog(true);
          }}
          onPageChange={(page) => setCurrentPage(page)}
        />
      </ContentWrapper>
      <Dialog
        open={openUploadDialog}
        onOpenChange={(open) => {
          setOpenUploadDialog(open);
          if (!open) {
            setEditingTemplate(null);
            // Recarregar a tabela mantendo a página atual
            tableRef.current?.refresh(currentPage);
          }
        }}
      >
        <DialogContent className="rounded-lg">
          <DialogHeader>
            <DialogTitle className="font-bold text-xl">
              Template de relatório
            </DialogTitle>
            <DialogDescription>Enviar template de relatório</DialogDescription>
            <FileUpload
              submitFormData={(data) => submitFormData(data, editingTemplate?.id)}
              fileTypes={[{ label: 'Histograma', value: 'HISTOGRAM' }]}
              defaultFileType={editingTemplate?.reportType || "HISTOGRAM"}
              onUploadSuccess={() => {
                setOpenUploadDialog(false);
                setEditingTemplate(null);
                tableRef.current?.refresh(currentPage);
              }}
              editingTemplate={editingTemplate}
            />
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </>
  );
}
