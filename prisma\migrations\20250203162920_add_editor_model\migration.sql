-- AlterEnum
ALTER TYPE "Periodicity" ADD VALUE 'NONE';

-- AlterTable
ALTER TABLE "Proposal" ADD COLUMN     "methodology" TEXT[];

-- CreateTable
CREATE TABLE "FileEditor" (
    "id" TEXT NOT NULL,
    "filename" TEXT NOT NULL,
    "mimetype" TEXT NOT NULL,
    "size" INTEGER NOT NULL,
    "key" TEXT NOT NULL,
    "bucket" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FileEditor_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "FileEditor_key_key" ON "FileEditor"("key");
