"use client";

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/src/components/ui/card";
import { formatCurrency, formatDate } from "@/src/lib/utils";
import { Proposal } from "@/src/types/core/proposal";
import { MailIcon, PhoneIcon, UserCircle, Calendar as CalendarIcon, CalendarCheck, Ruler } from "lucide-react";

interface ProposalCardProps {
  proposal: Proposal;
  onClick?: () => void;
  isLoading?: boolean;
  columnType?: string;
}

export default function ProposalCard({ proposal, onClick, isLoading, columnType = 'NEW' }: ProposalCardProps) {
  // Cálculo dos dias restantes para o início do projeto
  // const today = new Date();
  // const startDate = new Date(proposal.startDate || today);
  // const timeDifference = startDate.getTime() - today.getTime();
  // const remainingDays = Math.ceil(timeDifference / (1000 * 3600 * 24));

  // Determinar as cores do card com base no tipo de coluna
  const getCardStyle = () => {
    switch (columnType) {
      case 'NEW': return {
        bg: 'bg-blue-50',
        hoverBg: 'hover:bg-blue-100',
        border: 'border-blue-200',
        shadow: 'shadow-blue-100',
        accent: 'bg-blue-500',
        text: 'text-blue-700',
        badge: 'status-badge-new'
      };
      case 'UNDER_ANALYSIS': return {
        bg: 'bg-blue-50',
        hoverBg: 'hover:bg-blue-100',
        border: 'border-blue-200',
        shadow: 'shadow-blue-100',
        accent: 'bg-blue-500',
        text: 'text-blue-700',
        badge: 'status-badge-analysis'
      };
      case 'PROPOSAL_SENT': return {
        bg: 'bg-blue-50',
        hoverBg: 'hover:bg-blue-100',
        border: 'border-blue-200',
        shadow: 'shadow-blue-100',
        accent: 'bg-blue-500',
        text: 'text-blue-700',
        badge: 'status-badge-sent'
      };
      case 'PROPOSAL_ACCEPTED': return {
        bg: 'bg-blue-50',
        hoverBg: 'hover:bg-blue-100',
        border: 'border-blue-200',
        shadow: 'shadow-blue-100',
        accent: 'bg-blue-500',
        text: 'text-blue-700',
        badge: 'status-badge-accepted'
      };
      case 'SIGN_REQUESTED': return {
        bg: 'bg-greenLogo-50',
        hoverBg: 'hover:bg-greenLogo-100',
        border: 'border-greenLogo-200',
        shadow: 'shadow-greenLogo-100',
        accent: 'bg-greenLogo-500',
        text: 'text-greenLogo-600',
        badge: 'status-badge-sign'
      };
      case 'SIGNED': return {
        bg: 'bg-greenLogo-50',
        hoverBg: 'hover:bg-greenLogo-100',
        border: 'border-greenLogo-200',
        shadow: 'shadow-greenLogo-100',
        accent: 'bg-greenLogo-500',
        text: 'text-greenLogo-600',
        badge: 'status-badge-signed'
      };
      case 'PROJECT_IN_PROGRESS': return {
        bg: 'bg-greenLogo-50',
        hoverBg: 'hover:bg-greenLogo-100',
        border: 'border-greenLogo-200',
        shadow: 'shadow-greenLogo-100',
        accent: 'bg-greenLogo-500',
        text: 'text-greenLogo-600',
        badge: 'status-badge-in-progress'
      };
      case 'PROJECT_FINISHED': return {
        bg: 'bg-greenLogo-50',
        hoverBg: 'hover:bg-greenLogo-100',
        border: 'border-greenLogo-200',
        shadow: 'shadow-greenLogo-100',
        accent: 'bg-greenLogo-500',
        text: 'text-greenLogo-600',
        badge: 'status-badge-finished'
      };
      case 'LOST': return {
        bg: 'bg-red-50',
        hoverBg: 'hover:bg-red-100',
        border: 'border-red-200',
        shadow: 'shadow-red-100',
        accent: 'bg-red-500',
        text: 'text-red-600',
        badge: 'status-badge-lost'
      };
      default: return {
        bg: 'bg-greenLogo-50',
        hoverBg: 'hover:bg-greenLogo-100',
        border: 'border-greenLogo-200',
        shadow: 'shadow-greenLogo-100',
        accent: 'bg-greenLogo-500',
        text: 'text-greenLogo-600',
        badge: ''
      };
    }
  };

  const cardStyle = getCardStyle();

  return (
    <>
      <Card
        onClick={() => !isLoading && onClick && onClick()}
        className={`card-enter card-glow rounded-xl p-3 md:p-4 ${cardStyle.bg}
        ${!isLoading
            ? `${cardStyle.hoverBg} cursor-pointer`
            : 'cursor-not-allowed opacity-70'
          }
        border ${cardStyle.border}
        shadow-md hover:shadow-xl ${cardStyle.shadow}
        transition-all duration-300 ease-in-out hover:translate-y-[-3px]
        w-full min-w-[200px] max-w-full sm:max-w-[225px] overflow-hidden
        block`}
      >
        <div className="relative">
          {/* Indicador de status */}
          <div className={`absolute -right-1 -top-2.5 ${cardStyle.badge} status-badge z-10`}>
            {columnType === 'NEW' && 'Nova'}
            {columnType === 'UNDER_ANALYSIS' && 'Em análise'}
            {columnType === 'PROPOSAL_SENT' && 'Enviada'}
            {columnType === 'PROPOSAL_ACCEPTED' && 'Aceita'}
            {columnType === 'SIGN_REQUESTED' && 'Assinar'}
            {columnType === 'SIGNED' && 'Assinada'}
            {columnType === 'PROJECT_IN_PROGRESS' && 'Em andamento'}
            {columnType === 'PROJECT_FINISHED' && 'Concluído'}
            {columnType === 'LOST' && 'Perdido'}
          </div>

          {/* Barra de acento colorida */}
          <div className={`h-1 w-16 ${cardStyle.accent} rounded-full mb-3`}></div>

          <CardHeader className="p-0 mb-2">
            <CardTitle className="flex flex-col gap-2">
              <span className="text-lg font-semibold tracking-wide truncate max-w-full sm:max-w-[190px]">{proposal.name || 'Sem nome'}</span>
              <div className="flex flex-col gap-1">
                <div className="flex items-center gap-1">
                  <CalendarIcon className={`h-3 w-3 ${cardStyle.text}`} />
                  <small className={`${cardStyle.text} text-xs`}>
                    <span className="font-medium">Início:</span> {proposal.startDate ? formatDate(proposal.startDate) : 'Não informada'}
                  </small>
                </div>

                <div className="flex items-center gap-1">
                  <CalendarCheck className={`h-3 w-3 ${cardStyle.text}`} />
                  <small className={`${cardStyle.text} text-xs`}>
                    <span className="font-medium">Fim:</span> {proposal.endDate ? formatDate(proposal.endDate) : 'Não informada'}
                  </small>
                </div>
              </div>
            </CardTitle>
          </CardHeader>

          <CardContent className="p-0 mb-3">
            <div className="flex flex-col gap-3">
              <div className="flex flex-col gap-1">
                <small className="text-gray-500 text-xs font-medium">Valor da proposta</small>
                <h1 className={`text-2xl ${cardStyle.text} font-bold`}>
                  {formatCurrency(proposal.budget)}
                </h1>
              </div>

              <div className="flex flex-col gap-1">
                <div className="flex items-center gap-1">
                  <Ruler className={`h-3 w-3 text-blue-500`} />
                  <small className="text-gray-500 text-xs font-medium">Área:</small>
                  <p className={`text-sm ${cardStyle.text} font-medium`}>
                    {proposal.area ? `${Number(proposal.area).toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} m²` : '0,00 m²'}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>

          <CardFooter className="p-0 border-t pt-3">
            <div className="flex flex-col gap-2 w-full">
              <span className="flex gap-2 text-sm items-center">
                <UserCircle className={`size-4 ${cardStyle.text}`} />
                <span className="truncate max-w-full sm:max-w-[170px]">{proposal.customer?.name || 'Cliente não informado'}</span>
              </span>
              <span className="flex gap-2 text-sm items-center">
                <MailIcon className={`size-4 ${cardStyle.text}`} />
                <span className="truncate max-w-full sm:max-w-[170px]">{proposal.customer?.email || 'Email não informado'}</span>
              </span>
              <span className="flex gap-2 text-sm items-center">
                <PhoneIcon className={`size-4 ${cardStyle.text}`} />
                <span className="truncate max-w-full sm:max-w-[170px]">{proposal.customer?.phone || 'Telefone não informado'}</span>
              </span>
            </div>
          </CardFooter>
        </div>
      </Card>
    </>
  );
}
