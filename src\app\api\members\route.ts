import { loadOrganizationMembers } from "@/src/actions/membership";
import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get("organizationId");
    const page = Number(searchParams.get("page")) || 1;
    const pageSize = Number(searchParams.get("pageSize")) || 10;
    const search = searchParams.get("search") || "";

    if (!organizationId) {
      return NextResponse.json(
        { error: "OrganizationId é obrigatório" },
        { status: 400 }
      );
    }

    const result = await loadOrganizationMembers(organizationId, page, pageSize, search);
    return NextResponse.json(result);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Erro ao carregar membros" },
      { status: 500 }
    );
  }
}