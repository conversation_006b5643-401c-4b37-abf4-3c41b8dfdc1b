"use server";
import { replaceFromFileEditorIdAndVariables } from "@/src/helpers/file-editor";
import { prisma } from "@/src/lib/prisma";
import { toKebabCase } from "@/src/lib/utils";
import { WeatherImpactItem } from "@/src/types/core/wheather-impact";
import { Productivity } from "@prisma/client";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

const TOMORROW_IO_API_KEY = process.env.TOMORROW_IO_WHEATER_IMPACT_API_KEY;
const BASE_URL = "https://api.tomorrow.io/v4/weather/";

export async function getWeatherImpactFromProposalId(
  id: string
): Promise<WeatherImpactItem[]> {
  const { organizationId } = await getCurrentOrganization();

  const proposal = await prisma.proposal.findFirst({
    where: {
      id,
      customer: {
        organizationId,
      },
    },
    include: {
      Productivity: true,
    },
  });

  const productivity = proposal?.Productivity || [];
  const city = toKebabCase(`${proposal?.city}`);

  const days = productivity
    .filter((item) => item.startDate)
    .map((item) => `${item.startDate?.toISOString().split("T")[0]}`);

  // Buscar apenas os dados já armazenados
  const pluviosity = await prisma.pluviosity.findMany({
    where: {
      dateKey: {
        in: days,
      },
      city,
    },
  });

  const volumeDateMap = new Map<string, number>(
    pluviosity.map((item) => [item.dateKey, Number(item.value)])
  );
  return parseProductivityToWheatherImpact(productivity, volumeDateMap);
}

function parseProductivityToWheatherImpact(
  productivity: Productivity[],
  volumeDateMap: Map<string, number>
) {
  const wheatherImpactData: WeatherImpactItem[] = productivity.map((item) => {
    const parsedDate = item.startDate?.toISOString().split("T")[0];
    return {
      volume: parsedDate ? volumeDateMap.get(parsedDate) : 0,
      data: parsedDate,
      percentage: Number(item.predictedPeriodPercentage),
    } as WeatherImpactItem;
  });

  return wheatherImpactData.sort((a, b) => {
    if (!a.data || !b.data) return 0;
    return new Date(a.data).getTime() - new Date(b.data).getTime();
  });
}

export async function generateReportWheaterImpact(
  reportTemplateId: string,
  params: any
) {
  try {
    const { organizationId } = await getCurrentOrganization();

    // Verify report template belongs to organization
    const reportTemplate = await prisma.reportTemplate.findFirst({
      where: {
        id: reportTemplateId,
        organizationId,
      },
    });

    if (!reportTemplate) {
      throw new Error(
        "Template não encontrado ou não pertence a sua organização"
      );
    }

    const data = await getWeatherImpactFromProposalId(params.proposalId);
    const formattedData = data.map((item) => ({
      ...item,
      date: new Intl.DateTimeFormat("pt-BR").format(new Date(item.data)),
      percentage: `${item.percentage}%`,
    }));

    const variables = {
      items: formattedData,
    };

    const fileName = `${Date.now()}-wheater-report`;
    const replacedFileEditor = await replaceFromFileEditorIdAndVariables(
      `${reportTemplate?.fileEditorId}`,
      variables,
      fileName
    );

    if (!replacedFileEditor?.id) throw Error("Falha ao salvar arquivo editado");
    const fileEditorId = replacedFileEditor?.id;
    return {
      fileEditorId,
    };
  } catch (e) {
    console.error(e);
  }
}

export async function getPluviosityData(location: string, date: string) {
  try {
    const existingRecord = await prisma.pluviosity.findUnique({
      where: {
        dateKey_city: {
          dateKey: date,
          city: location,
        },
      },
    });

    if (existingRecord && existingRecord.saved) {
      console.log(`Dados já existentes para ${location} em ${date}`);
      return;
    }

    console.log("Iniciando requisição para API...");

    const response = await fetch(
      `${BASE_URL}history/recent?location=${location}&timesteps=daily&apikey=${TOMORROW_IO_API_KEY}`
    );

    const data = await response.json();
    console.log("Resposta da API recebida");

    const result = {
      rain: data,
    };

    console.log(result?.rain.timelines?.daily);
    let messageErrorLogs = "Data saved successfully";

    if (result.rain.code === 429001) {
      messageErrorLogs = result.rain.message;
      console.error("Erro 429001: Limite de requisições excedido.");
      throw new Error(result.rain.message);
    }
    const valueRainIntensityAvg =
      result?.rain.timelines?.daily[1].values?.rainIntensityAvg;

    await prisma.pluviosity.create({
      data: {
        dateKey: date,
        value: valueRainIntensityAvg ?? -1,
        city: location,
        saved: valueRainIntensityAvg != null ? true : false,
        logs: valueRainIntensityAvg ? messageErrorLogs : "Data not saved",
      },
    });

    console.log("Dados salvos com sucesso no banco de dados.");
    return result;
  } catch (e) {
    console.error("Erro ao consultar API ou salvar no banco:", e);
  }
}

export async function syncDatawheater() {
  try {
    const cities = await prisma.proposal.findMany({
      select: {
        city: true,
      },
      distinct: ["city"],
    });

    const failedRecords = await prisma.pluviosity.findMany({
      where: {
        OR: [{ value: -1 }, { saved: false }],
      },
    });

    if (failedRecords) {
      for (const record of failedRecords) {
        await syncOldData(record.city, record.dateKey);
      }
    }

    const today = new Date().toISOString().split("T")[0];

    for (const { city } of cities) {
      const cityFormatted = toKebabCase(`${city}`);
      await getPluviosityData(cityFormatted, today);
    }

    const completionTime = new Date().toLocaleString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      timeZone: "America/Sao_Paulo",
    });

    console.log("----------------------------------------------");
    console.log(`Sincronização concluída em ${completionTime}`);
    console.log("----------------------------------------------");

    return `Sincronização concluída em ${completionTime}`;
  } catch (e) {
    console.error("Erro durante a sincronização:", e);
  }
}

export async function syncOldData(cityFormatted: string, date: string) {
  try {
    console.log("Atualizando dados antigos perdidos...");

    const response = await fetch(
      `${BASE_URL}history/recent?location=${cityFormatted}&timesteps=daily&startTime=${date}T00:00:00Z&endTime=${date}T23:59:59Z&apikey=${TOMORROW_IO_API_KEY}`
    );

    const data = await response.json();

    const result = {
      rain: data,
    };

    const valueRainIntensityAvg =
      result?.rain.timelines?.daily[0].values?.rainIntensityAvg;

    await prisma.pluviosity.update({
      where: {
        dateKey_city: {
          dateKey: date,
          city: cityFormatted,
        },
      },
      data: {
        dateKey: date,
        value: valueRainIntensityAvg ?? -1,
        saved: valueRainIntensityAvg != null ? true : false,
      },
    });

    console.log("Dados antigos atualizados com sucesso no banco de dados.");
  } catch (e) {
    console.error(e);
  }
}
