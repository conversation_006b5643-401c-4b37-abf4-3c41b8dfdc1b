"use client";

import {
  toggleMemberStatus,
  changeMemberRole,
} from "@/src/actions/membership";
import AppConfirmationDialog from "@/src/components/app-confirmation-dialog";
import ContentWrapper from "@/src/components/content-wrapper";
import { Switch } from "@/src/components/ui/switch";
import { useOrganization } from "@/src/hooks/use-organization";
import { useToast } from "@/src/hooks/use-toast";
import { contructColumn } from "@/src/lib/table/columns";
import { formatDate } from "@/src/lib/utils";
import { useRef, useState } from "react";
import MembersTable, { MembersTableRef } from "./components/members-table";
import { Badge } from "@/src/components/ui/badge";
import { useSession } from "next-auth/react";
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/src/components/ui/dialog";
import { <PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/src/components/ui/select";

// interface Member {
//   id: string;
//   enabled: boolean;
//   createdAt: Date;
//   user: {
//     name: string;
//     email: string;
//   };
// }

// interface PaginationState {
//   page: number;
//   pageSize: number;
//   total: number;
//   totalPages: number;
// }

export default function ManageMembers() {
  const [currentPage, setCurrentPage] = useState(1);
  const tableRef = useRef<MembersTableRef>(null);
  const { organizationId, role: currentUserRole } = useOrganization();
  const { toast } = useToast();
  const { data: session } = useSession();
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [dialogOpen, setDialogOpen] = useState<string | null>(null);
  const [isChangingRole, setIsChangingRole] = useState(false);

  const handleToggleStatus = async (memberId: string) => {
    try {
      await toggleMemberStatus(memberId);
      toast({
        title: "Sucesso",
        description: "Status alterado com sucesso!",
        variant: "default"
      });
      tableRef.current?.refresh(currentPage);
    } catch (error) {
      console.error(error);
      toast({
        title: "Erro",
        description: "Erro ao alterar status do membro",
        variant: "destructive"
      });
    }
  };

  const handleChangeRole = async (memberId: string, newRole: "OWNER" | "MEMBER") => {
    setIsChangingRole(true);
    try {
      await changeMemberRole(memberId, newRole);
      toast({
        title: "Sucesso",
        description: "Tipo de usuário alterado com sucesso!",
        variant: "default"
      });
      tableRef.current?.refresh(currentPage);
    } catch (error: any) {
      console.error(error);
      toast({
        title: "Erro",
        description: error.message || "Erro ao alterar o tipo de usuário.",
        variant: "destructive"
      });
    } finally {
      setIsChangingRole(false);
    }
  };

  const columns = [
    contructColumn("user.name", "Nome", (row: any) => row.user?.name || "-"),
    contructColumn("user.email", "Email", (row: any) => row.user?.email || "-"),
    contructColumn("createdAt", "Data de cadastro", (row: any) =>
      formatDate(row.createdAt, "DATE")
    ),
    contructColumn("role", "Tipo de usuário", (row: any) => {
      if (row.role === "OWNER")
        return <Badge variant="default" className="bg-green-100 text-green-600 hover:bg-green-100 hover:text-green-600">Administrador</Badge>;
      if (row.role === "MEMBER")
        return <Badge variant="secondary" className="bg-blue-100 text-blue-600 hover:bg-blue-100 hover:text-blue-600">Editor</Badge>;
      return <Badge variant="outline">{row.role || "-"}</Badge>;
    }),
    contructColumn("enabled", "Status", (row: any) => {
      const enabled = row.enabled;
      return (
        <AppConfirmationDialog
          title={`Deseja realmente ${enabled ? "desabilitar" : "habilitar"
            } este membro?`}
          description="Esta ação pode ser revertida posteriormente."
          onConfirmCallback={() => handleToggleStatus(row.id,)}
          dialogCancelClassName="bg-blue-500 hover:bg-blue-600"
          dialogActionClassName="bg-green-500 hover:bg-green-600"
          confirmButtonText="Confirmar"
        >
          <div className="flex items-center space-x-2">
            <Switch checked={!!enabled} />
            <span>{enabled ? "Ativo" : "Inativo"}</span>
          </div>
        </AppConfirmationDialog>
      );
    }),
    // Coluna de ação para trocar tipo de usuário
    ...(currentUserRole === "OWNER" ? [
      contructColumn("actions", "Ações", (row: any) => {
        if (row.user?.email === session?.user?.email) return null;
        return (
          <Dialog
            open={dialogOpen === row.id}
            onOpenChange={open => {
              if (isChangingRole) return;
              setDialogOpen(open ? row.id : null);
              if (open) setSelectedRole(row.role);
            }}
          >
            <DialogTrigger asChild>
              <button className="p-2 rounded hover:bg-gray-100" title="Alterar tipo de usuário">
                <SquarePen className="w-5 h-5 text-green-600" />
              </button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Alterar tipo de usuário</DialogTitle>
              </DialogHeader>
              <div className="flex flex-col gap-4 mt-2">
                <Select value={selectedRole} onValueChange={setSelectedRole}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Selecione o tipo de usuário" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="OWNER">Administrador</SelectItem>
                    <SelectItem value="MEMBER">Editor</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <DialogFooter>
                <Button
                  className="bg-green-500 hover:bg-green-600"
                  onClick={async () => {
                    await handleChangeRole(row.id, selectedRole as "OWNER" | "MEMBER");
                    if (!isChangingRole) {
                      setDialogOpen(null);
                    }
                  }}
                  disabled={isChangingRole || !selectedRole || selectedRole === row.role}
                >
                  {isChangingRole && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Confirmar
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        );
      })
    ] : []),
  ];



  return (
    <ContentWrapper title="Gerenciar Membros">
      <MembersTable
        ref={tableRef}
        columns={columns}
        onRefreshClick={() => { }}
        onPageChange={(page) => setCurrentPage(page)}
        organizationId={organizationId}
      />
    </ContentWrapper>
  );
}
