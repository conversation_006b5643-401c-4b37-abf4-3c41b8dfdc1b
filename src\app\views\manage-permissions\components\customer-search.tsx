"use client";

import { useState, useEffect } from "react";
import { Combobox, ComboboxOption } from "@/components/ui/combobox";
import { useToast } from "@/src/hooks/use-toast";

interface CustomerSearchProps {
  onCustomerChange: (customerId: string) => void;
  disabled?: boolean;
  className?: string;
}

export function CustomerSearch({ onCustomerChange, className = "" }: CustomerSearchProps) {
  const { toast } = useToast();
  const [customerOptions, setCustomerOptions] = useState<ComboboxOption[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<string>("");

  // Carregar clientes iniciais
  const fetchInitialCustomers = async () => {
    try {
      const response = await fetch("/api/customers/search");
      if (!response.ok) throw new Error("Falha ao carregar clientes");
      const data = await response.json();
      setCustomerOptions(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error("Erro ao carregar clientes:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar a lista de clientes",
        variant: "destructive",
      });
      setCustomerOptions([]);
    }
  };

  // Buscar clientes por termo
  const searchCustomers = async (search: string): Promise<ComboboxOption[]> => {
    try {
      if (!search.trim()) {
        const response = await fetch(`/api/customers/search`);
        if (!response.ok) throw new Error("Falha ao buscar clientes");
        const data = await response.json();
        return Array.isArray(data) ? data : [];
      }
      const cleanSearch = search.trim();
      const response = await fetch(`/api/customers/search?search=${encodeURIComponent(cleanSearch)}`);
      if (!response.ok) throw new Error("Falha ao buscar clientes");
      const data = await response.json();
      if (!Array.isArray(data)) return [];
      const sortedResults = data.sort((a, b) => {
        const aLabel = a.label.toLowerCase();
        const bLabel = b.label.toLowerCase();
        const searchLower = cleanSearch.toLowerCase();
        if (aLabel === searchLower && bLabel !== searchLower) return -1;
        if (bLabel === searchLower && aLabel !== searchLower) return 1;
        if (aLabel.startsWith(searchLower) && !bLabel.startsWith(searchLower)) return -1;
        if (bLabel.startsWith(searchLower) && !aLabel.startsWith(searchLower)) return 1;
        return aLabel.localeCompare(bLabel);
      });
      return sortedResults;
    } catch (error) {
      console.error("Erro ao buscar clientes:", error);
      toast({
        title: "Erro",
        description: "Não foi possível buscar clientes",
        variant: "destructive",
      });
      return [];
    }
  };

  useEffect(() => {
    fetchInitialCustomers();
  }, []);

  useEffect(() => {
    onCustomerChange(selectedCustomer);
  }, [selectedCustomer, onCustomerChange]);

  return (
    <div className={className}>
      <Combobox
        options={customerOptions}
        value={selectedCustomer}
        onChange={setSelectedCustomer}
        placeholder="Selecione um cliente"
        searchPlaceholder="Digite o nome do cliente..."
        emptyMessage="Nenhum cliente encontrado"
        onSearch={searchCustomers}
        className="w-full text-green-700 font-medium"
        customStyles={{
          trigger: "border-green-100 bg-green-50 hover:bg-green-100 hover:border-green-200 text-green-700",
          search: "border-green-100 focus:border-green-300 focus:ring-green-200",
          searchIcon: "text-green-500",
          option: "hover:bg-green-50 hover:text-green-700 focus:bg-green-50 focus:text-green-700",
          selectedIcon: "text-green-600",
          content: "border-green-100",
          emptyMessage: "text-green-600"
        }}
      />
    </div>
  );
} 