"use client"

import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import { cn } from "@/src/lib/utils";
import { Periodicity } from "@/src/types/common";
import { PlanningFrequencyItemInterface } from "@/src/types/core/proposal";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";

interface PlanningFormsProps {
	planningNumber: number;
	periodicity: Periodicity;
	plannings?: PlanningFrequencyItemInterface[];
	disabled?: boolean;
}

export const PlanningForms = forwardRef(
	({ planningNumber, periodicity, plannings, disabled }: PlanningFormsProps, ref) => {
		const [planningForms, setPlanningForms] = useState<any>(plannings);
		const [errors, setErrors] = useState<boolean[]>([]);

		// Used to update the planning form item when the content of a planning form changes
		const handleContentChange = (index: number, value: string) => {
			const form = planningForms.map((form, i) => {
				if (i === index) {
					return { ...form, content: value };
				}
				return form;
			});

			// Atualizar os erros quando o conteúdo mudar
			const newErrors = [...errors];
			newErrors[index] = !value.trim();
			setErrors(newErrors);

			setPlanningForms(form);
		};

		// Função para validar todos os campos do cronograma
		const validateAll = () => {
			if (!planningForms || planningForms.length === 0) return true;

			const newErrors = planningForms.map(form => !form.content || !form.content.trim());
			setErrors(newErrors);

			return !newErrors.some(error => error);
		};

		// Used to update the planningForms when the planningNumber prop changes
		useEffect(() => {
			if (planningNumber > 0) {
				const formList = Array.from(
					{ length: planningNumber },
					(_, i) => i
				).map((index) => ({
					order: index,
					content: "",
					label: ""
				}));

				setPlanningForms(formList);
				setErrors(new Array(planningNumber).fill(true));
			} else {
				setPlanningForms([]);
				setErrors([]);
			}
		}, [planningNumber]);

		// Used to update the planningForms when the plannings prop changes
		useEffect(() => {
			if (plannings?.length) {
				setPlanningForms(plannings);
				setErrors(plannings.map(planning => !planning.content || !planning.content.trim()));
			} else if (planningNumber > 0) {
				const formList = Array.from({ length: planningNumber }, (_, i) => ({
					order: i,
					content: "",
					label: ""
				}));
				setPlanningForms(formList);
				setErrors(new Array(planningNumber).fill(true));
			} else {
				setPlanningForms([]);
				setErrors([]);
			}
		}, [plannings, planningNumber]);

		//Used to expose the planningForms to the parent
		useImperativeHandle(ref, () => ({
			planningForms,
			validate: validateAll,
			hasErrors: () => errors.some(error => error)
		}));

		return (
			<div className="flex flex-col gap-3">
				{!!planningForms.length && planningNumber > 0 && (
					<>
						{/* Mensagem de campos obrigatórios */}
						<div className="text-sm text-gray-500 mb-2">
							Todos os campos do cronograma são obrigatórios.
						</div>

						{/* Formulários de planejamento */}
						{planningForms.map((form, index) => (
							<form key={index}>
								<Label
									className={cn(
										"font-bold text-gray-700",
										errors[index] && "text-destructive"
									)}
								>{`${index + 1}° ${periodicity == "MONTHLY" ? "mês" : "semana"
									}`}</Label>
								<Input
									type="text"
									placeholder="Planejamento"
									value={form.content}
									onChange={(event) =>
										handleContentChange(index, event.target.value)
									}
									className={cn(
										errors[index] && "border-destructive placeholder:text-destructive",
										"focus:ring-2 focus:ring-offset-1",
										errors[index] ? "focus:ring-red-300" : "focus:ring-green-300"
									)}
									disabled={disabled}
								/>
								{errors[index] && (
									<small className="text-xs font-semibold text-destructive">
										Preencha este campo
									</small>
								)}
							</form>
						))}
					</>
				)}
			</div>
		);
	}
);

PlanningForms.displayName = "PlanningForms";
