import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";

interface UnsavedChangesDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSaveAndContinue: () => Promise<void>;
  onContinueWithoutSaving: () => void;
  title?: string;
  description?: string;
  actionName?: string;
}

export function UnsavedChangesDialog({
  isOpen,
  onClose,
  onSaveAndContinue,
  onContinueWithoutSaving,
  title = "Alterações não salvas",
  description = "Você tem alterações não salvas. Deseja salvar antes de continuar?",
  actionName = "continuar"
}: UnsavedChangesDialogProps) {
  const [isSaving, setIsSaving] = React.useState(false);

  const handleSaveAndContinue = async () => {
    setIsSaving(true);
    try {
      await onSaveAndContinue();
    } catch (error) {
      console.error("Erro ao salvar alterações:", error);
    } finally {
      setIsSaving(false);
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] p-0 overflow-hidden">
        <div className="bg-amber-50 p-6 border-b border-amber-200">
          <DialogHeader>
            <div className="flex items-center gap-3">
              <div className="bg-amber-100 p-2 rounded-full">
                <AlertTriangle className="h-6 w-6 text-amber-600" />
              </div>
              <DialogTitle className="text-xl font-semibold text-amber-800">{title}</DialogTitle>
            </div>
            <DialogDescription className="text-amber-700 mt-2 text-base">
              {description}
            </DialogDescription>
          </DialogHeader>
        </div>

        <div className="p-6">
          <DialogFooter className="flex flex-col sm:flex-row gap-3 mt-2">
            <Button
              variant="outline"
              onClick={onClose}
              className="sm:order-1 border-gray-300 hover:bg-gray-100 hover:text-gray-800"
            >
              Cancelar
            </Button>
            <Button
              variant="outline"
              onClick={onContinueWithoutSaving}
              className="sm:order-2 border-amber-300 text-amber-700 hover:bg-amber-100 hover:text-amber-800"
            >
              {`Não salvar e ${actionName}`}
            </Button>
            <Button
              onClick={handleSaveAndContinue}
              className="bg-green-500 hover:bg-green-600 sm:order-3 transition-all duration-200"
              disabled={isSaving}
            >
              {isSaving ? "Salvando..." : `Salvar e ${actionName}`}
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}
