"use client";

/**
 * Função para gerar um arquivo Excel a partir dos dados fornecidos
 * @param data Dados para o Excel
 * @param selectedReports Relatórios selecionados
 * @returns Blob do arquivo Excel
 */
export async function generateExcelFile(data: any, selectedReports: any) {
  // Importar ExcelJS dinamicamente (apenas no cliente)
  const ExcelJS = (await import("exceljs")).default;

  // Criar um novo workbook
  const workbook = new ExcelJS.Workbook();
  workbook.creator = "Ageu Dashboard";
  workbook.lastModifiedBy = "Ageu Dashboard";
  workbook.created = new Date();
  workbook.modified = new Date();

  // Adicionar uma planilha de resumo
  const summarySheet = workbook.addWorksheet("Resumo");

  // Estilizar o cabeçalho
  summarySheet.getColumn(1).width = 30;
  summarySheet.getColumn(2).width = 20;

  // Adicionar título
  const titleRow = summarySheet.addRow(["Dashboard de Indicadores"]);
  titleRow.font = { size: 16, bold: true, color: { argb: "2E7D32" } };
  summarySheet.mergeCells("A1:B1");

  // Adicionar período
  const periodRow = summarySheet.addRow([
    `Período: ${data.dateRange.from} a ${data.dateRange.to}`,
  ]);
  periodRow.font = { size: 12, italic: true };
  summarySheet.mergeCells("A2:B2");

  // Adicionar espaço
  summarySheet.addRow([]);

  // Adicionar cabeçalho da tabela de resumo
  const headerRow = summarySheet.addRow(["Indicador", "Valor"]);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "E8F5E9" },
  };

  // Adicionar dados de resumo
  data.summary.forEach((item: any) => {
    const row = summarySheet.addRow([item.Indicador, ""]);

    // Formatar o valor com base no tipo de indicador
    const cell = row.getCell(2);
    if (
      item.Indicador.includes("Faturamento") ||
      item.Indicador.includes("Valor")
    ) {
      cell.value = item.Valor;
      cell.numFmt = '"R$"#,##0.00';
    } else if (item.Indicador.includes("Taxa")) {
      cell.value = item.Valor;
      cell.numFmt = '0.00"%"';
    } else if (item.Indicador.includes("Área")) {
      cell.value = item.Valor;
      cell.numFmt = '#,##0.00" m²"';
    } else {
      cell.value = item.Valor;
    }
  });

  // Adicionar bordas à tabela
  for (let i = 4; i < 4 + data.summary.length + 1; i++) {
    const row = summarySheet.getRow(i);
    row.eachCell({ includeEmpty: true }, (cell) => {
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };
    });
  }

  // Adicionar planilhas para cada relatório selecionado
  if (selectedReports.revenue && data.monthlyData.length > 0) {
    const revenueSheet = workbook.addWorksheet("Faturamento Mensal");

    // Configurar colunas
    revenueSheet.columns = [
      { header: "Mês/Ano", key: "monthYear", width: 15 },
      {
        header: "Faturamento",
        key: "revenue",
        width: 20,
        style: { numFmt: '"R$"#,##0.00' },
      },
    ];

    // Adicionar dados
    data.monthlyData.forEach((item: any) => {
      revenueSheet.addRow({
        monthYear: item["Mês/Ano"],
        revenue: item["Faturamento"],
      });
    });

    // Estilizar cabeçalho
    const headerRow = revenueSheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "E8F5E9" },
    };

    // Adicionar bordas
    revenueSheet.eachRow((row) => {
      row.eachCell({ includeEmpty: true }, (cell) => {
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
      });
    });
  }

  if (selectedReports.squareMeter && data.monthlyData.length > 0) {
    const squareMeterSheet = workbook.addWorksheet("Valor por Metro Quadrado");

    // Configurar colunas
    squareMeterSheet.columns = [
      { header: "Mês/Ano", key: "monthYear", width: 15 },
      {
        header: "Valor por m²",
        key: "valuePerSquareMeter",
        width: 20,
        style: { numFmt: '"R$"#,##0.00' },
      },
    ];

    // Adicionar dados
    data.monthlyData.forEach((item: any) => {
      squareMeterSheet.addRow({
        monthYear: item["Mês/Ano"],
        valuePerSquareMeter: item["Valor por m²"],
      });
    });

    // Estilizar cabeçalho
    const headerRow = squareMeterSheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "E8F5E9" },
    };

    // Adicionar bordas
    squareMeterSheet.eachRow((row) => {
      row.eachCell({ includeEmpty: true }, (cell) => {
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
      });
    });
  }

  if (selectedReports.conversionRate && data.monthlyData.length > 0) {
    const conversionRateSheet = workbook.addWorksheet("Taxa de Conversão");

    // Configurar colunas
    conversionRateSheet.columns = [
      { header: "Mês/Ano", key: "monthYear", width: 15 },
      {
        header: "Taxa de Conversão",
        key: "conversionRate",
        width: 20,
        style: { numFmt: '0.00"%"' },
      },
    ];

    // Adicionar dados
    data.monthlyData.forEach((item: any) => {
      conversionRateSheet.addRow({
        monthYear: item["Mês/Ano"],
        conversionRate: item["Taxa de Conversão"],
      });
    });

    // Estilizar cabeçalho
    const headerRow = conversionRateSheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "E8F5E9" },
    };

    // Adicionar bordas
    conversionRateSheet.eachRow((row) => {
      row.eachCell({ includeEmpty: true }, (cell) => {
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
      });
    });
  }

  if (selectedReports.contractsCount && data.monthlyData.length > 0) {
    const contractsSheet = workbook.addWorksheet("Quantidade de Contratos");

    // Configurar colunas
    contractsSheet.columns = [
      { header: "Mês/Ano", key: "monthYear", width: 15 },
      { header: "Contratos", key: "contracts", width: 15 },
    ];

    // Adicionar dados
    data.monthlyData.forEach((item: any) => {
      contractsSheet.addRow({
        monthYear: item["Mês/Ano"],
        contracts:
          item["Contratos"] !== null && item["Contratos"] !== undefined
            ? Number(item["Contratos"])
            : 0,
      });
    });

    // Estilizar cabeçalho
    const headerRow = contractsSheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "E8F5E9" },
    };

    // Adicionar bordas
    contractsSheet.eachRow((row) => {
      row.eachCell({ includeEmpty: true }, (cell) => {
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
      });
    });
  }

  if (selectedReports.statusDistribution && data.statusData.length > 0) {
    const statusSheet = workbook.addWorksheet("Distribuição por Status");

    // Configurar colunas
    statusSheet.columns = [
      { header: "Status", key: "status", width: 25 },
      { header: "Quantidade", key: "count", width: 15 },
      {
        header: "Percentual",
        key: "percentage",
        width: 15,
        style: { numFmt: '0.00"%"' },
      },
    ];

    // Adicionar dados
    data.statusData.forEach((item: any) => {
      statusSheet.addRow({
        status: item["Status"],
        count: item["Quantidade"],
        percentage: item["Percentual"],
      });
    });

    // Estilizar cabeçalho
    const headerRow = statusSheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "E8F5E9" },
    };

    // Adicionar bordas
    statusSheet.eachRow((row) => {
      row.eachCell({ includeEmpty: true }, (cell) => {
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
      });
    });
  }

  // Gerar o arquivo como um blob
  const buffer = await workbook.xlsx.writeBuffer();
  return new Blob([buffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });
}
