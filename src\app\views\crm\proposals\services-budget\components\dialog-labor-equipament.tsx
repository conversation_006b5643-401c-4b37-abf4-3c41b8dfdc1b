import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, Di<PERSON>Title } from "@/src/components/ui/dialog";
import { CustomInput } from "@/src/components/app-input";
import { But<PERSON> } from "@/src/components/ui/button";
import { getLabortypes } from "@/src/actions/laborType";
import { useForm, FormProvider } from 'react-hook-form';
import { DialogClose } from "@radix-ui/react-dialog";

type LaborEquipamentProps = {
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
    type: string;
    setSelectedLabors: (labors: any[]) => void; // Passando os labors para o RepairBudgetForm
};

export default function LaborEquipament({ isOpen, onOpenChange, type, setSelectedLabors }: LaborEquipamentProps) {
    const methods = useForm();
    const { watch, handleSubmit } = methods;
    const selectedLabors = watch('laborsEquipament', []);
    const [data, setData] = useState<any[]>([]);
    const [itemsFiltered, setItemsFiltered] = useState<any[]>([]);

    useEffect(() => {
        async function fetchData() {
            try {
                const result = await getLabortypes();
                if (result) {
                    setData(result.data);
                }
            } catch (error) {
                console.error("Erro ao buscar os tipos de trabalho:", error);
            }
        }
        fetchData();
    }, []);

    useEffect(() => {
        if (data.length > 0) {
            const filtered = data.filter(item => item.type === type);
            setItemsFiltered(filtered);
        }
    }, [data, type]);

    const handleSave = () => {
        const selectedItems = itemsFiltered.filter(obj => selectedLabors.includes(obj.id));
        setSelectedLabors(selectedItems);
        onOpenChange(false); // Fecha o modal
    };

    return (
        <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Adicionar {type === "LABOR" ? "mão de obra" : "equipamentos"}</DialogTitle>
                </DialogHeader>
                <FormProvider {...methods}>
                    <form onSubmit={handleSubmit(handleSave)}>
                        <CustomInput
                            name="laborsEquipament"
                            type="checkbox-group"
                            items={itemsFiltered.map((labor: any) => ({
                                label: labor.name,
                                value: labor.id,
                            }))}
                        />
                        <DialogFooter>
                            <DialogClose asChild>
                                <Button variant="outline" onClick={() => onOpenChange(false)}>
                                    Cancelar
                                </Button>
                            </DialogClose>
                            <Button type="submit" className="bg-green-500 hover:bg-green-400">
                                Adicionar
                            </Button>
                        </DialogFooter>
                    </form>
                </FormProvider>
            </DialogContent>
        </Dialog>
    );
}
