"use client";

/**
 * Função para fazer o download de um arquivo Blob
 * @param blob O blob do arquivo
 * @param fileName Nome do arquivo para download
 */
export function downloadBlob(blob: Blob, fileName: string) {
  // Criar um elemento <a> temporário
  const link = document.createElement('a');
  
  // Criar uma URL para o blob
  const url = URL.createObjectURL(blob);
  
  // Configurar o link
  link.href = url;
  link.download = fileName;
  
  // Adicionar o link ao documento
  document.body.appendChild(link);
  
  // Simular um clique no link
  link.click();
  
  // Limpar: remover o link e revogar a URL
  setTimeout(() => {
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, 100);
}
