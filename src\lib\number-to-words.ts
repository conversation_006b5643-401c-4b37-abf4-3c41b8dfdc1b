"use server";

/**
 * Converte um número para seu valor por extenso em português
 * @param value Valor numérico a ser convertido
 * @returns String com o valor por extenso
 */
export async function numberToWords(value: number): Promise<string> {
  if (value === 0) return "zero";

  const units = [
    "", "um", "dois", "três", "quatro", "cinco", "seis", "sete", "oito", "nove",
    "dez", "onze", "doze", "treze", "quatorze", "quinze", "dezesseis", "dezessete", "dezoito", "dezenove"
  ];
  
  const tens = [
    "", "", "vinte", "trinta", "quarenta", "cinquenta", "sessenta", "setenta", "oitenta", "noventa"
  ];
  
  const hundreds = [
    "", "cento", "duzentos", "trezentos", "quatrocentos", "quinhentos", "seiscentos", "setecentos", "oitocentos", "novecentos"
  ];
  
  const scales = ["", "mil", "milhão", "bilhão", "trilhão"];
  const scalesPlural = ["", "mil", "milhões", "bilhões", "trilhões"];

  // Função para converter um grupo de 3 dígitos
  function convertGroup(num: number): string {
    let result = "";
    
    // Caso especial para 100
    if (num === 100) {
      return "cem";
    }
    
    // Centenas
    const hundred = Math.floor(num / 100);
    if (hundred > 0) {
      result += hundreds[hundred] + " ";
      num %= 100;
      
      // Adicionar "e" se houver dezenas ou unidades
      if (num > 0) {
        result += "e ";
      }
    }
    
    // Dezenas e unidades
    if (num < 20) {
      // Para números menores que 20, usar a lista de unidades
      if (num > 0) {
        result += units[num];
      }
    } else {
      // Para números maiores ou iguais a 20
      const ten = Math.floor(num / 10);
      const unit = num % 10;
      
      result += tens[ten];
      
      if (unit > 0) {
        result += " e " + units[unit];
      }
    }
    
    return result.trim();
  }

  // Função principal para converter o número
  function convert(num: number): string {
    if (num === 0) return "";
    
    let result = "";
    let scaleIndex = 0;
    
    // Processar o número em grupos de 3 dígitos
    while (num > 0) {
      const group = num % 1000;
      
      if (group > 0) {
        const groupText = convertGroup(group);
        const scale = group === 1 ? scales[scaleIndex] : scalesPlural[scaleIndex];
        
        if (scaleIndex > 0) {
          result = groupText + (scale ? " " + scale : "") + (result ? " e " + result : "");
        } else {
          result = groupText + (result ? " e " + result : "");
        }
      }
      
      num = Math.floor(num / 1000);
      scaleIndex++;
    }
    
    return result;
  }

  // Separar parte inteira e decimal
  const isNegative = value < 0;
  const absValue = Math.abs(value);
  const integerPart = Math.floor(absValue);
  const decimalPart = Math.round((absValue - integerPart) * 100);
  
  let result = convert(integerPart);
  
  // Adicionar parte decimal se houver
  if (decimalPart > 0) {
    result += " reais e " + convert(decimalPart) + (decimalPart === 1 ? " centavo" : " centavos");
  } else {
    result += " reais";
  }
  
  // Adicionar "negativo" se for um número negativo
  if (isNegative) {
    result = "menos " + result;
  }
  
  return result;
}

/**
 * Converte um valor monetário para seu valor por extenso em português
 * @param value Valor monetário a ser convertido
 * @returns String com o valor por extenso
 */
export async function currencyToWords(value: number): Promise<string> {
  if (value === 0) return "zero reais";

  // Separar parte inteira e decimal
  const isNegative = value < 0;
  const absValue = Math.abs(value);
  const integerPart = Math.floor(absValue);
  const decimalPart = Math.round((absValue - integerPart) * 100);
  
  let result = await numberToWords(integerPart);
  
  // Ajustar a palavra "reais" que já foi adicionada pelo numberToWords
  result = result.replace(" reais", "");
  
  // Singular ou plural para "real"
  if (integerPart === 1) {
    result = "um real";
  } else {
    result += " reais";
  }
  
  // Adicionar parte decimal se houver
  if (decimalPart > 0) {
    const centavosText = await numberToWords(decimalPart);
    // Remover a palavra "reais" que foi adicionada pelo numberToWords para os centavos
    const cleanCentavosText = centavosText.replace(" reais", "");
    
    result += " e " + cleanCentavosText + (decimalPart === 1 ? " centavo" : " centavos");
  }
  
  // Adicionar "negativo" se for um número negativo
  if (isNegative) {
    result = "menos " + result;
  }
  
  return result;
}
