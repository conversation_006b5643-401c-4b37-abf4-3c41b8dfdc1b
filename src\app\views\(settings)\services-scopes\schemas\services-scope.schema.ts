import { z } from "zod";

export const servicesScopeSchema = z.object({
	id: z.string().optional(),
	name: z.string().trim().min(1, "Preencha esse campo"),
	description: z.string(),
	types: z
		.array(z.union([z.literal("PROPOSAL_SERVICE"), z.literal("REPAIR_SERVICE")]))
		.refine((value) => value.length > 0, "Escolha pelo menos um tipo"),
});

export type ServicesScopeSchema = z.infer<typeof servicesScopeSchema>;
