"use client";

import { useState } from "react";
import { useEmail } from "@/src/hooks/use-email";
import ContentWrapper from "@/src/components/content-wrapper";
import { But<PERSON> } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import { Textarea } from "@/src/components/ui/textarea";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/src/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs";
import { toast } from "@/src/hooks/use-toast";
import { Loader2, Mail, Send } from "lucide-react";
import EmailConfigTest from "./test-config";

export default function EmailTestPage() {
  const {
    loading,
    error,
    sendTestEmail,
    sendContractEmail,
    sendProposalEmail,
    sendNotificationEmail,
    sendCustomEmail,
  } = useEmail();

  // Estado para o email de teste
  const [testEmail, setTestEmail] = useState("");

  // Estado para o email de contrato
  const [contractEmail, setContractEmail] = useState({
    to: "",
    contractName: "",
    contractLink: "",
    customerName: "",
  });

  // Estado para o email de proposta
  const [proposalEmail, setProposalEmail] = useState({
    to: "",
    proposalName: "",
    proposalLink: "",
    customerName: "",
  });

  // Estado para o email de notificação
  const [notificationEmail, setNotificationEmail] = useState({
    to: "",
    title: "",
    message: "",
    actionLink: "",
    actionText: "",
  });

  // Estado para o email personalizado
  const [customEmail, setCustomEmail] = useState({
    to: "",
    subject: "",
    html: "",
  });

  // Função para lidar com o envio de email de teste
  const handleTestEmail = async () => {
    if (!testEmail) {
      toast({
        title: "Erro",
        description: "Por favor, informe um email válido",
        variant: "destructive",
      });
      return;
    }

    const result = await sendTestEmail(testEmail);
    if (result) {
      toast({
        title: "Sucesso",
        description: "Email de teste enviado com sucesso",
      });
    } else {
      toast({
        title: "Erro",
        description: error || "Falha ao enviar email de teste",
        variant: "destructive",
      });
    }
  };

  // Função para lidar com o envio de email de contrato
  const handleContractEmail = async () => {
    const { to, contractName, contractLink, customerName } = contractEmail;
    if (!to || !contractName || !contractLink || !customerName) {
      toast({
        title: "Erro",
        description: "Por favor, preencha todos os campos",
        variant: "destructive",
      });
      return;
    }

    const result = await sendContractEmail(contractEmail);
    if (result) {
      toast({
        title: "Sucesso",
        description: "Email de contrato enviado com sucesso",
      });
    } else {
      toast({
        title: "Erro",
        description: error || "Falha ao enviar email de contrato",
        variant: "destructive",
      });
    }
  };

  // Função para lidar com o envio de email de proposta
  const handleProposalEmail = async () => {
    const { to, proposalName, proposalLink, customerName } = proposalEmail;
    if (!to || !proposalName || !proposalLink || !customerName) {
      toast({
        title: "Erro",
        description: "Por favor, preencha todos os campos",
        variant: "destructive",
      });
      return;
    }

    const result = await sendProposalEmail(proposalEmail);
    if (result) {
      toast({
        title: "Sucesso",
        description: "Email de proposta enviado com sucesso",
      });
    } else {
      toast({
        title: "Erro",
        description: error || "Falha ao enviar email de proposta",
        variant: "destructive",
      });
    }
  };

  // Função para lidar com o envio de email de notificação
  const handleNotificationEmail = async () => {
    const { to, title, message } = notificationEmail;
    if (!to || !title || !message) {
      toast({
        title: "Erro",
        description: "Por favor, preencha todos os campos obrigatórios",
        variant: "destructive",
      });
      return;
    }

    const result = await sendNotificationEmail(notificationEmail);
    if (result) {
      toast({
        title: "Sucesso",
        description: "Email de notificação enviado com sucesso",
      });
    } else {
      toast({
        title: "Erro",
        description: error || "Falha ao enviar email de notificação",
        variant: "destructive",
      });
    }
  };

  // Função para lidar com o envio de email personalizado
  const handleCustomEmail = async () => {
    const { to, subject, html } = customEmail;
    if (!to || !subject || !html) {
      toast({
        title: "Erro",
        description: "Por favor, preencha todos os campos",
        variant: "destructive",
      });
      return;
    }

    const result = await sendCustomEmail(customEmail);
    if (result) {
      toast({
        title: "Sucesso",
        description: "Email personalizado enviado com sucesso",
      });
    } else {
      toast({
        title: "Erro",
        description: error || "Falha ao enviar email personalizado",
        variant: "destructive",
      });
    }
  };

  return (
    <ContentWrapper title="Teste de Envio de Emails" id="email-test">
      <div className="container mx-auto py-6">
        <EmailConfigTest />

        <Tabs defaultValue="test" className="w-full">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="config">Configuração</TabsTrigger>
            <TabsTrigger value="test">Teste</TabsTrigger>
            <TabsTrigger value="contract">Contrato</TabsTrigger>
            <TabsTrigger value="proposal">Proposta</TabsTrigger>
            <TabsTrigger value="notification">Notificação</TabsTrigger>
            <TabsTrigger value="custom">Personalizado</TabsTrigger>
          </TabsList>

          {/* Tab de Teste */}
          <TabsContent value="test">
            <Card>
              <CardHeader>
                <CardTitle>Email de Teste</CardTitle>
                <CardDescription>
                  Envie um email de teste para verificar se o serviço está funcionando corretamente.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="test-email">Email de Destino</Label>
                  <Input
                    id="test-email"
                    placeholder="<EMAIL>"
                    value={testEmail}
                    onChange={(e) => setTestEmail(e.target.value)}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  onClick={handleTestEmail}
                  disabled={loading || !testEmail}
                  className="w-full"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Enviando...
                    </>
                  ) : (
                    <>
                      <Mail className="mr-2 h-4 w-4" />
                      Enviar Email de Teste
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Tab de Contrato */}
          <TabsContent value="contract">
            <Card>
              <CardHeader>
                <CardTitle>Email de Contrato</CardTitle>
                <CardDescription>
                  Envie um email informando sobre um contrato disponível para assinatura.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="contract-email">Email de Destino</Label>
                  <Input
                    id="contract-email"
                    placeholder="<EMAIL>"
                    value={contractEmail.to}
                    onChange={(e) =>
                      setContractEmail({ ...contractEmail, to: e.target.value })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="customer-name">Nome do Cliente</Label>
                  <Input
                    id="customer-name"
                    placeholder="Nome do Cliente"
                    value={contractEmail.customerName}
                    onChange={(e) =>
                      setContractEmail({
                        ...contractEmail,
                        customerName: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contract-name">Nome do Contrato</Label>
                  <Input
                    id="contract-name"
                    placeholder="Nome do Contrato"
                    value={contractEmail.contractName}
                    onChange={(e) =>
                      setContractEmail({
                        ...contractEmail,
                        contractName: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contract-link">Link do Contrato</Label>
                  <Input
                    id="contract-link"
                    placeholder="https://exemplo.com/contrato"
                    value={contractEmail.contractLink}
                    onChange={(e) =>
                      setContractEmail({
                        ...contractEmail,
                        contractLink: e.target.value,
                      })
                    }
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  onClick={handleContractEmail}
                  disabled={
                    loading ||
                    !contractEmail.to ||
                    !contractEmail.contractName ||
                    !contractEmail.contractLink ||
                    !contractEmail.customerName
                  }
                  className="w-full"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Enviando...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Enviar Email de Contrato
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Tab de Proposta */}
          <TabsContent value="proposal">
            <Card>
              <CardHeader>
                <CardTitle>Email de Proposta</CardTitle>
                <CardDescription>
                  Envie um email informando sobre uma proposta disponível para análise.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="proposal-email">Email de Destino</Label>
                  <Input
                    id="proposal-email"
                    placeholder="<EMAIL>"
                    value={proposalEmail.to}
                    onChange={(e) =>
                      setProposalEmail({ ...proposalEmail, to: e.target.value })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="proposal-customer-name">Nome do Cliente</Label>
                  <Input
                    id="proposal-customer-name"
                    placeholder="Nome do Cliente"
                    value={proposalEmail.customerName}
                    onChange={(e) =>
                      setProposalEmail({
                        ...proposalEmail,
                        customerName: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="proposal-name">Nome da Proposta</Label>
                  <Input
                    id="proposal-name"
                    placeholder="Nome da Proposta"
                    value={proposalEmail.proposalName}
                    onChange={(e) =>
                      setProposalEmail({
                        ...proposalEmail,
                        proposalName: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="proposal-link">Link da Proposta</Label>
                  <Input
                    id="proposal-link"
                    placeholder="https://exemplo.com/proposta"
                    value={proposalEmail.proposalLink}
                    onChange={(e) =>
                      setProposalEmail({
                        ...proposalEmail,
                        proposalLink: e.target.value,
                      })
                    }
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  onClick={handleProposalEmail}
                  disabled={
                    loading ||
                    !proposalEmail.to ||
                    !proposalEmail.proposalName ||
                    !proposalEmail.proposalLink ||
                    !proposalEmail.customerName
                  }
                  className="w-full"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Enviando...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Enviar Email de Proposta
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Tab de Notificação */}
          <TabsContent value="notification">
            <Card>
              <CardHeader>
                <CardTitle>Email de Notificação</CardTitle>
                <CardDescription>
                  Envie um email de notificação personalizado.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="notification-email">Email de Destino</Label>
                  <Input
                    id="notification-email"
                    placeholder="<EMAIL>"
                    value={notificationEmail.to}
                    onChange={(e) =>
                      setNotificationEmail({
                        ...notificationEmail,
                        to: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notification-title">Título</Label>
                  <Input
                    id="notification-title"
                    placeholder="Título da Notificação"
                    value={notificationEmail.title}
                    onChange={(e) =>
                      setNotificationEmail({
                        ...notificationEmail,
                        title: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notification-message">Mensagem</Label>
                  <Textarea
                    id="notification-message"
                    placeholder="Mensagem da notificação"
                    value={notificationEmail.message}
                    onChange={(e) =>
                      setNotificationEmail({
                        ...notificationEmail,
                        message: e.target.value,
                      })
                    }
                    rows={4}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notification-action-link">
                    Link de Ação (opcional)
                  </Label>
                  <Input
                    id="notification-action-link"
                    placeholder="https://exemplo.com/acao"
                    value={notificationEmail.actionLink}
                    onChange={(e) =>
                      setNotificationEmail({
                        ...notificationEmail,
                        actionLink: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notification-action-text">
                    Texto do Botão (opcional)
                  </Label>
                  <Input
                    id="notification-action-text"
                    placeholder="Clique Aqui"
                    value={notificationEmail.actionText}
                    onChange={(e) =>
                      setNotificationEmail({
                        ...notificationEmail,
                        actionText: e.target.value,
                      })
                    }
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  onClick={handleNotificationEmail}
                  disabled={
                    loading ||
                    !notificationEmail.to ||
                    !notificationEmail.title ||
                    !notificationEmail.message
                  }
                  className="w-full"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Enviando...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Enviar Notificação
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Tab Personalizado */}
          <TabsContent value="config">
            <Card>
              <CardHeader>
                <CardTitle>Configuração de Email</CardTitle>
                <CardDescription>
                  Verifique as configurações de email do sistema e teste a conexão com o servidor SMTP.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="mb-4">
                  Para verificar as configurações de email, clique no botão &quot;Testar Configurações de Email&quot; acima.
                </p>
                <p className="mb-4">
                  Se houver algum problema com as configurações, verifique o arquivo <code>.env</code> e certifique-se de que as seguintes variáveis estão configuradas corretamente:
                </p>
                <ul className="list-disc pl-5 mb-4 space-y-2">
                  <li><code>EMAIL_HOST</code>: Servidor SMTP (ex: smtp.sendgrid.net)</li>
                  <li><code>EMAIL_PORT</code>: Porta do servidor SMTP (ex: 587)</li>
                  <li><code>EMAIL_USER</code>: Usuário para autenticação SMTP</li>
                  <li><code>EMAIL_PASS</code>: Senha para autenticação SMTP</li>
                  <li><code>EMAIL_FROM</code>: Endereço de email de origem (ex: <EMAIL>)</li>
                </ul>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="custom">
            <Card>
              <CardHeader>
                <CardTitle>Email Personalizado</CardTitle>
                <CardDescription>
                  Envie um email totalmente personalizado com HTML.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="custom-email">Email de Destino</Label>
                  <Input
                    id="custom-email"
                    placeholder="<EMAIL>"
                    value={customEmail.to}
                    onChange={(e) =>
                      setCustomEmail({ ...customEmail, to: e.target.value })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="custom-subject">Assunto</Label>
                  <Input
                    id="custom-subject"
                    placeholder="Assunto do Email"
                    value={customEmail.subject}
                    onChange={(e) =>
                      setCustomEmail({
                        ...customEmail,
                        subject: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="custom-html">Conteúdo HTML</Label>
                  <Textarea
                    id="custom-html"
                    placeholder="<div>Seu conteúdo HTML aqui</div>"
                    value={customEmail.html}
                    onChange={(e) =>
                      setCustomEmail({ ...customEmail, html: e.target.value })
                    }
                    rows={10}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  onClick={handleCustomEmail}
                  disabled={
                    loading ||
                    !customEmail.to ||
                    !customEmail.subject ||
                    !customEmail.html
                  }
                  className="w-full"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Enviando...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Enviar Email Personalizado
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </ContentWrapper>
  );
}
