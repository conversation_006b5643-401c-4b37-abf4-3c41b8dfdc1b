import { Button } from "@/src/components/ui/button";
import { Card } from "@/src/components/ui/card";
import DocumentPreviewDialog from "@/src/components/app-document-preview";
import { Eye, Hourglass, X, GripVertical } from "lucide-react";
import Image from "next/image";
import { Photo } from "@/src/types/core/inspection-paramenters";

interface PhotoCardProps {
  photo: Photo; // A interface Photo já inclui tempId como propriedade opcional
  index: number;
  photoPreviewUrl?: string;
  onDelete: (index: number) => void;
  isDraggable?: boolean;
}

export default function PhotoCard({
  photo,
  index,
  photoPreviewUrl,
  onDelete,
  isDraggable = true,
}: PhotoCardProps) {
  const handleDelete = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onDelete(index);
  };

  // Determina se a foto já foi salva (tem ID e caminho do arquivo)
  const isSaved = photo.file?.path && photo.id;

  // Determina se temos uma URL de preview para exibir
  const hasPreview = Boolean(photoPreviewUrl);

  return (
    <Card className="overflow-hidden hover:shadow-md transition-all duration-300 border-gray-200 hover:border-gray-300">
      <div className="p-3">
        {/* Cabeçalho com título e botão de exclusão */}
        <div className="flex justify-between items-start mb-2 gap-2">
          <div className="flex items-center gap-1 min-w-0 flex-1">
            {isDraggable && (
              <div className="text-gray-400 cursor-grab flex-shrink-0">
                <GripVertical className="w-4 h-4" />
              </div>
            )}
            <h4 className="font-medium text-gray-800 truncate min-w-0">{photo.description}</h4>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="text-red-500 hover:bg-red-50 hover:text-red-500 flex-shrink-0"
            onClick={handleDelete}
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Preview da imagem - com 3 estados possíveis */}
        {isSaved ? (
          // Estado 1: Foto já salva no servidor
          <div className="mb-3 rounded-md overflow-hidden border border-gray-200 bg-gray-50 relative" style={{ height: "140px" }}>
            <Image
              src={`/api/thumbnails/${encodeURIComponent(photo.fileId || '')}`}
              alt={photo.description}
              className="object-cover"
              fill
              sizes="(max-width: 768px) 100vw, 300px"
              unoptimized
            />
          </div>
        ) : hasPreview ? (
          // Estado 2: Foto carregada localmente, com preview disponível
          <div className="mb-3 rounded-md overflow-hidden border border-gray-200 bg-gray-50 relative" style={{ height: "140px" }}>
            <Image
              src={photoPreviewUrl || ''}
              alt={photo.description}
              className="object-cover"
              fill
              sizes="(max-width: 768px) 100vw, 300px"
              unoptimized
            />
          </div>
        ) : (
          // Estado 3: Foto em processamento
          <div className="mb-3 rounded-md overflow-hidden border border-gray-200 bg-gray-50 flex items-center justify-center" style={{ height: "140px" }}>
            <div className="flex flex-col items-center gap-2">
              <Hourglass className="w-8 h-8 text-amber-500" />
              <p className="text-xs text-gray-500">Processando imagem</p>
            </div>
          </div>
        )}

        {/* Rodapé - botão de visualização ou mensagem de aguardando */}
        {isSaved ? (
          // Foto já salva - mostrar botão de visualização
          <div className="flex justify-end">
            <DocumentPreviewDialog
              template={
                <Button variant="outline" size="sm" className="text-green-500 border-green-200 hover:bg-green-50 hover:text-green-500">
                  <Eye className="mr-1 h-4 w-4" />
                  Visualizar
                </Button>
              }
              url={`/api/files/${encodeURIComponent(photo.fileId || '')}`}
              type="image"
            />
          </div>
        ) : (
          // Foto ainda não salva - mostrar mensagem de aguardando
          <div className="bg-yellow-100 text-yellow-500 px-3 py-2 rounded flex items-center justify-center text-sm w-full">
            <Hourglass className="mr-1 h-4 w-4" />
            Imagem inserida e aguardando salvamento
          </div>
        )}
      </div>
    </Card>
  );
}
