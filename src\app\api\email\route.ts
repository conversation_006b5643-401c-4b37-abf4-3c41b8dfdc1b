import { NextRequest, NextResponse } from 'next/server';
import { sendEmail, sendContractEmail, sendProposalEmail, sendNotificationEmail, testEmailService } from '@/src/services/email-service';
import { auth } from '@/src/providers/auth';

// Função para verificar se o usuário está autenticado
async function isAuthenticated() {
  const session = await auth();
  return !!session?.user;
}

// Endpoint para enviar emails
export async function POST(request: NextRequest) {
  try {
    // Verificar autenticação
    const authenticated = await isAuthenticated();
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }

    // Obter dados do corpo da requisição
    const data = await request.json();
    const { type, ...params } = data;

    let result = false;

    // Enviar email com base no tipo
    switch (type) {
      case 'test':
        result = await testEmailService(params.to);
        break;
      case 'contract':
        result = await sendContractEmail(
          params.to,
          params.contractName,
          params.contractLink,
          params.customerName
        );
        break;
      case 'proposal':
        result = await sendProposalEmail(
          params.to,
          params.proposalName,
          params.proposalLink,
          params.customerName,
          params.attachments
        );
        break;
      case 'notification':
        result = await sendNotificationEmail(
          params.to,
          params.title,
          params.message,
          params.actionLink,
          params.actionText
        );
        break;
      case 'custom':
        result = await sendEmail(params);
        break;
      default:
        return NextResponse.json(
          { error: 'Tipo de email inválido' },
          { status: 400 }
        );
    }

    if (result) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json(
        { error: 'Falha ao enviar email' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Erro ao processar requisição de email:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
