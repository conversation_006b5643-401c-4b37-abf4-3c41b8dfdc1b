/* Estilos para o filtro de tipo de serviço */
.service-type-filter {
  --placeholder-color: #15803d;
  --badge-text-color: white;
  --chevron-color: #15803d;
  --x-icon-color: #15803d;
}

/* Ajuste de altura do MultiSelect para ficar consistente com outros inputs */
.service-type-filter button {
  height: 36px !important;
  min-height: 36px !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  display: flex !important;
  align-items: center !important;
}

/* Ajuste para o container interno do MultiSelect */
.service-type-filter button > div {
  display: flex !important;
  align-items: center !important;
  height: 100% !important;
}

/* Estilos para garantir que o texto nos badges seja branco */
.service-type-filter .bg-green-500,
.service-type-filter .bg-green-600,
.service-type-filter [class*="bg-green-"] {
  color: var(--badge-text-color) !important;
  font-weight: 500 !important;
  height: 22px !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  display: flex !important;
  align-items: center !important;
  font-size: 0.8rem !important;
}

.service-type-filter .bg-green-500 *,
.service-type-filter .bg-green-600 *,
.service-type-filter [class*="bg-green-"] * {
  color: var(--badge-text-color) !important;
}

/* Estilo para o texto do placeholder no MultiSelect */
.service-type-filter button > div > span,
.service-type-filter button span,
.service-type-filter button div span {
  color: var(--placeholder-color) !important;
  font-weight: 500 !important;
  opacity: 1 !important;
}

/* Estilo para o ícone de seta no MultiSelect */
.service-type-filter button > div > svg,
.service-type-filter button svg.h-4,
.service-type-filter svg.h-4,
.service-type-filter svg.cursor-pointer,
.service-type-filter .text-muted-foreground,
.service-type-filter button div svg,
.service-type-filter div svg:not([class*="bg-green-"] svg) {
  color: var(--placeholder-color) !important;
  opacity: 1 !important;
}

/* Estilo específico para o ícone de chevron (seta para baixo) */
.service-type-filter svg.h-4.mx-2,
.service-type-filter svg.mx-2,
.service-type-filter svg[class*="ChevronDown"],
.service-type-filter button div svg.h-4.mx-2.cursor-pointer.text-muted-foreground {
  color: var(--chevron-color, #15803d) !important;
  opacity: 0.8 !important;
}

/* Estilo específico para o ícone X (limpar seleção) */
.service-type-filter button div svg.h-4.mx-2.cursor-pointer.text-muted-foreground,
.service-type-filter svg[class*="XIcon"] {
  color: var(--x-icon-color, #15803d) !important;
  opacity: 0.8 !important;
}

/* Exceção para os ícones dentro dos badges (que devem permanecer brancos) */
.service-type-filter [class*="bg-green-"] svg {
  color: var(--badge-text-color) !important;
  opacity: 0.9 !important;
}

/* Estilos específicos para os ícones do Lucide */
.service-type-filter button div svg[data-lucide="ChevronDown"],
.service-type-filter button div svg[data-lucide="X"] {
  color: var(--chevron-color, #15803d) !important;
  opacity: 0.8 !important;
  stroke-width: 2.5 !important; /* Aumenta a espessura para melhor visibilidade */
}

/* Sobrescrever a classe text-muted-foreground para os ícones */
.service-type-filter .text-muted-foreground {
  color: var(--chevron-color, #15803d) !important;
}

/* Estilo específico para o texto do placeholder */
.service-type-filter button div.flex.items-center.justify-between.w-full.mx-auto span {
  color: var(--placeholder-color) !important;
}
