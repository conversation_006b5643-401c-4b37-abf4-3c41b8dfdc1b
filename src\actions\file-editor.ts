"use server";

import { deleteImageFromBucketAnd<PERSON><PERSON>, minioClient } from "@/src/lib/minio";
import { prisma } from "@/src/lib/prisma";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { v4 as uuidv4 } from 'uuid';
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";
import { toKebabCase } from "@/src/lib/utils";

const BUCKET_NAME = `${process.env.MINIO_BUCKET_NAME}`;

export async function loadFileEditorById(id: string) {
  try {
    const file = await prisma.fileEditor.findUnique({
      where: {
        id,
      }
    });
    return file;
  } catch (error) {
    console.error('Error fetching file:', error);
  }
}

export async function deleteFileEditorById(id: string) {
  try {
    const file = await loadFileEditorById(id);
    if (!file) {
      throw Error("File not found or access denied");
    }

    await deleteImageFromBucketAndKey(file.bucket, file.key);
    await prisma.fileEditor.delete({
      where: {
        id,
      }
    });
    return file;
  } catch (error) {
    console.error('Error deleting file:', error);
  }
}

export async function persistFileEditor(file: File, existingFileKey?: string) {
  try {
    if (!file) {
      throw Error("No file uploaded");
    }

    const buffer = Buffer.from(await file.arrayBuffer());
    const filename = file.name;
    const mimetype = file.type;
    const size = file.size;
    const newVersion = Date.now();
    const { organizationName } = await getCurrentOrganization();
    if (!organizationName) throw new Error("Nome da organização não encontrado para salvar arquivo do editor.");
    const orgSlug = toKebabCase(organizationName);

    // Se temos um fileKey existente, atualizamos o arquivo existente
    if (existingFileKey) {
      // Buscar o arquivo existente
      const existingFile = await prisma.fileEditor.findFirst({
        where: {
          key: existingFileKey
        }
      });

      if (!existingFile) {
        throw Error("Existing file not found");
      }

      // Upload to MinIO using S3 client with the existing key
      await minioClient.send(
        new PutObjectCommand({
          Bucket: BUCKET_NAME,
          Key: existingFileKey,
          Body: buffer,
          ContentType: mimetype,
          Metadata: {
            version: newVersion.toString()
          }
        })
      );

      // Update file reference in database
      const updatedFile = await prisma.fileEditor.update({
        where: {
          id: existingFile.id
        },
        data: {
          filename,
          mimetype,
          size,
          version: newVersion,
        },
      });

      return {
        id: updatedFile.id
      };
    } else {
      // Criar um novo arquivo
      const uuid = uuidv4();
      const key = uuid;
      const path = orgSlug;
      const minioKey = `${path}/${key}`;

      // Upload to MinIO using S3 client
      await minioClient.send(
        new PutObjectCommand({
          Bucket: BUCKET_NAME,
          Key: minioKey,
          Body: buffer,
          ContentType: mimetype,
          Metadata: {
            version: newVersion.toString()
          }
        })
      );

      // Save file reference to database
      const fileRecord = await prisma.fileEditor.create({
        data: {
          filename,
          mimetype,
          size,
          key,
          version: newVersion,
          bucket: BUCKET_NAME,
          path,
        },
      });

      return {
        id: fileRecord.id
      };
    }
  } catch (error) {
    console.error('Error uploading file:', error);
    throw error;
  }
}

export async function persistFileEditorFromBuffer(buffer: Buffer<any>, fileName: string, fileType: string) {
  try {
    if (!buffer) {
      throw Error("No file uploaded");
    }
    const { organizationName } = await getCurrentOrganization();
    if (!organizationName) throw new Error("Nome da organização não encontrado para salvar arquivo do editor.");
    const orgSlug = toKebabCase(organizationName);
    const uuid = uuidv4();
    const key = uuid;
    const path = orgSlug;
    const minioKey = `${path}/${key}`;
    const size = buffer.length;

    const newVersion = Date.now();

    // Upload to MinIO using S3 client
    await minioClient.send(
      new PutObjectCommand({
        Bucket: BUCKET_NAME,
        Key: minioKey,
        Body: buffer,
        ContentType: fileType,
        Metadata: {
          version: newVersion.toString()
        }
      })
    );

    // Save file reference to database
    const fileRecord = await prisma.fileEditor.create({
      data: {
        filename: fileName,
        mimetype: fileType,
        size,
        key,
        version: newVersion,
        bucket: BUCKET_NAME,
        path,
      },
    });

    return {
      id: fileRecord.id
    };

  } catch (error) {
    console.error('Error uploading file:', error);
  }
}