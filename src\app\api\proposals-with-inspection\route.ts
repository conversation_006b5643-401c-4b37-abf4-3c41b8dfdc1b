import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";
import { parseObject } from "@/src/lib/utils";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = Number(searchParams.get("page")) || 1;
    const pageSize = Number(searchParams.get("pageSize")) || 10;
    const search = searchParams.get("search") || "";
    const customerId = searchParams.get("customerId") || undefined;

    const { organizationId } = await getCurrentOrganization();

    const skip = (page - 1) * pageSize;

    const where: any = {
      customer: {
        organizationId,
      },
      inspectionParameters: {
        some: {},
      },
      // Filtrar apenas contratos concluídos ou em andamento
      situation: {
        in: [
          "PROJECT_IN_PROGRESS",
          "PROJECT_FINISHED",
        ],
      },
      // Filtrar apenas contratos do tipo Inspeção ou Consultoria
      serviceType: {
        in: ["INSPECAO", "CONSULTORIA"],
      },
      ...(customerId && { customerId }),
      ...(search
        ? {
            OR: [
              { name: { contains: search, mode: "insensitive" } },
              { customer: { name: { contains: search, mode: "insensitive" } } },
              { customService: { contains: search, mode: "insensitive" } },
            ],
          }
        : {}),
    };

    const [total, items] = await Promise.all([
      prisma.proposal.count({ where }),
      prisma.proposal.findMany({
        where,
        skip,
        take: pageSize,
        include: {
          customer: true,
          inspectionParameters: {
            include: {
              photos: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      }),
    ]);

    const totalPages = Math.ceil(total / pageSize);

    return NextResponse.json({
      data: parseObject(items),
      page,
      pageSize,
      total,
      totalPages,
    });
  } catch (error) {
    console.error("Error loading proposals with inspection parameters:", error);
    return NextResponse.json(
      { error: "Error loading proposals with inspection parameters" },
      { status: 500 }
    );
  }
}
