"use client";
import ContentWrapper from "@/src/components/content-wrapper";
import { Button } from "@/src/components/ui/button";
import { useState } from "react";
import KpiDashboard from "./components/kpi-dashboard";
import { FileSpreadsheet, FileText } from "lucide-react";
import { ExportReportDialog } from "./components/export-report-dialog";
import PdfExportDialog from "./components/pdf-export-dialog";
import { DateRange } from "react-day-picker";

export default function KPI() {
  const [openExportDialog, setOpenExportDialog] = useState(false);
  const [openPdfDialog, setOpenPdfDialog] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().getFullYear(), 0, 1), // 1º de janeiro do ano atual
    to: new Date(new Date().getFullYear(), 11, 31)  // 31 de dezembro do ano atual
  });

  return (
    <div className="relative">
      <ContentWrapper
        title="Dashboard de Indicadores"
        id="kpi-dashboard"
        header={
          <div className={`flex justify-end gap-2 mt-2 sm:absolute sm:right-4 sm:top-4 sm:mt-0 sm:z-10`}>
            <Button
              className="bg-green-600 hover:bg-green-700 text-white flex items-center gap-2 shadow-md transition-all duration-200 hover:shadow-lg text-xs sm:text-sm"
              onClick={() => setOpenExportDialog(true)}
            >
              <FileSpreadsheet className="h-4 w-4" />
              <span className="hidden sm:inline">Exportar para Excel</span>
              <span className="sm:hidden">Excel</span>
            </Button>
            <Button
              className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 shadow-md transition-all duration-200 hover:shadow-lg text-xs sm:text-sm"
              onClick={() => setOpenPdfDialog(true)}
            >
              <FileText className="h-4 w-4" />
              <span className="hidden sm:inline">Exportar para PDF</span>
              <span className="sm:hidden">PDF</span>
            </Button>
          </div>
        }
      >
        <div className="p-4">
          <KpiDashboard onDateRangeChange={setDateRange} />
        </div>
      </ContentWrapper>
      <ExportReportDialog
        open={openExportDialog}
        onOpenChange={setOpenExportDialog}
        dateRange={dateRange}
      />
      <PdfExportDialog
        open={openPdfDialog}
        onOpenChange={setOpenPdfDialog}
        dateRange={dateRange}
      />
    </div>
  );
}
