import { auth } from "@/src/providers/auth";
import { redirect } from "next/navigation";
import { prisma } from "@/src/lib/prisma";

export default async function Home() {
  const session = await auth();
  
  // If user is not logged in, redirect to auth page
  if (!session?.user) {
    return redirect("/auth");
  }
  
  // Check if user has an active membership
  const membership = await prisma.membership.findFirst({
    where: {
      userId: session.user.id,
      enabled: true
    }
  });
  
  // If no active membership, redirect to welcome page
  if (!membership) {
    return redirect("/welcome");
  }
  
  // If user has an active membership, redirect to control panel
  return redirect("/views/control-panel");
}