"use server";
import { auth } from "@/src/providers/auth";
import { prisma } from "@/src/lib/prisma";
import {
  parseCurrencyToNumber,
  parseObject,
  toKebabCase,
} from "@/src/lib/utils";
import { Periodicity } from "@/src/types/common";
import { Proposal, ProposalSituation } from "@/src/types/core/proposal";

import { replaceFromFileEditorIdAndVariables } from "@/src/helpers/file-editor";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";
import { ProposalSchema } from "@/src/app/views/crm/proposals/_schemas/proposals.schema";
import { removeFile, saveFile } from "./files";
import { removeInspectionParameters } from "./inspection-parameters";
import { removeRepairBudgets } from "./repair-budget";
import { File } from "@/src/types/core/file";

// Função auxiliar para processar propostas antes de enviá-las ao frontend
function processProposals(proposals: any[]) {
  return proposals.map((proposal) => {
    // Processar datas
    const processedProposal = {
      ...proposal,
      // Garantir que as datas sejam válidas
      startDate:
        proposal.startDate instanceof Date &&
        !isNaN(proposal.startDate.getTime())
          ? proposal.startDate
          : null,
      endDate:
        proposal.endDate instanceof Date && !isNaN(proposal.endDate.getTime())
          ? proposal.endDate
          : null,
      createdAt:
        proposal.createdAt instanceof Date &&
        !isNaN(proposal.createdAt.getTime())
          ? proposal.createdAt
          : new Date(),
      updatedAt:
        proposal.updatedAt instanceof Date &&
        !isNaN(proposal.updatedAt.getTime())
          ? proposal.updatedAt
          : new Date(),

      // Garantir que os valores numéricos sejam válidos
      budget:
        typeof proposal.budget === "object" && proposal.budget !== null
          ? Number(proposal.budget.toString())
          : typeof proposal.budget === "number"
          ? proposal.budget
          : 0,
      area:
        typeof proposal.area === "object" && proposal.area !== null
          ? Number(proposal.area.toString())
          : typeof proposal.area === "number"
          ? proposal.area
          : 0,
      downPayment: proposal.downPayment
        ? typeof proposal.downPayment === "object"
          ? Number(proposal.downPayment.toString())
          : typeof proposal.downPayment === "number"
          ? proposal.downPayment
          : 0
        : 0,
      installmentAmount: proposal.installmentAmount
        ? typeof proposal.installmentAmount === "object"
          ? Number(proposal.installmentAmount.toString())
          : typeof proposal.installmentAmount === "number"
          ? proposal.installmentAmount
          : 0
        : 0,
      installmentNumber: proposal.installmentNumber
        ? Number(proposal.installmentNumber)
        : 0,
    };

    return processedProposal;
  });
}

export async function loadProposals(filters?: {
  page?: number;
  pageSize?: number;
  search?: string;
  customerId?: string;
  ignorePermissions?: boolean;
  dateRange?: { from: Date | string; to: Date | string };
  serviceTypes?: string[];
  situation?: ProposalSituation[];
}) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      throw new Error("User not authenticated");
    }

    const { organizationId } = await getCurrentOrganization();
    const userId = session.user.id;
    const isOwner = session.membership?.role === "OWNER";
    const ignorePermissions = filters?.ignorePermissions ?? false;

    // Construir a query base
    const query: any = {
      where: {
        customer: {
          organizationId,
        },
      },
      include: {
        customer: {
          include: {
            contacts: {
              orderBy: {
                date: "desc",
              },
            },
          },
        },
        plannings: true,
        serviceScopes: true,
        proposalTemplate: true,
        file: true,
        contract: true,
        permissions: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    };

    // Adicionar filtros de busca
    if (filters?.search) {
      query.where.OR = [
        { name: { contains: filters.search, mode: "insensitive" } },
        {
          customer: {
            name: { contains: filters.search, mode: "insensitive" },
          },
        },
        {
          customService: { contains: filters.search, mode: "insensitive" },
        },
      ];
    }

    // Adicionar filtro de cliente se fornecido
    if (filters?.customerId) {
      query.where.customerId = filters.customerId;
    }

    // Adicionar filtro de tipo de serviço se fornecido
    if (filters?.serviceTypes && filters.serviceTypes.length > 0) {
      query.where.serviceType = {
        in: filters.serviceTypes,
      };
    }

    // Adicionar filtro de situação se fornecido
    if (filters?.situation) {
      query.where.situation = Array.isArray(filters.situation)
        ? { in: filters.situation }
        : filters.situation;
    }

    // Adicionar filtro de intervalo de datas se fornecido
    if (filters?.dateRange?.from && filters?.dateRange?.to) {
      const fromDate = new Date(filters.dateRange.from);
      const toDate = new Date(filters.dateRange.to);

      // Adicionar um dia ao 'to' para incluir o dia inteiro
      toDate.setDate(toDate.getDate() + 1);

      query.where.createdAt = {
        gte: fromDate,
        lt: toDate,
      };
    }

    // Aplicar paginação apenas se não houver customerId
    if (filters?.page && !filters?.customerId) {
      const page = filters.page || 1;
      const pageSize = filters.pageSize || 10;
      query.skip = (page - 1) * pageSize;
      query.take = pageSize;
    }

    // --- LÓGICA DE PERMISSÃO ---
    if (!isOwner && !ignorePermissions) {
      // Usuário comum: só vê as permitidas, criadas por ele ou sem permissão específica
      query.where = {
        AND: [
          // Deve pertencer à organização
          {
            customer: {
              organizationId,
            },
          },
          // E deve satisfazer uma das condições de permissão
          {
            OR: [
              // Propostas que o usuário tem permissão
              {
                permissions: {
                  some: {
                    userId: userId,
                    organizationId: organizationId,
                  },
                },
              },
              // Propostas criadas pelo usuário
              {
                createdBy: userId,
              },
            ],
          },
        ],
      };
    }
    // --- FIM LÓGICA DE PERMISSÃO ---

    // Buscar propostas
    const [proposals, total] = await Promise.all([
      prisma.proposal.findMany(query),
      prisma.proposal.count({ where: query.where }),
    ]);

    // Processar os resultados
    const processedProposals = proposals.map((proposal) => ({
      ...proposal,
      startDate: proposal.startDate ? new Date(proposal.startDate) : null,
      endDate: proposal.endDate ? new Date(proposal.endDate) : null,
    }));

    // Se não houver customerId, retornar com informações de paginação
    if (!filters?.customerId) {
      return {
        data: processedProposals,
        pagination: {
          total,
          page: filters?.page || 1,
          pageSize: filters?.pageSize || 10,
        },
      };
    }

    // Se houver customerId, retornar apenas os dados
    return processedProposals;
  } catch (error) {
    console.error("Error loading proposals:", error);
    throw error;
  }
}

export async function findProposal(id: string) {
  try {
    const session = await auth();
    const userId = session?.user?.id;
    const { organizationId } = await getCurrentOrganization();

    console.log("[findProposal] id recebido:", id);
    console.log("[findProposal] userId:", userId);
    console.log("[findProposal] organizationId:", organizationId);

    // Construir a query base
    let where: any = {
      id,
      customer: {
        organizationId,
      },
    };

    // Se o usuário for OWNER, retorna sempre
    const isOwner = session?.membership?.role === "OWNER";
    if (userId && !isOwner) {
      where = {
        AND: [
          {
            id,
          },
          {
            customer: {
              organizationId,
            },
          },
          {
            OR: [
              // Propostas que o usuário tem permissão
              {
                permissions: {
                  some: {
                    userId: userId,
                    organizationId: organizationId,
                  },
                },
              },
              // Propostas criadas pelo usuário
              {
                createdBy: userId,
              },
              // Propostas que não têm permissões específicas
              {
                permissions: {
                  none: {},
                },
              },
            ],
          },
        ],
      };
    }
    console.log("[findProposal] filtro where:", JSON.stringify(where, null, 2));

    const data = await prisma.proposal.findFirst({
      where,
      include: {
        customer: true,
        serviceScopes: true,
        plannings: {
          orderBy: {
            order: "asc",
          },
        },
        attachments: {
          include: {
            file: true,
          },
          orderBy: {
            file: {
              uploadedAt: "desc",
            },
          },
        },
      },
    });

    console.log("[findProposal] resultado do Prisma:", data);

    // Processar a proposta antes de enviá-la ao frontend
    if (data) {
      const processedData = processProposals([data])[0];
      return parseObject(processedData) as Proposal;
    }

    return null;
  } catch (error) {
    console.error("[findProposal] erro:", error);
  }
}

// Alias para findProposal para compatibilidade com o código existente
export const getProposalById = findProposal;

async function logProposalStatusChange(
  proposalId: string,
  oldStatus: ProposalSituation,
  newStatus: ProposalSituation
) {
  await prisma.logProposal.create({
    data: {
      proposalId,
      oldStatus,
      newStatus,
    },
  });
}

export async function saveProposal(
  proposalData: ProposalSchema,
  forceRegenerateFile: boolean = false
) {
  console.log("[LOG] Início de saveProposal", {
    proposalData,
    forceRegenerateFile,
  });
  try {
    const session = await auth();
    const userId = session?.user?.id;
    const { organizationId } = await getCurrentOrganization();
    console.log("[LOG] Sessão e organização obtidas", {
      userId,
      organizationId,
    });

    const customer = await prisma.customer.findUnique({
      where: {
        id: proposalData.customerId,
        organizationId,
      },
    });
    console.log("[LOG] Resultado do findUnique customer", { customer });

    if (!customer) {
      console.error("[ERRO] Cliente não encontrado", {
        customerId: proposalData.customerId,
        organizationId,
      });
      throw new Error("Cliente não encontrado");
    }

    const proposalTemplate = await prisma.proposalTemplate.findUnique({
      where: { id: proposalData.proposalTemplateId },
      include: { fileEditor: true },
    });
    console.log("[LOG] Resultado do findUnique proposalTemplate", {
      proposalTemplate,
    });

    const serviceScopes = await prisma.serviceScope.findMany({
      where: { id: { in: proposalData.serviceScopes } },
    });
    console.log("[LOG] Resultado do findMany serviceScopes", { serviceScopes });

    if (customer && serviceScopes && proposalTemplate) {
      const where = { id: proposalData.id };
      const include = {
        plannings: true,
        serviceScopes: true,
        customer: true,
        file: true,
      };

      const {
        name,
        startDate,
        endDate,
        budget,
        paymentCondition,
        situation,
        periodicity,
        plannings,
        installmentAmount,
        installmentNumber,
        area,
        cep,
        city,
        state,
        address,
        methodology,
        downPayment,
        serviceType,
        customService,
      } = proposalData;

      // Adicionar logs antes de cada operação crítica
      console.log("[LOG] Dados base para proposta", {
        name,
        startDate,
        endDate,
        budget,
        paymentCondition,
        situation,
        periodicity,
        plannings,
        installmentAmount,
        installmentNumber,
        area,
        cep,
        city,
        state,
        address,
        methodology,
        downPayment,
        serviceType,
        customService,
      });

      // Preparar dados base
      const data = {
        name,
        startDate,
        endDate,
        budget: parseCurrencyToNumber(budget.toString()),
        workTotalCost: parseCurrencyToNumber(
          proposalData.workTotalCost?.toString() || "0"
        ), // Usar workTotalCost se existir, senão deixar zerado
        cep,
        city,
        state,
        address,
        methodology,
        customService,
        area: Number(area),
        paymentCondition,
        downPayment: parseCurrencyToNumber(downPayment?.toString() || "0"),
        situation: situation as ProposalSituation,
        periodicity: periodicity as Periodicity,
        installmentAmount: parseCurrencyToNumber(
          installmentAmount?.toString() || "0"
        ),
        installmentNumber: installmentNumber
          ? Number(installmentNumber)
          : undefined,
        customer: { connect: { id: customer.id } },
        proposalTemplate: { connect: { id: proposalTemplate.id } },
        serviceScopes: {
          connect: serviceScopes.map(({ id }) => ({
            id,
          })),
        },
      } as any;

      // Adicionar serviceType de forma segura
      if (typeof serviceType === "string" && serviceType.trim() !== "") {
        data.serviceType = serviceType.trim();
        console.log(`Definindo serviceType: ${serviceType.trim()}`);
      } else {
        console.log("serviceType não definido ou inválido:", serviceType);
      }

      let proposal;

      // Adicionar logs antes de criar/atualizar proposta
      if (proposalData.id) {
        console.log("[LOG] Atualizando proposta existente", {
          id: proposalData.id,
        });
      } else {
        console.log("[LOG] Criando nova proposta");
      }

      let currentSituation: ProposalSituation | undefined;

      if (proposalData.id) {
        // Buscar a situação atual da proposta antes de atualizar
        const existingProposal = await prisma.proposal.findUnique({
          where: { id: proposalData.id },
          select: { situation: true },
        });

        if (existingProposal) {
          currentSituation = existingProposal.situation as ProposalSituation;
        }

        // Atualização de proposta existente
        // Primeiro, deletar plannings existentes
        await prisma.planningFrequencyItem.deleteMany({
          where: { proposalId: proposalData.id },
        });

        // Depois, atualizar a proposta com novos plannings
        proposal = await prisma.proposal.update({
          where,
          data: {
            ...data,
            plannings: {
              create: plannings!.map((planning) => ({
                order: planning.order,
                content: planning.content,
                label: planning.label,
              })),
            },
          },
          include,
        });
      } else {
        // Criação de nova proposta
        proposal = await prisma.proposal.create({
          data: {
            ...data,
            creator: { connect: { id: userId } },
            plannings: {
              create: plannings!.map((planning) => ({
                order: planning.order,
                content: planning.content,
                label: planning.label,
              })),
            },
          },
          include,
        });
      }

      // Só gerar um novo arquivo se forceRegenerateFile for true ou se não existir fileEditorId
      const shouldGenerateFile = forceRegenerateFile || !proposal.fileEditorId;

      if (shouldGenerateFile) {
        console.log("[LOG] Gerando novo arquivo", {
          forceRegenerateFile,
          hasExistingFile: !!proposal.fileEditorId,
          reason: forceRegenerateFile
            ? "Forçado pelo usuário"
            : "Arquivo não existe",
        });

        // Gerar variáveis para o template usando a função auxiliar
        const variables = await generateProposalTemplateVariables(proposal);
        // Adicionar logs antes de gerar arquivo
        console.log("[LOG] Gerando variáveis para template");
        // Substituir as variáveis no template
        const fileName = `${Date.now()}-proposta-${proposalTemplate.title}`;
        const replacedFileEditor = await replaceFromFileEditorIdAndVariables(
          `${proposalTemplate?.fileEditorId}`,
          variables,
          fileName
        );
        // Adicionar logs antes de substituir variáveis no template
        console.log("[LOG] Substituindo variáveis no template", {
          fileEditorId: proposalTemplate?.fileEditorId,
          fileName,
        });

        if (replacedFileEditor) {
          proposal = await prisma.proposal.update({
            where: { id: proposal.id },
            data: { fileEditorId: replacedFileEditor.id },
            include,
          });
          // Adicionar logs antes de atualizar proposal com fileEditorId
          console.log("[LOG] Atualizando proposal com fileEditorId", {
            fileEditorId: replacedFileEditor.id,
          });
        }
      } else {
        console.log("[LOG] Mantendo arquivo existente", {
          fileEditorId: proposal.fileEditorId,
        });
      }

      // Se houver mudança de situação, registrar no log
      if (currentSituation && currentSituation !== proposalData.situation) {
        console.log("[LOG] Registrando mudança de situação", {
          proposalId: proposal.id,
          de: currentSituation,
          para: proposalData.situation,
        });
        await logProposalStatusChange(
          proposal.id,
          currentSituation,
          proposalData.situation as ProposalSituation
        );
      }

      console.log("[LOG] Proposta salva com sucesso", { proposal });
      return parseObject(proposal) as Proposal;
    } else {
      console.error("[ERRO] Dados obrigatórios faltando", {
        customer,
        serviceScopes,
        proposalTemplate,
      });
    }
  } catch (error) {
    console.error("[ERRO] Exceção em saveProposal", error, { proposalData });
    throw new Error("Erro ao salvar proposta");
  }
}

export async function updateProposalsPositions(proposals: Proposal[]) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const updatedProposals = await Promise.all(
      proposals.map(async (proposal) => {
        try {
          const currentProposal = await prisma.proposal.findUnique({
            where: { id: proposal.id },
            select: { situation: true },
          });

          // Verificar se a proposta está sendo movida para "Proposta aceita"
          const isMovingToAccepted =
            currentProposal &&
            currentProposal.situation !== "PROPOSAL_ACCEPTED" &&
            proposal.situation === "PROPOSAL_ACCEPTED";

          // Preparar os dados para atualização
          const updateData = {
            order: proposal.order,
            situation: proposal.situation as ProposalSituation,
          };

          console.log(`Atualizando proposta ${proposal.id}:`, {
            situacaoAtual: currentProposal?.situation,
            novaSituacao: proposal.situation,
            isMovingToAccepted,
          });

          // Atualizar a proposta
          const updatedProposal = await prisma.proposal.update({
            where: {
              id: proposal.id,
              customer: {
                organizationId,
              },
            },
            data: updateData,
          });

          // Se a proposta está sendo movida para "Proposta aceita", registrar no log
          if (isMovingToAccepted) {
            console.log(
              `Proposta ${proposal.id} movida para PROPOSAL_ACCEPTED. O usuário deve selecionar um template de contrato.`
            );
          }

          // Se houver mudança de situação, registrar no log
          if (
            currentProposal &&
            currentProposal.situation !== proposal.situation
          ) {
            await logProposalStatusChange(
              proposal.id,
              currentProposal.situation,
              proposal.situation as ProposalSituation
            );
          }

          // Chama a função para verificar e criar contrato se necessário
          await createContractForProposal(
            updatedProposal.id,
            updatedProposal.situation
          );

          return updatedProposal;
        } catch (error) {
          console.error(error);
          return null;
        }
      })
    );

    return parseObject(updatedProposals) as Proposal[];
  } catch (error) {
    console.error(error);
  }
}

export async function createContractForProposal(
  proposalId: string,
  situation: ProposalSituation
) {
  const eligibleSituations: ProposalSituation[] = [
    "PROPOSAL_ACCEPTED",
    "SIGN_REQUESTED",
    "SIGNED",
    "PROJECT_IN_PROGRESS",
    "PROJECT_FINISHED",
  ];

  if (!eligibleSituations.includes(situation)) return;

  try {
    // Buscar a proposta com todos os dados necessários
    const proposal = await prisma.proposal.findUnique({
      where: { id: proposalId },
      include: {
        customer: true,
        proposalTemplate: true,
        file: true,
        contract: true,
      },
    });

    if (!proposal) {
      console.error(`Proposta não encontrada: ${proposalId}`);
      return;
    }

    // Verificar se já existe um contrato para essa proposta
    if (proposal.contract) {
      // Se já existe um contrato mas não tem arquivo, gerar o arquivo
      if (!proposal.contract.fileEditorId) {
        // Gerar variáveis para o template usando a função auxiliar
        const variables = await generateProposalTemplateVariables(proposal);

        // Buscar o template de contrato
        const contractTemplate = await prisma.proposalTemplate.findFirst({
          where: {
            type: "CONTRACT",
            ...(proposal.proposalTemplateId
              ? { id: proposal.proposalTemplateId }
              : {}),
          },
        });

        if (contractTemplate && contractTemplate.fileEditorId) {
          // Substituir as variáveis no template de contrato
          const fileName = `${Date.now()}-contrato-${proposal.name}`;
          const replacedFileEditor = await replaceFromFileEditorIdAndVariables(
            contractTemplate.fileEditorId,
            variables,
            fileName
          );

          if (replacedFileEditor) {
            // Atualizar o contrato com o arquivo gerado
            await prisma.contract.update({
              where: { id: proposal.contract.id },
              data: { fileEditorId: replacedFileEditor.id },
            });

            console.log(
              `Arquivo de contrato gerado com sucesso para a proposta ${proposalId}`
            );
          }
        }
      }
    } else {
      // Criar um novo contrato
      const newContract = await prisma.contract.create({
        data: {
          proposalId,
        },
      });

      // Gerar o arquivo de contrato
      // Gerar variáveis para o template usando a função auxiliar
      const variables = await generateProposalTemplateVariables(proposal);

      // Buscar o template de contrato
      const contractTemplate = await prisma.proposalTemplate.findFirst({
        where: {
          type: "CONTRACT",
          ...(proposal.proposalTemplateId
            ? { id: proposal.proposalTemplateId }
            : {}),
        },
      });

      if (contractTemplate && contractTemplate.fileEditorId) {
        // Substituir as variáveis no template de contrato
        const fileName = `${Date.now()}-contrato-${proposal.name}`;
        const replacedFileEditor = await replaceFromFileEditorIdAndVariables(
          contractTemplate.fileEditorId,
          variables,
          fileName
        );

        if (replacedFileEditor) {
          // Atualizar o contrato com o arquivo gerado
          await prisma.contract.update({
            where: { id: newContract.id },
            data: { fileEditorId: replacedFileEditor.id },
          });

          console.log(
            `Arquivo de contrato gerado com sucesso para a proposta ${proposalId}`
          );
        }
      }
    }
  } catch (error) {
    console.error(
      `Erro ao criar contrato para a proposta ${proposalId}:`,
      error
    );
  }
}

export async function removeProposal(id: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const proposal = await prisma.proposal.findUnique({
      where: {
        id,
        customer: {
          organizationId,
        },
      },
      include: { file: true, repairBudgets: true, inspectionParameters: true },
    });

    if (!proposal) {
      throw new Error("Proposta não existe ou não pertence a sua organização");
    }

    // Excluir todos os registros relacionados à proposta
    await prisma.planningFrequencyItem.deleteMany({
      where: { proposalId: id },
    });
    await prisma.productivity.deleteMany({
      where: { proposalId: id },
    });
    await prisma.contract.deleteMany({
      where: { proposalId: id },
    });
    // Excluir logs de proposta para evitar violação de chave estrangeira
    await prisma.logProposal.deleteMany({
      where: { proposalId: id },
    });

    if (proposal.file) {
      await removeFile(proposal.file);
    }

    if (proposal.repairBudgets.length) {
      await removeRepairBudgets(id);
    }

    if (proposal.inspectionParameters.length) {
      await removeInspectionParameters(id);
    }

    await prisma.proposal.delete({ where: { id } });

    return { message: "Proposta removida com sucesso!" };
  } catch (error) {
    console.error(error);
  }
}

export async function fetchProposalsGroupedByDate() {
  try {
    const { organizationId } = await getCurrentOrganization();

    const data = await prisma.proposal.groupBy({
      by: ["startDate"],
      where: {
        customer: {
          organizationId,
        },
      },
      _count: { id: true },
      orderBy: { startDate: "asc" },
    });

    // Processar as datas antes de enviá-las ao frontend
    const processedData = data.map((item) => ({
      ...item,
      startDate:
        item.startDate instanceof Date && !isNaN(item.startDate.getTime())
          ? item.startDate
          : null,
    }));

    return parseObject(processedData) as Proposal[];
  } catch (error) {
    console.error(error);
  }
}

// Função para formatar datas no padrão DD/MM/YYYY
function formatDate(date: number | string) {
  return new Intl.DateTimeFormat("pt-BR").format(new Date(date));
}

// Função para formatar valores monetários no padrão R$ 9.999,99
function formatCurrency(value: number) {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(value);
}

/**
 * Função auxiliar para gerar variáveis para templates de proposta
 * Esta função facilita a adição de novas variáveis no futuro
 */
import { currencyToWords } from "@/src/lib/number-to-words";

// Função auxiliar para tratar campos de endereço do cliente
function infoOuNaoInformado(valor: string | undefined, nome: string) {
  return valor && valor.trim() !== "" ? valor : `[${nome}] não informado`;
}

export async function generateProposalTemplateVariables(proposal: any) {
  // Calcular o número de dias entre a data de início e fim
  const startDateObj = new Date(proposal.startDate);
  const endDateObj = new Date(proposal.endDate);
  const diffTime = Math.abs(endDateObj.getTime() - startDateObj.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  // Calcular 80% do valor da proposta
  const valor80Porcento = Number(proposal.budget) * 0.8;

  // Converter valores para texto por extenso
  const valorPropostaExtenso = await currencyToWords(
    Number(proposal.budget) || 0
  );
  const valorParcelaExtenso = await currencyToWords(
    Number(proposal.installmentAmount) || 0
  );
  const valorEntradaExtenso = await currencyToWords(
    Number(proposal.downPayment) || 0
  );
  const valor80PorcentoExtenso = await currencyToWords(valor80Porcento);

  // Formatar endereço completo com CEP formatado
  const enderecoCompleto = `${proposal.address || ""}, CEP: ${
    proposal.cep || ""
  }, ${proposal.city || ""}/${proposal.state || ""}`;

  // Montar endereço do cliente com tratamento de campos não informados
  const enderecoCliente = proposal.customer
    ? `${infoOuNaoInformado(
        proposal.customer.address,
        "Endereço"
      )}, CEP: ${infoOuNaoInformado(
        proposal.customer.cep,
        "CEP"
      )}, ${infoOuNaoInformado(
        proposal.customer.city,
        "Cidade"
      )}/${infoOuNaoInformado(proposal.customer.state, "Estado")}`
    : "[Endereço, CEP, Cidade, Estado] não informado";

  // Extrair metodologias como lista (array de strings)
  let metodologias: string[] = [];
  if (proposal.methodology) {
    if (Array.isArray(proposal.methodology)) {
      metodologias = proposal.methodology
        .map((item: any) => String(item).trim())
        .filter((item: string) => item.length > 0);
    } else if (typeof proposal.methodology === "string") {
      // Aceita separação por linha, vírgula ou ponto e vírgula
      metodologias = proposal.methodology
        .split(/\r?\n|,|;/)
        .map((item: string) => item.trim())
        .filter((item: string) => item.length > 0);
    }
  }

  // Adicionar log para depuração
  console.log(
    "[LOG] metodologias geradas:",
    metodologias,
    "Tipo:",
    Array.isArray(metodologias) ? "array" : typeof metodologias
  );

  // Mapeamento das variáveis
  return {
    // Informações da proposta
    proposta: proposal.name,
    nome_proposta: proposal.name,
    orcamento: proposal.budget
      ? formatCurrency(Number(proposal.budget))
      : "R$ 0,00",
    valor_proposta: proposal.budget
      ? formatCurrency(Number(proposal.budget))
      : "R$ 0,00",
    valor_proposta_extenso: valorPropostaExtenso,
    valor_80_porcento: formatCurrency(valor80Porcento),
    valor_80_porcento_extenso: valor80PorcentoExtenso,

    // Informações de pagamento
    entrada: proposal.downPayment
      ? formatCurrency(Number(proposal.downPayment))
      : "R$ 0,00",
    valor_entrada: proposal.downPayment
      ? formatCurrency(Number(proposal.downPayment))
      : "R$ 0,00",
    valor_entrada_extenso: valorEntradaExtenso,
    num_parcelas: proposal.installmentNumber || "0",
    quantidade_parcelas: proposal.installmentNumber || "0",
    valor_parcela: proposal.installmentAmount
      ? formatCurrency(Number(proposal.installmentAmount))
      : "R$ 0,00",
    valor_parcela_extenso: valorParcelaExtenso,

    // Informações de prazo
    prazo_dias: diffDays.toString(),
    data_inicio: formatDate(proposal.startDate),
    data_fim: formatDate(proposal.endDate),

    // Informações do cliente
    nome_cliente: proposal.customer?.name || "",
    nome_cliente_maiusculo: (proposal.customer?.name || "").toUpperCase(),
    cpf_cnpj: proposal.customer?.document || "",
    endereco: enderecoCompleto,
    endereco_completo: enderecoCompleto,
    endereco_maiusculo: enderecoCompleto.toUpperCase(),
    endereco_cliente: enderecoCliente,
    endereco_cliente_maiusculo: enderecoCliente.toUpperCase(),

    // Outras informações
    data_hoje: formatDate(Date.now()),
    data_hoje_extenso: formatDate(Date.now()), // Formato por extenso: dia de mês de ano
    metodologias,
    metodologias_lista_ul: metodologias.length
      ? metodologias.map((m) => `• ${m}`).join("\n")
      : "",
  };
}

// Funções para gerenciar anexos da proposta
export async function addProposalAttachments(
  proposalId: string,
  files: File[]
) {
  try {
    const { organizationId, organizationName } = await getCurrentOrganization();
    if (!organizationName)
      throw new Error("Nome da organização não encontrado para salvar anexo.");
    const orgSlug = toKebabCase(organizationName);

    // Verificar se a proposta existe e pertence à organização
    const proposal = await prisma.proposal.findUnique({
      where: {
        id: proposalId,
        customer: {
          organizationId,
        },
      },
    });

    if (!proposal) {
      throw new Error(
        "Proposta não encontrada ou não pertence à sua organização"
      );
    }

    // Salvar os arquivos
    const savedFiles: File[] = [];
    for (const file of files) {
      const filePath = `${orgSlug}/proposals/${proposalId}/attachments`;
      const savedFile = await saveFile({
        name: file.name,
        path: filePath,
        type: file.type,
        size: file.size,
        buffer: file.buffer,
      } as File);

      if (savedFile) {
        // Criar a relação entre proposta e arquivo
        await prisma.proposalAttachment.create({
          data: {
            proposalId,
            fileId: savedFile.id,
          },
        });
        savedFiles.push(savedFile as File);
      }
    }

    return savedFiles;
  } catch (error) {
    console.error("Erro ao adicionar anexos à proposta:", error);
    throw error;
  }
}

export async function removeProposalAttachment(
  proposalId: string,
  fileId: string
) {
  try {
    const { organizationId } = await getCurrentOrganization();

    // Verificar se a proposta existe e pertence à organização
    const proposal = await prisma.proposal.findUnique({
      where: {
        id: proposalId,
        customer: {
          organizationId,
        },
      },
    });

    if (!proposal) {
      throw new Error(
        "Proposta não encontrada ou não pertence à sua organização"
      );
    }

    // Buscar o anexo
    const attachment = await prisma.proposalAttachment.findUnique({
      where: {
        proposalId_fileId: {
          proposalId,
          fileId,
        },
      },
      include: {
        file: true,
      },
    });

    if (!attachment) {
      throw new Error("Anexo não encontrado");
    }

    // Remover a relação
    await prisma.proposalAttachment.delete({
      where: {
        proposalId_fileId: {
          proposalId,
          fileId,
        },
      },
    });

    // Remover o arquivo
    if (attachment.file) {
      await removeFile(attachment.file);
    }

    return { message: "Anexo removido com sucesso!" };
  } catch (error) {
    console.error("Erro ao remover anexo da proposta:", error);
    throw error;
  }
}

export async function getProposalAttachments(proposalId: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    // Verificar se a proposta existe e pertence à organização
    const proposal = await prisma.proposal.findUnique({
      where: {
        id: proposalId,
        customer: {
          organizationId,
        },
      },
    });

    if (!proposal) {
      throw new Error(
        "Proposta não encontrada ou não pertence à sua organização"
      );
    }

    // Buscar os anexos
    const attachments = await prisma.proposalAttachment.findMany({
      where: {
        proposalId,
      },
      include: {
        file: true,
      },
      orderBy: {
        file: {
          uploadedAt: "desc",
        },
      },
    });

    return parseObject(attachments);
  } catch (error) {
    console.error("Erro ao buscar anexos da proposta:", error);
    throw error;
  }
}
