"use client";

import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/src/components/ui/chart";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card";
import { useEffect, useState, useMemo } from "react";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";
import { DateRange } from "react-day-picker";

interface RevenueChartProps {
  data: any[];
  dateRange?: DateRange;
  isMobile?: boolean;
}

export default function RevenueChart({ data, dateRange, isMobile = false }: RevenueChartProps) {
  const [maxTotalIncome, setMaxTotalIncome] = useState<number>(0);

  const chartConfig: ChartConfig = {
    totalIncome: {
      label: "Faturamento",
      color: "rgb(22 163 74)",
    },
  };

  // Filtrar dados com base no intervalo de datas selecionado
  const filteredData = useMemo(() => {
    if (!data) return [];

    // Se não houver intervalo de datas, mostrar todos os dados
    if (!dateRange?.from && !dateRange?.to) return data;

    return data.filter(item => {
      const itemDate = new Date(item.year, item.month - 1, 15); // Dia 15 do mês para evitar problemas com dias no fim/início do mês

      if (dateRange.from && dateRange.to) {
        return itemDate >= dateRange.from && itemDate <= dateRange.to;
      } else if (dateRange.from) {
        return itemDate >= dateRange.from;
      } else if (dateRange.to) {
        return itemDate <= dateRange.to;
      }

      return true;
    });
  }, [data, dateRange]);

  // Determinar o intervalo adequado para os rótulos do eixo X
  const xAxisInterval = useMemo(() => {
    // Se tivermos muitos pontos de dados, aumentamos o intervalo
    if (filteredData.length > 6) {
      return Math.ceil(filteredData.length / 6);
    }
    // Para dispositivos móveis, sempre mostramos menos rótulos
    return isMobile ? 1 : 0;
  }, [filteredData.length, isMobile]);

  useEffect(() => {
    if (filteredData && filteredData.length > 0) {
      const maxIncome = Math.max(...filteredData.map((item: any) => item.totalIncome));
      setMaxTotalIncome(maxIncome);
    }
  }, [filteredData]);

  // Calcular o faturamento total apenas com os dados filtrados
  const totalRevenue = filteredData?.reduce((sum, item) => sum + item.totalIncome, 0) || 0;

  // Determinar o título com base no intervalo de datas
  const chartTitle = useMemo(() => {
    if (!dateRange?.from && !dateRange?.to) return "Faturamento Mensal";

    if (dateRange.from && dateRange.to) {
      // Se o intervalo está no mesmo ano, mostrar apenas o ano
      if (dateRange.from.getFullYear() === dateRange.to.getFullYear()) {
        return `Faturamento Mensal ${dateRange.from.getFullYear()}`;
      }
      // Se o intervalo abrange anos diferentes, mostrar o intervalo de anos
      return `Faturamento Mensal ${dateRange.from.getFullYear()} - ${dateRange.to.getFullYear()}`;
    }

    if (dateRange.from) return `Faturamento Mensal desde ${dateRange.from.getFullYear()}`;
    if (dateRange.to) return `Faturamento Mensal até ${dateRange.to.getFullYear()}`;

    return "Faturamento Mensal";
  }, [dateRange]);

  return (
    <Card className="w-full" id="revenue-chart">
      <CardHeader className="pb-2">
        <CardTitle className={`${isMobile ? 'text-lg' : 'text-xl'} font-bold`}>{chartTitle}</CardTitle>
        <CardDescription className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
          <span className="text-xs sm:text-sm">Valor total dos contratos assinados por mês no período selecionado</span>
          <span className="text-base sm:text-lg font-semibold text-green-600 mt-1 sm:mt-0">
            R$ {totalRevenue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
          </span>
        </CardDescription>
      </CardHeader>
      <CardContent className={`${isMobile ? 'h-64' : 'h-96'}`}>
        {filteredData.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <p className="text-gray-500">Não há dados disponíveis para o período selecionado</p>
          </div>
        ) : (
          <ChartContainer
            config={chartConfig}
            className="h-full w-full"
          >
            <AreaChart
              data={filteredData}
              margin={isMobile ? { top: 5, right: 5, left: 0, bottom: 5 } : { top: 10, right: 30, left: 0, bottom: 5 }}
            >
              <defs>
                <linearGradient id="colorTotalIncome" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="rgb(22 163 74)" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="rgb(22 163 74)" stopOpacity={0.1} />
                </linearGradient>
              </defs>
              <CartesianGrid vertical={false} strokeDasharray="3 3" />
              <XAxis
                dataKey="monthYear"
                tickLine={false}
                tickMargin={isMobile ? 5 : 10}
                axisLine={true}
                tick={{ fontSize: isMobile ? 10 : 12 }}
                interval={xAxisInterval}
              />
              <YAxis
                type="number"
                tickLine={true}
                axisLine={true}
                tickMargin={isMobile ? 5 : 10}
                width={isMobile ? 60 : 100}
                domain={[0, maxTotalIncome * 1.1]}
                tickFormatter={(value) => `R$ ${value.toLocaleString('pt-BR', { notation: 'compact', compactDisplay: 'short' })}`}
                tick={{ fontSize: isMobile ? 10 : 12 }}
              />
              <ChartTooltip
                content={<ChartTooltipContent currencyLines={['totalIncome']} />}
                cursor={false}
              />
              <ChartLegend content={<ChartLegendContent />} />
              <Area
                type="monotone"
                dataKey="totalIncome"
                name="Faturamento"
                stroke="var(--color-totalIncome)"
                fillOpacity={1}
                fill="url(#colorTotalIncome)"
              />
            </AreaChart>
          </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
}
