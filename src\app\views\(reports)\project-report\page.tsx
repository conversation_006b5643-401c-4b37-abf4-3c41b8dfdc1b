"use client";
import ContentWrapper from "@/src/components/content-wrapper";
import { formatDate, cn } from "@/src/lib/utils";
import { FileSearch } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { proposalSituations, serviceTypeOptions } from "@/src/constants";
import { Proposal } from "@/src/types/core/proposal";

import ProjectReportTable, { ProjectReportTableRef } from "./components/project-report-table";
import { useRouter } from "next/navigation";

export default function ProjectReport() {
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [isNavigating, setIsNavigating] = useState(false);
  const tableRef = useRef<ProjectReportTableRef>(null);
  const router = useRouter();

  const fetchProposals = async () => {
    try {
      const response = await fetch('/api/proposals-with-project');
      if (!response.ok) {
        throw new Error('Failed to fetch proposals');
      }
      const data = await response.json();
      if (data?.data) {
        setProposals(data.data);
      }
    } catch (error) {
      console.error('Error fetching proposals:', error);
    }
  };

  useEffect(() => {
    fetchProposals();
  }, []);

  const handleNavigation = (url: string) => {
    setIsNavigating(true);
    router.push(url);
  };

  const handleViewInspections = (proposalId: string) => {
    handleNavigation(`/views/project-report/${proposalId}`);
  };

  const columns = [
    {
      key: "customer",
      header: "Cliente",
      cell: (row: any) => row.customer?.name || "N/A",
      sortable: true,
      filterable: true,
      filterOptions: proposals.map(({ customer }) => ({
        label: customer?.name || "N/A",
        value: customer?.id || "",
      })),
    },
    {
      key: "name",
      header: "Titulo",
      cell: (row: any) => row.name || "N/A",
      sortable: true,
    },
    {
      key: "startDate",
      header: "Data início",
      cell: (row: any) => formatDate(row.startDate, "DATE"),
      sortable: true,
    },
    {
      key: "endDate",
      header: "Data fim prevista",
      cell: (row: any) => formatDate(row.endDate, "DATE"),
      sortable: true,
    },
    {
      key: "serviceType",
      header: "Tipo de Serviço",
      cell: (row: any) => {
        const serviceTypeLabel = serviceTypeOptions.find(
          (option) => option.value === row.serviceType
        )?.label || row.serviceType || '';

        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-600">
            {serviceTypeLabel}
          </span>
        );
      },
      sortable: true,
    },
    {
      key: "situation",
      header: "Situação",
      cell: (row: any) => {
        const rowValue = row.situation;
        let badgeClass = "";

        switch (rowValue) {
          case "NEW":
            badgeClass = "bg-blue-100 text-blue-500";
            break;
          case "UNDER_ANALYSIS":
            badgeClass = "bg-purple-100 text-purple-500";
            break;
          case "PROPOSAL_SENT":
            badgeClass = "bg-indigo-100 text-indigo-500";
            break;
          case "PROPOSAL_ACCEPTED":
            badgeClass = "bg-green-100 text-green-500";
            break;
          case "SIGN_REQUESTED":
            badgeClass = "bg-orange-100 text-orange-500";
            break;
          case "SIGNED":
            badgeClass = "bg-emerald-100 text-emerald-500";
            break;
          case "PROJECT_IN_PROGRESS":
            badgeClass = "bg-amber-100 text-amber-600";
            break;
          case "PROJECT_FINISHED":
            badgeClass = "bg-teal-100 text-teal-600";
            break;
          case "LOST":
            badgeClass = "bg-red-100 text-red-500";
            break;
          default:
            badgeClass = "bg-gray-100 text-gray-500";
        }

        return (
          <span
            className={cn(
              "p-1 rounded-md font-medium text-xs",
              badgeClass
            )}
          >
            {
              proposalSituations.find(
                (situation) => situation.value == rowValue
              )?.label || rowValue
            }
          </span>
        );
      },
      sortable: true,
    },
    // {
    //   key: "actions",
    //   header: "Ações",
    //   cell: (row: any) => (
    //     <div className="flex justify-start">
    //       <button
    //         className="p-2 rounded-md hover:bg-gray-100"
    //         onClick={() => handleViewReport(row.id)}
    //         title="Gerar Relatório de Projeto"
    //       >
    //         <FileText className="size-5 text-yellow-500" />
    //       </button>
    //     </div>
    //   ),
    // },
    {
      key: "actions",
      header: "Ações",
      cell: (row) => (
        <div className="flex gap-3">
          <FileSearch
            className="size-5 text-yellow-500 cursor-pointer"
            onClick={() => handleViewInspections(row.id)}
          />
        </div>
      ),
    },
  ];

  return (
    <ContentWrapper title="Relatório de Projeto" loading={isNavigating}>
      <ProjectReportTable
        ref={tableRef}
        columns={columns}
        customers={proposals.map(p => p.customer).filter(Boolean) as any[]}
        onViewClick={() => { }}
        onPageChange={() => { }}
      />
    </ContentWrapper>
  );
}
