"use client";

import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import { Fragment, useState } from "react";
import { breadcrumbs } from "../constants";
import { RouteDataInterface } from "../lib/routes/routes";
import { cn } from "../lib/utils";
import { useIsMobile } from "@src/hooks/use-mobile";
import { Menu } from "lucide-react";
import { Button } from "./ui/button";
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbList,
	BreadcrumbSeparator,
} from "./ui/breadcrumb";

export default function AppBreadcrumbs() {
	const pathname = usePathname();
	const searchParams = useSearchParams();
	const isMobile = useIsMobile();
	const [sidebarOpen, setSidebarOpen] = useState(false);

	// Função para abrir o sidebar
	const toggleSidebar = () => {
		// Emite um evento personalizado para comunicar com o componente AppSidebar
		const event = new CustomEvent('toggle-sidebar');
		window.dispatchEvent(event);
		setSidebarOpen(!sidebarOpen);
	};

	// Melhora a extração do ID para capturar qualquer ID na URL
	const id = pathname.split('/').pop();

	// Obter o parâmetro 'from' da URL
	const fromParam = searchParams.get('from');

	const findBreadcrumbPath = (
		routes: RouteDataInterface[],
		pathname: string
	): RouteDataInterface[] | null => {
		// Se temos um parâmetro 'from' e estamos na página de histórico, ajustar o breadcrumb
		if (fromParam && pathname.includes('/views/crm/proposals/completed/')) {
			// Determinar a rota base com base no parâmetro 'from'
			let baseRoute = '';
			switch (fromParam) {
				case 'to-start':
					baseRoute = '/views/crm/proposals/to-start';
					break;
				case 'accepted':
					baseRoute = '/views/crm/proposals/accepted';
					break;
				case 'lost':
					baseRoute = '/views/crm/proposals/lost';
					break;
				default:
					baseRoute = '/views/crm/proposals/completed';
			}

			// Buscar o caminho do breadcrumb para a rota de origem
			for (const route of routes) {
				if (route.items) {
					for (const item of route.items) {
						if (item.url === baseRoute) {
							// Adicionar o item de histórico
							if (item.items) {
								for (const subItem of item.items) {
									if (subItem.title === "Histórico") {
										return [route, item, subItem];
									}
								}
							}
						}
					}
				}
			}
		}

		// Comportamento padrão
		for (const route of routes) {
			// Verifica se a URL corresponde ou se é uma rota dinâmica
			const isDynamicRoute = route.url.includes("[id]");
			const routeBasePath = isDynamicRoute ? route.url.split("[id]")[0] : route.url;
			const isMatch = isDynamicRoute
				? pathname.startsWith(routeBasePath)
				: route.url === pathname;

			if (isMatch) {
				return [route];
			}
			if (route.items) {
				const subPath = findBreadcrumbPath(route.items, pathname);
				if (subPath) {
					return [route, ...subPath];
				}
			}
		}
		return null;
	};

	const constructBreadcrumbItem = (
		route: RouteDataInterface,
		pathname: string,
		// id?: string | null
	) => {
		let resolvedUrl = route.url;

		// Se a URL contém [id], remove o [id] e tudo que vem depois
		if (route.url.includes("[id]")) {
			resolvedUrl = route.url.split("/[id]")[0];
		}

		return (
			<BreadcrumbItem>
				<route.icon
					className={cn(
						"size-4 hidden sm:block",
						resolvedUrl === pathname && "text-green-500"
					)}
				/>
				<Link
					href={resolvedUrl}
					className={cn(
						resolvedUrl === pathname && "text-green-500 font-semibold",
						"hover:text-green-500"
					)}
					prefetch={true}
				>
					{route.title}
				</Link>
			</BreadcrumbItem>
		);
	};

	const breadcrumbPath = findBreadcrumbPath(breadcrumbs, pathname);

	return (
		<div className="flex items-center gap-2">
			{isMobile && (
				<Button
					variant="outline"
					size="icon"
					className="mr-2 bg-white border-green-300 hover:bg-green-50 hover:border-green-500 transition-all duration-200 shadow-md h-9 w-9"
					onClick={toggleSidebar}
					aria-label="Toggle menu"
				>
					<Menu className="h-5 w-5 text-green-600" />
				</Button>
			)}
			<Breadcrumb>
				<BreadcrumbList>
					{breadcrumbPath?.map((route, index) => (
						<Fragment key={route.title}>
							{/* Só mostra dropdown se tiver routerGroup E não for uma rota com [id] */}
							{route.routerGroup && route.items && !route.url.includes("[id]") ? (
								<DropdownMenu>
									<DropdownMenuTrigger asChild>
										{constructBreadcrumbItem(route, pathname)}
									</DropdownMenuTrigger>
									<DropdownMenuContent>
										{route.items?.map((item) => (
											<DropdownMenuItem
												key={item.title}
												className="hover:!text-green-500 hover:!bg-green-100"
											>
												<Link
													href={item.url.includes("[id]") && id
														? item.url.replace("[id]", id)
														: item.url
													}
													className={cn(
														"w-full",
														item.url === pathname && "text-green-500"
													)}
													prefetch={true}
												>
													{item.title}
												</Link>
											</DropdownMenuItem>
										))}
									</DropdownMenuContent>
								</DropdownMenu>
							) : (
								constructBreadcrumbItem(route, pathname)
							)}
							{index < breadcrumbPath.length - 1 && <BreadcrumbSeparator />}
						</Fragment>
					))}
				</BreadcrumbList>
			</Breadcrumb>
		</div>
	);
}
