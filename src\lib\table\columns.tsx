import AppConfirmationDialog from "@/src/components/app-confirmation-dialog";
// import { Button } from "@/src/components/ui/button";
// import { CustomColumnDef } from "@/src/types/utils";
import { Column } from "@/src/components/ui/table-grid";
import {
  // ArrowUpDown,
  EyeIcon,
  LucideIcon,
  SquarePen,
  Trash,
} from "lucide-react";
import { ReactNode } from "react";
import { cn } from "../utils";

export interface ColumnActionInterface<T> {
  action: "edit" | "delete" | "custom" | "view";
  customIcon?: LucideIcon;
  customIconClass?: string;
  dialogTitle?: string;
  dialogDescription?: string;
  callback: (data: T) => void;
}

export function contructColumn<T>(
  key: string,
  header: string,
  cell?: (row: T) => ReactNode,
  options: {
    sortable?: boolean;
    filterable?: boolean;
    filterOptions?: { label: string; value: string }[];
    getFilterValue?: (row: T) => string;
  } = {
      sortable: true,
    }
): Column<T> {
  return {
    key,
    header,
    cell,
    sortable: options.sortable,
    filterable: options.filterable,
    filterOptions: options.filterOptions,
    getFilterValue: options.getFilterValue,
  };
}

export function constructActionColumn<T>(actions: ColumnActionInterface<T>[]): Column<T> {
  return {
    key: "actions",
    header: "Ações",
    cell: (row: T) => {
      const viewAction = actions.find(({ action }) => action === "view");
      const editAction = actions.find(({ action }) => action === "edit");
      const deleteAction = actions.find(({ action }) => action === "delete");
      const customActions = actions.filter(({ action }) => action === "custom");

      return (
        <div className="flex gap-3">
          {viewAction && (
            viewAction.customIcon ? (
              <viewAction.customIcon
                className={cn(
                  "size-5 cursor-pointer",
                  viewAction.customIconClass
                )}
                onClick={() => viewAction.callback(row)}
              />
            ) : (
              <EyeIcon
                className="size-5 text-green-500 cursor-pointer"
                onClick={() => viewAction.callback(row)}
              />
            )
          )}
          {editAction && (
            <SquarePen
              className="size-5 text-green-500 cursor-pointer"
              onClick={() => editAction.callback(row)}
            />
          )}
          {deleteAction && (
            <AppConfirmationDialog
              title={deleteAction.dialogTitle}
              description={deleteAction.dialogDescription}
              onConfirmCallback={() => {
                deleteAction.callback(row);
              }}
              dialogCancelClassName="bg-blue-500 text-white hover:bg-blue-600"
              dialogActionClassName="bg-destructive hover:bg-destructive/90"
            >
              <Trash className="size-5 cursor-pointer" color="#ef4444" />
            </AppConfirmationDialog>
          )}
          {customActions.map(
            (action, index) =>
              action.customIcon && (
                <action.customIcon
                  key={index}
                  onClick={() => action.callback(row)}
                  className={cn(
                    "size-5 cursor-pointer",
                    action.customIconClass
                  )}
                />
              )
          )}
        </div>
      );
    }
  };
}
