"use client";

import {
  checkUserMembership,
  requestOrganizationAccess,
} from "@/src/actions/membership";
import { loadOrganizations } from "@/src/actions/organization";
import AppConfirmationDialog from "@/src/components/app-confirmation-dialog";
import { Button } from "@/src/components/ui/button";
import { useToast } from "@/src/hooks/use-toast";
import logoGreen from "@public/logos/logo.svg";
import { Building2, Loader2, LogIn, LogOut, UserPlus2 } from "lucide-react";
import { signOut } from "next-auth/react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function WelcomePage() {
  const [loading, setLoading] = useState(true);
  const [organizations, setOrganizations] = useState<any[]>([]);
  const [membership, setMembership] = useState<any>(null);
  const router = useRouter();
  const { toast } = useToast();

  useEffect(() => {
    checkMembership();
  }, []);

  const checkMembership = async () => {
    try {
      setLoading(true);
      const membership = await checkUserMembership();
      setMembership(membership);

      if (membership?.enabled) {
        router.push("/views/control-panel");
        return;
      }

      if (!membership) {
        const orgs = await loadOrganizations();
        setOrganizations(orgs);
      }
    } catch (error) {
      console.error(error);
      toast({
        title: "Erro",
        description: "Erro ao verificar acesso",
        variant: "destructive",
      });
      signOut({ callbackUrl: "/auth" });
    } finally {
      setLoading(false);
    }
  };

  const handleRequestAccess = async (organizationId: string) => {
    try {
      setLoading(true);
      await requestOrganizationAccess(organizationId);
      await checkMembership(); // Refresh membership status
      toast({
        title: "Sucesso",
        description: "Solicitação de acesso enviada com sucesso",
      });
    } catch (error) {
      console.error(error);
      toast({
        title: "Erro",
        description: "Erro ao solicitar acesso",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-green-500" />
          <p className="text-gray-600">Carregando...</p>
        </div>
      </div>
    );
  }

  if (membership && !membership.enabled) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center max-w-md mx-auto p-8 bg-white rounded-xl shadow-lg">
          <div className="mb-6 w-full flex justify-center">
            <Image src={logoGreen} alt="Logo" className={"w-32"} priority />
          </div>
          <div className="flex flex-col items-center gap-4">
            <h2 className="text-2xl font-bold text-gray-800">
              Solicitação em Análise
            </h2>
            <p className="text-gray-600 max-w-sm">
              Sua solicitação de acesso está sendo analisada. Você receberá uma
              notificação assim que for aprovada.
            </p>
            <div className="mt-6">
              <Button
                variant="outline"
                className="text-gray-600 border-gray-300 hover:bg-gray-100 flex items-center gap-2"
                onClick={() => signOut({ callbackUrl: "/auth" })}
              >
                <LogOut className="h-4 w-4" />
                Trocar de conta
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="mb-6 w-full flex justify-center">
            <Image src={logoGreen} alt="Logo" className={"w-32"} priority />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Bem-vindo ao Ageu
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Selecione uma organização para começar a utilizar nossa plataforma
            de gestão de engenharia.
          </p>
        </div>

        {/* Organizations Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {organizations.map((org) => (
            <div
              key={org.id}
              className="bg-white rounded-xl shadow-md overflow-hidden transition-transform hover:scale-[1.02] hover:shadow-lg"
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Building2 className="h-6 w-6 text-green-600" />
                  </div>
                  <AppConfirmationDialog
                    title="Confirmar solicitação de acesso"
                    description={`Deseja realmente solicitar acesso à organização ${org.name}?`}
                    onConfirmCallback={() => handleRequestAccess(org.id)}
                    dialogActionClassName="bg-green-500 hover:bg-green-600"
                    dialogCancelClassName="bg-blue-500 hover:bg-blue-600"
                    confirmButtonText="Solicitar"
                  >
                    <Button className="bg-green-500 hover:bg-green-600 transition-colors flex items-center gap-2">
                      <UserPlus2 className="h-4 w-4" />
                      <span>Solicitar acesso</span>
                    </Button>
                  </AppConfirmationDialog>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {org.name}
                </h3>
                <div className="flex items-center text-gray-500 text-sm">
                  <LogIn className="h-4 w-4 mr-2" />
                  <span>Clique para solicitar acesso</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {organizations.length === 0 && (
          <div className="text-center py-12">
            <Building2 className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900">
              Nenhuma organização disponível
            </h3>
            <p className="mt-2 text-gray-500">
              No momento não há organizações disponíveis para solicitação de
              acesso.
            </p>
          </div>
        )}

        {/* Logout Button */}
        <div className="mt-8 flex justify-center">
          <Button
            variant="outline"
            className="text-gray-600 border-gray-300 hover:bg-gray-100 flex items-center gap-2"
            onClick={() => signOut({ callbackUrl: "/auth" })}
          >
            <LogOut className="h-4 w-4" />
            Trocar de conta
          </Button>
        </div>
      </div>
    </div>
  );
}
