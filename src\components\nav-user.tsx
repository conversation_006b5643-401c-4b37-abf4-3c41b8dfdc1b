"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { LogOut } from "lucide-react";
import { User } from "next-auth";
import { signOut } from "next-auth/react";

interface NavUserProps {
  user: User;
  isCollapsed?: boolean;
}

export function NavUser({ user, isCollapsed }: NavUserProps) {
  // Verifica se é um dispositivo móvel
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

  return (
    <div className={`flex items-center gap-2 rounded-md hover:bg-gray-100 cursor-pointer ${isMobile ? 'p-3' : 'p-2'}`} onClick={() => signOut()}>
      <Avatar className="h-8 w-8 flex-shrink-0">
        <AvatarImage src={user.image!} alt={user.name!} />
        <AvatarFallback>
          {user.name?.charAt(0).toUpperCase()}
        </AvatarFallback>
      </Avatar>
      {!isCollapsed && (
        <div className="flex-1 text-left text-sm overflow-hidden">
          <div className="font-semibold text-green-500 truncate">{user.name}</div>
          <div className="text-xs text-gray-500 truncate">{user.email}</div>
        </div>
      )}
      {!isCollapsed && <LogOut className="h-4 w-4 text-red-500 flex-shrink-0" />}
    </div>
  );
}