import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { id, predictedPeriodPercentage, realPeriodPercentage } = body;

    if (!id) {
      return NextResponse.json(
        { error: "ID da produtividade é obrigatório" },
        { status: 400 }
      );
    }

    // Verificar se os valores são números
    const predictedValue = Number(predictedPeriodPercentage) || 0;
    const realValue = Number(realPeriodPercentage) || 0;

    console.log('Updating productivity values via SQL:');
    console.log('- ID:', id);
    console.log('- predictedPeriodPercentage:', predictedValue);
    console.log('- realPeriodPercentage:', realValue);

    // Usar o Prisma para atualizar os valores
    // O Prisma espera valores Decimal para esses campos
    const result = await prisma.productivity.update({
      where: { id },
      data: {
        predictedPeriodPercentage: predictedValue,
        realPeriodPercentage: realValue
      }
    });

    console.log('Prisma update result:', result);
    console.log('- predictedPeriodPercentage after update:', result.predictedPeriodPercentage);
    console.log('- realPeriodPercentage after update:', result.realPeriodPercentage);

    console.log('SQL update result:', result);

    // Buscar o registro atualizado para confirmar
    const updatedRecord = await prisma.productivity.findUnique({
      where: { id },
    });

    console.log('Updated record from database:', updatedRecord);

    return NextResponse.json({
      success: true,
      message: "Valores atualizados com sucesso",
      record: updatedRecord
    });
  } catch (error) {
    console.error("Erro ao atualizar valores de produtividade:", error);
    return NextResponse.json(
      { error: "Erro ao atualizar valores de produtividade" },
      { status: 500 }
    );
  }
}
