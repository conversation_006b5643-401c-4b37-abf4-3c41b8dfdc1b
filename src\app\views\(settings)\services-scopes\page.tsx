"use client";

import { useState, useRef } from "react";
import ServicesScopeDialog from "./components/services-scope-dialog";
import ServicesScopeTable from "./components/services-scope-table";
import { ServicesScope } from "@/src/types/core/services-scope";
import { removeServiceScope } from "@/src/actions/services-scopes";
import { toast } from "@/src/hooks/use-toast";
import { formatDate } from "@/src/lib/utils";
import { contructColumn, constructActionColumn } from "@/src/lib/table/columns";
import ContentWrapper from "@/src/components/content-wrapper";

export default function ServicesScopePage() {
	const [isOpen, setIsOpen] = useState(false);
	const [selectedService, setSelectedService] = useState<ServicesScope | null>(null);
	const [currentPage, setCurrentPage] = useState(1);
	const tableRef = useRef<{ refresh: (page?: number) => void }>(null);	

	const handleEdit = (service: ServicesScope) => {
		setSelectedService(service);
		setIsOpen(true);
	};

	const handleDelete = async (service: ServicesScope) => {
		try {
			const result = await removeServiceScope(service.id);

			if (result?.error) {
				toast({
					title: result.title,
					description: result.message,
					variant: "destructive",
				});
			} else {
				toast({
					title: "Sucesso",
					description: "Serviço excluído com sucesso!",
					variant: "default",
				});
				tableRef.current?.refresh(currentPage);
			}
		} catch (error) {
			console.error(error);
			toast({
				title: "Erro",
				description: "Erro ao excluir serviço",
				variant: "destructive",
			});
		} 
	};

	const columns = [
		contructColumn("name", "Nome"),
		contructColumn("description", "Descrição"),
		contructColumn("createdAt", "Criado em", (row: any) =>
			row.createdAt ? formatDate(row.createdAt, "DATE") : "-"
		),
		constructActionColumn<ServicesScope>([
			{
				action: "edit",
				callback: (row) => handleEdit(row)
			},
			{
				action: "delete",
				dialogTitle: "Deseja realmente excluir o serviço?",
				dialogDescription: "Esta ação não poderá ser desfeita. Todos os dados relacionados a este serviço serão permanentemente excluídos.",
				callback: (row) => handleDelete(row)
			}
		])
	];

	return (
		// <div className="bg-white p-6">
		// 	<h1 className="text-2xl font-bold mb-6">Escopo de serviços</h1>
		<ContentWrapper title="Escopo de serviços">
			<ServicesScopeTable
				ref={tableRef}
				columns={columns}
				onAddClick={() => {
					setSelectedService(null);
					setIsOpen(true);
				}}
				onPageChange={(page) => setCurrentPage(page)}
			/>
			<ServicesScopeDialog
				isOpen={isOpen}
				onOpenChange={(open) => {
					setIsOpen(open);
					if (!open) {
						setSelectedService(null);
						tableRef.current?.refresh(currentPage);
					}
				}}
				service={selectedService}
				currentPage={currentPage}
			/>
		</ContentWrapper>
		// </div>
	);
}
