import {
	Card,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/src/components/ui/button";
import { Eye, Trash, Edit } from "lucide-react";
import { Template } from "../types/utils";

interface ItemCardProps {
	name?: string;
	description?: string;
	children?: Template;
	onViewTemplate?: Template;
	onView?: () => void;
	onEdit?: () => void;
	onDelete?: () => void;
}

export default function ItemCard({
	name,
	description,
	children,
	onViewTemplate,
	onView,
	onEdit,
	onDelete,
}: ItemCardProps) {
	return (
		<Card className="rounded-lg p-3 sm:p-5 min-w-[200px] w-full sm:min-w-[280px] sm:max-w-[350px] hover:shadow-md transition-all duration-300 border border-gray-200 bg-gradient-to-br from-white to-gray-50 hover:translate-y-[-2px] block">
			<div className="flex flex-col sm:flex-row">
				<div className="flex-1">
					<CardHeader className="p-0 pb-4">
						<CardTitle className="text-lg font-semibold text-green-700 mb-2">{name}</CardTitle>
						<div className="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
							<CardDescription className="text-gray-700">{description}</CardDescription>
						</div>
					</CardHeader>
					{children && <div className="mb-4">{children}</div>}
				</div>

				{/* Ícones na horizontal em mobile e vertical em desktop */}
				<div className="flex flex-row sm:flex-col gap-2 mt-3 sm:mt-0 sm:ml-4 justify-center">
					{onView && (
						<Button
							variant="outline"
							size="icon"
							className="rounded-full hover:bg-blue-50 hover:border-blue-200 transition-colors duration-200"
							onClick={() => onView!()}
							type="button"
						>
							<Eye className="size-5 text-blue-500 cursor-pointer" />
						</Button>
					)}
					{onViewTemplate && onViewTemplate}

					{onEdit && (
						<Button
							variant="outline"
							size="icon"
							className="rounded-full hover:bg-green-50 hover:border-green-200 transition-colors duration-200"
							onClick={() => onEdit?.()}
							type="button"
						>
							<Edit className="size-4 text-green-500" />
						</Button>
					)}

					{onDelete && (
						<Button
							variant="outline"
							size="icon"
							className="rounded-full hover:bg-red-50 hover:border-red-200 transition-colors duration-200"
							onClick={() => onDelete?.()}
							type="button"
						>
							<Trash className="size-4 text-red-500" />
						</Button>
					)}
				</div>
			</div>
		</Card>
	);
}
