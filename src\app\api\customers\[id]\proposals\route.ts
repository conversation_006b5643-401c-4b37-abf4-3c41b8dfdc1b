import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const customerId = params.id;

    if (!customerId) {
      return NextResponse.json(
        { error: "ID do cliente não fornecido" },
        { status: 400 }
      );
    }

    // Buscar todas as propostas do cliente
    const proposals = await prisma.proposal.findMany({
      where: {
        customerId: customerId,
      },
      orderBy: {
        createdAt: "desc",
      },
      include: {
        customer: true,
      },
    });

    return NextResponse.json({
      items: proposals,
      total: proposals.length,
    });
  } catch (error) {
    console.error("Erro ao buscar propostas do cliente:", error);
    return NextResponse.json(
      { error: "Erro ao buscar propostas do cliente" },
      { status: 500 }
    );
  }
}
