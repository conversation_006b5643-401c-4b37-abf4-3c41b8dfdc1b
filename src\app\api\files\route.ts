import { NextRequest, NextResponse } from "next/server";
import { storageProvider } from "@/src/lib/storage";
import { prisma } from "@/src/lib/prisma";
import { File as FileType } from "@/src/types/core/file";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";
import { toKebabCase } from "@/src/lib/utils";

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const file = formData.get("file");
    const thumbnail = formData.get("thumbnail");
    const description = formData.get("description") as string | null;

    if (!file || !(file instanceof File)) {
      return NextResponse.json(
        { error: "Arquivo de vídeo não enviado." },
        { status: 400 }
      );
    }

    // Obter informações da organização para o caminho
    const { organizationName } = await getCurrentOrganization();
    if (!organizationName) {
      return NextResponse.json(
        { error: "Nome da organização não encontrado." },
        { status: 400 }
      );
    }
    const orgSlug = toKebabCase(organizationName);

    // Montar objeto File para o storageProvider
    const buffer = Array.from(new Uint8Array(await file.arrayBuffer()));
    const fileName = file.name;
    const fileType = file.type;
    const fileSize = file.size;
    const filePath = `${orgSlug}/videos`;

    // Salvar vídeo no Minio
    const storagePath = await storageProvider.upload({
      id: "",
      name: fileName,
      path: filePath,
      type: fileType,
      size: fileSize,
      buffer,
    } as FileType);

    // Registrar no banco
    const createdFile = await prisma.file.create({
      data: {
        name: fileName,
        path: storagePath,
        type: fileType,
        size: fileSize,
        // description não existe no modelo File, então não incluir
      },
    });

    let thumbnailPath: string | null = null;
    // Se thumbnail enviada, salvar também
    if (thumbnail && thumbnail instanceof File) {
      const thumbBuffer = Array.from(
        new Uint8Array(await thumbnail.arrayBuffer())
      );
      const thumbFileName = `thumb-${createdFile.id}.jpg`;
      thumbnailPath = `${orgSlug}/thumbnails/${thumbFileName}`;
      await storageProvider.upload({
        id: "",
        name: thumbFileName,
        path: `${orgSlug}/thumbnails`,
        type: "image/jpeg",
        size: thumbBuffer.length,
        buffer: thumbBuffer,
      } as FileType);
      // Atualizar registro no banco
      await prisma.file.update({
        where: { id: createdFile.id },
        data: { thumbnailPath },
      });
    }

    return NextResponse.json({
      id: createdFile.id,
      thumbnailPath,
      description,
    });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Erro ao processar upload." },
      { status: 500 }
    );
  }
}
