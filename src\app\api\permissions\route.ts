import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";
import { auth } from "@/src/providers/auth";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";
import { getAllRoutesWithTitles } from "@/src/lib/routes/extract-routes";

export const dynamic = "force-dynamic";

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return new NextResponse("Não autorizado", { status: 401 });
    }

    const { organizationId } = await getCurrentOrganization();

    // Buscar todas as rotas do sistema diretamente do routeData
    const systemRoutes = getAllRoutesWithTitles();

    // Buscar todas as permissões do sistema
    const permissions = await prisma.userRoutePermission.findMany({
      where: {
        organizationId,
      },
      select: {
        id: true,
        route: true,
        enabled: true,
      },
      orderBy: {
        route: "asc",
      },
    });

    // Criar um mapa de permissões existentes
    const existingPermissions = new Map(
      permissions.map(p => [p.route, p])
    );

    // Combinar rotas do sistema com permissões existentes
    const allPermissions = systemRoutes.map(routeObj => {
      const existing = existingPermissions.get(routeObj.route);
      if (existing) {
        return {
          ...existing,
          title: routeObj.title,
        };
      }
      return {
        id: `new-${routeObj.route}`,
        route: routeObj.route,
        title: routeObj.title,
        enabled: true,
      };
    });

    return NextResponse.json(allPermissions);
  } catch (error) {
    console.error("Erro ao buscar permissões:", error);
    return new NextResponse("Erro interno do servidor", { status: 500 });
  }
} 