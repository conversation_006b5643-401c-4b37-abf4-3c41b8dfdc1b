"use client";

import { loadControlPanelData } from "@/src/actions/control-panel";
import { loadKpiData } from "@/src/actions/kpi";
import ContentWrapper from "@/src/components/content-wrapper";
import { Button } from "@/src/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/src/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select";
import { ControlPanelData } from "@/src/types/core/control-panel";
import {
  AreaChart,
  BarChart,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  Area,
  Bar,
  Cell
} from "recharts";
import { useEffect, useState } from "react";
import {
  ArrowUpRight,
  BarChart3,
  CheckCircle2,
  ChevronRight,
  CircleDollarSign,
  Clock,
  <PERSON>Text,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>fresh<PERSON><PERSON><PERSON>,
  Ruler,
  Square<PERSON>tack,
  Users,
} from "lucide-react";
import { useIsMobile } from "@/src/hooks/use-mobile";
import Link from "next/link";

// Definição de cores para os gráficos
const COLORS = {
  primary: "#10b981",
  secondary: "#3b82f6",
  accent: "#8b5cf6",
  warning: "#f59e0b",
  danger: "#ef4444",
  info: "#06b6d4",
  success: "#22c55e",
  background: "#f8fafc",
  foreground: "#334155",
  muted: "#94a3b8",
  border: "#e2e8f0",
};

// Cores para o gráfico de pizza
const PIE_COLORS = [
  COLORS.primary,
  COLORS.secondary,
  COLORS.accent,
  COLORS.warning,
  COLORS.danger,
  COLORS.info,
];

// Definição de tipos de serviço padronizados
const SERVICE_TYPES = [
  { label: "Inspeção", value: "INSPECAO" },
  { label: "Fiscalização", value: "FISCALIZACAO" },
  { label: "Gerenciamento", value: "GERENCIAMENTO" },
  { label: "Projeto", value: "PROJECT" },
  { label: "Consultoria", value: "CONSULTANCY" },
];
const SERVICE_TYPE_LABELS: Record<string, string> = SERVICE_TYPES.reduce((acc, cur) => {
  acc[cur.value] = cur.label;
  return acc;
}, {} as Record<string, string>);

// Componente para exibir um card de estatística
interface StatCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: React.ReactNode;
  trend?: number;
  color?: string;
  onClick?: () => void;
}

const StatCard = ({ title, value, description, icon, trend, color = COLORS.primary, onClick }: StatCardProps) => {
  // Definir um limite para exibição de percentuais (por exemplo, 200%)
  const TREND_THRESHOLD = 200;
  const isTrendExcessive = trend !== undefined && Math.abs(trend) > TREND_THRESHOLD;

  // Calcular o valor anterior com base na tendência atual
  const calculatePreviousValue = () => {
    // Obter o valor atual
    const currentValue = Number(value);

    // Se não houver tendência ou o valor atual for 0, retornar 0
    if (trend === undefined || currentValue === 0) return 0;

    // Calcular o valor anterior com base na tendência
    // Se trend = 100%, significa que o valor dobrou, então o valor anterior era metade do atual
    // Se trend = -50%, significa que o valor caiu pela metade, então o valor anterior era o dobro do atual
    return Math.round(currentValue / (1 + (trend / 100)));
  };

  // Calcular o valor anterior
  const previousValue = calculatePreviousValue();

  return (
    <Card className="overflow-hidden transition-all duration-300 hover:shadow-md border border-gray-100 hover:border-gray-200">
      <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
        <CardTitle className="text-sm font-medium text-gray-600">{title}</CardTitle>
        <div className={`p-2 rounded-full bg-opacity-10`} style={{ backgroundColor: `${color}20` }}>
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold" style={{ color }}>
          {value}
        </div>
        {description && (
          <p className="text-xs text-gray-500 mt-1">
            {description}
          </p>
        )}
        {trend !== undefined && (
          <div className="flex items-center mt-2">
            {isTrendExcessive ? (
              // Exibir formato alternativo para tendências muito altas
              <div className="flex flex-col">
                <span className={`text-xs font-medium ${trend >= 0 ? 'text-green-500' : 'text-red-500'} flex items-center`}>
                  {trend >= 0 ? (
                    <ArrowUpRight className="h-3 w-3 mr-1 rotate-0" />
                  ) : (
                    <ArrowUpRight className="h-3 w-3 mr-1 rotate-180" />
                  )}
                  de {previousValue} para {value} {trend >= 0 ? '(↗ +' : '(↘ -'}{Math.min(Math.abs(trend), 200)}%)
                </span>
              </div>
            ) : (
              // Exibir percentual normal para tendências dentro do limite
              <>
                <span className={`text-xs font-medium ${trend >= 0 ? 'text-green-500' : 'text-red-500'} flex items-center`}>
                  {trend >= 0 ? (
                    <ArrowUpRight className="h-3 w-3 mr-1 rotate-0" />
                  ) : (
                    <ArrowUpRight className="h-3 w-3 mr-1 rotate-180" />
                  )}
                  {Math.abs(trend)}% {trend >= 0 ? 'aumento' : 'redução'}
                </span>
                <span className="text-xs text-gray-400 ml-1">em relação ao mês anterior</span>
              </>
            )}
          </div>
        )}
      </CardContent>
      {onClick && (
        <CardFooter className="pt-0">
          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-auto text-xs font-medium hover:bg-transparent"
            onClick={onClick}
          >
            {title === "Clientes Ativos" ? "Ver histórico de projetos por cliente" : "Ver detalhes"} <ChevronRight className="h-3 w-3 ml-1" />
          </Button>
        </CardFooter>
      )}
    </Card>
  );
};

// Componente para exibir um card com gráfico
interface ChartCardProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  icon: React.ReactNode;
  value?: string | number;
  footer?: React.ReactNode;
  headerContent?: React.ReactNode; // Novo parâmetro para conteúdo adicional no cabeçalho
}

const ChartCard = ({ title, description, children, icon, value, footer, headerContent }: ChartCardProps) => {
  const isMobile = useIsMobile();

  return (
    <Card className="overflow-hidden transition-all duration-300 hover:shadow-md border border-gray-100 hover:border-gray-200">
      <CardHeader className="pb-2 space-y-0">
        <div className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="text-base font-medium text-gray-700">{title}</CardTitle>
            {description && <CardDescription>{description}</CardDescription>}
          </div>
          <div className="p-2 rounded-full bg-gray-100">
            {icon}
          </div>
        </div>
        {headerContent && (
          <div className="mt-2">{headerContent}</div>
        )}
      </CardHeader>
      <CardContent className={isMobile ? "px-1 py-2" : ""}>
        {value && <div className="text-xl font-bold mb-4">{value}</div>}
        {children}
      </CardContent>
      {footer && <CardFooter className="border-t pt-4">{footer}</CardFooter>}
    </Card>
  );
};

// Componente principal do painel de controle
export default function ControlPanel() {
  const isMobile = useIsMobile();
  const [loading, setLoading] = useState(true); // Loading geral da página
  const [controlPanelData, setControlPanelData] = useState<ControlPanelData[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  // Estados para os dados, filtros e loading específicos de cada gráfico
  const [valueChartData, setValueChartData] = useState<any[]>([]); // Dados específicos para o gráfico de Valor por Metro Quadrado
  const [revenueChartData, setRevenueChartData] = useState<any[]>([]); // Dados específicos para o gráfico de Faturamento Recente
  const [selectedServiceTypeForValue, setSelectedServiceTypeForValue] = useState<string | null>(null);
  const [selectedServiceTypeForRevenue, setSelectedServiceTypeForRevenue] = useState<string | null>(null);
  const [loadingValueChart, setLoadingValueChart] = useState(false); // Loading específico para o gráfico de Valor por Metro Quadrado
  const [loadingRevenueChart, setLoadingRevenueChart] = useState(false); // Loading específico para o gráfico de Faturamento Recente

  // Função para buscar dados do painel de controle
  const fetchControlPanelData = async () => {
    try {
      setLoading(true);
      const data = await loadControlPanelData();
      if (data) setControlPanelData(data);

      // Buscar dados de KPI para os gráficos - não aplicamos filtro aqui, apenas carregamos todos os dados
      const kpiResult = await loadKpiData();
      if (kpiResult) {
        setValueChartData(kpiResult);
        setRevenueChartData(kpiResult);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // Função para atualizar os dados
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchControlPanelData();
    setTimeout(() => setRefreshing(false), 500);
  };

  // Função para lidar com a mudança do tipo de serviço para o gráfico de Valor por Metro Quadrado
  const handleServiceTypeChangeForValue = async (value: string) => {
    // Se o valor for "ALL", definimos como null para não aplicar filtro
    const serviceTypeValue = value === "ALL" ? null : value;
    setSelectedServiceTypeForValue(serviceTypeValue);
    setLoadingValueChart(true); // Ativar loading apenas para este gráfico
    try {
      const kpiResult = await loadKpiData(undefined, undefined, serviceTypeValue || undefined);
      if (kpiResult) setValueChartData(kpiResult); // Atualizar apenas os dados deste gráfico
    } catch (error) {
      console.error(error);
    } finally {
      setLoadingValueChart(false); // Desativar loading apenas para este gráfico
    }
  };

  // Função para lidar com a mudança do tipo de serviço para o gráfico de Faturamento Recente
  const handleServiceTypeChangeForRevenue = async (value: string) => {
    // Se o valor for "ALL", definimos como null para não aplicar filtro
    const serviceTypeValue = value === "ALL" ? null : value;
    setSelectedServiceTypeForRevenue(serviceTypeValue);
    setLoadingRevenueChart(true); // Ativar loading apenas para este gráfico
    try {
      const kpiResult = await loadKpiData(undefined, undefined, serviceTypeValue || undefined);
      if (kpiResult) setRevenueChartData(kpiResult); // Atualizar apenas os dados deste gráfico
    } catch (error) {
      console.error(error);
    } finally {
      setLoadingRevenueChart(false); // Desativar loading apenas para este gráfico
    }
  };

  // Efeito para carregar os dados iniciais e quando os filtros mudarem
  useEffect(() => {
    fetchControlPanelData();
  }, []);  // Executar apenas na montagem do componente

  // Encontrar dados específicos
  const findData = (title: string, year?: number) => {
    if (year) {
      // Busca pelo título e ano, se disponível
      return controlPanelData.find(item => item.title === title && item.year === year)?.count || 0;
    }
    return controlPanelData.find(item => item.title === title)?.count || 0;
  };

  // Ano atual
  const currentYear = new Date().getFullYear();

  // Dados para o gráfico de propostas por tipo de contrato
  const proposalByServiceTypeData = controlPanelData
    .filter(item => item.type === "CONTRACT_TYPE")
    .map(item => ({
      name: item.title,
      value: Number(item.count) || 0
    }))
    .sort((a, b) => b.value - a.value); // Ordenar por valor decrescente

  // Dados para o gráfico de barras de projetos por status
  const projectStatusData = [
    { name: 'Em análise', value: Number(findData('Propostas em analise')), color: COLORS.info },
    { name: 'Enviadas', value: Number(findData('Propostas enviadas')), color: COLORS.muted },
    { name: 'Aceitas', value: Number(findData('Propostas aceitas')), color: COLORS.secondary },
    { name: 'Assinadas', value: Number(findData('Propostas assinadas')), color: COLORS.accent },
    { name: 'Em andamento', value: Number(findData('Projetos em andamento')), color: COLORS.warning },
    // Alteração: só pega concluídos do ano atual e exibe o ano na coluna
    { name: `Concluídos ${currentYear}`, value: Number(findData('Projetos concluidos', currentYear)), color: COLORS.success },
    { name: 'Perdidas', value: Number(findData('Propostas perdidas')), color: COLORS.danger },
  ];

  // Calcular o total de propostas
  const totalProposals = Number(findData('Total de propostas'));

  // Calcular a taxa de conversão
  const acceptedProposals = Number(findData('Propostas aceitas')) +
    Number(findData('Propostas assinadas')) +
    Number(findData('Projetos em andamento')) +
    Number(findData('Projetos concluidos'));

  const conversionRate = totalProposals > 0
    ? ((acceptedProposals / totalProposals) * 100).toFixed(1)
    : "0.0";

  // Dados para o gráfico de área de faturamento
  const revenueData = revenueChartData
    .sort((a, b) => {
      // Ordenar por ano e mês
      if (a.year !== b.year) return a.year - b.year;
      return a.month - b.month;
    })
    .slice(-6) // Pegar os últimos 6 meses
    .map(item => ({
      name: `${item.month}/${item.year}`,
      value: item.completedProposalsValue || 0, // Valor das propostas concluídas
    }));

  // Dados para o gráfico de valor por metro quadrado
  const valuePerSquareMeterData = valueChartData
    .sort((a, b) => {
      // Ordenar por ano e mês
      if (a.year !== b.year) return a.year - b.year;
      return a.month - b.month;
    })
    .slice(-6) // Pegar os últimos 6 meses
    .map(item => ({
      name: `${item.month}/${item.year}`,
      value: parseFloat(item.valuePerSquareMeter) || 0,
    }));

  return (
    <ContentWrapper title="Painel de Controle" loading={loading}>
      <div className="flex flex-col space-y-6">
        {/* Botão de atualização */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center gap-2"
          >
            {refreshing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCcw className="h-4 w-4" />
            )}
            Atualizar dados
          </Button>
        </div>

        {/* Cards de estatísticas principais */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 card-container">
          <StatCard
            title="Clientes Ativos"
            value={findData('Clientes ativos')}
            description="Total de clientes cadastrados"
            icon={<Users className="h-4 w-4 text-blue-500" />}
            color={COLORS.secondary}
            trend={Number(controlPanelData.find(item => item.title === 'Clientes ativos')?.trend) || 0}
            onClick={() => window.location.href = '/views/crm/customer-projects-history'}
          />

          <StatCard
            title="Total de Propostas"
            value={findData('Total de propostas')}
            description="Todas as propostas registradas"
            icon={<FileText className="h-4 w-4 text-purple-500" />}
            color={COLORS.accent}
            trend={Number(controlPanelData.find(item => item.title === 'Total de propostas')?.trend) || 0}
            onClick={() => window.location.href = '/views/crm/proposals/management'}
          />

          <StatCard
            title="Taxa de Conversão"
            value={`${conversionRate}%`}
            description="Propostas aceitas / total"
            icon={<PercentIcon className="h-4 w-4 text-green-500" />}
            color={COLORS.primary}
            trend={Number(controlPanelData.find(item => item.title === 'Taxa de conversão')?.trend) || 0}
          />

          <StatCard
            title="Projetos Concluídos"
            value={findData('Projetos concluidos')}
            description="Projetos finalizados com sucesso"
            icon={<CheckCircle2 className="h-4 w-4 text-green-600" />}
            color={COLORS.success}
            trend={Number(controlPanelData.find(item => item.title === 'Projetos concluidos')?.trend) || 0}
            onClick={() => window.location.href = '/views/crm/proposals/completed'}
          />
        </div>

        {/* Gráficos e estatísticas detalhadas */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Gráfico de propostas por tipo de serviço */}
          <ChartCard
            title="Tipos de Contrato"
            description="Distribuição por tipo de contrato nos últimos 6 meses"
            icon={<SquareStack className="h-4 w-4 text-gray-500" />}
          >
            <div className={`h-64 w-full ${isMobile ? 'flex justify-center items-center' : ''}`}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  layout="vertical"
                  data={proposalByServiceTypeData}
                  margin={isMobile ? { top: 5, right: 10, left: 10, bottom: 5 } : { top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" horizontal={false} />
                  <XAxis type="number" />
                  <YAxis
                    dataKey="name"
                    type="category"
                    width={isMobile ? 80 : 100}
                    tick={{ fontSize: isMobile ? 10 : 12 }}
                  />
                  <Tooltip
                    formatter={(value) => [`${value} contratos`, 'Quantidade']}
                    contentStyle={isMobile ? { fontSize: '10px' } : undefined}
                  />
                  <Bar
                    dataKey="value"
                    fill={COLORS.secondary}
                    radius={[0, 4, 4, 0]}
                  >
                    {proposalByServiceTypeData.map((_, index) => (
                      <Cell key={`cell-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
          </ChartCard>

          {/* Gráfico de faturamento */}
          <ChartCard
            title="Faturamento Recente"
            description={selectedServiceTypeForRevenue ? `Valor das propostas concluídas nos últimos 6 meses - ${SERVICE_TYPE_LABELS[selectedServiceTypeForRevenue] || selectedServiceTypeForRevenue}` : "Valor das propostas concluídas nos últimos 6 meses"}
            icon={<LineChart className="h-4 w-4 text-gray-500" />}
            headerContent={
              <div className="flex items-center space-x-2">
                <span className="text-xs text-gray-500">Filtrar por tipo:</span>
                <Select value={selectedServiceTypeForRevenue || "ALL"} onValueChange={handleServiceTypeChangeForRevenue} disabled={loadingRevenueChart}>
                  <SelectTrigger className="h-8 w-48">
                    {loadingRevenueChart ? (
                      <div className="flex items-center">
                        <Loader2 className="h-3 w-3 animate-spin mr-2" />
                        <span>Carregando...</span>
                      </div>
                    ) : (
                      <SelectValue placeholder="Todos os tipos" />
                    )}
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">Todos os tipos</SelectItem>
                    {SERVICE_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            }
          >
            <div className={`h-64 w-full ${isMobile ? 'flex justify-center items-center' : ''} relative`}>
              {loadingRevenueChart && (
                <div className="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-10">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              )}
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={revenueData}
                  margin={isMobile ? { top: 5, right: 5, left: 0, bottom: 5 } : { top: 5, right: 30, left: 0, bottom: 5 }}
                >
                  <defs>
                    <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor={COLORS.primary} stopOpacity={0.8} />
                      <stop offset="95%" stopColor={COLORS.primary} stopOpacity={0.1} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f0f0f0" />
                  <XAxis dataKey="name" tick={{ fontSize: isMobile ? 10 : 12 }} />
                  <YAxis
                    tickFormatter={(value) =>
                      `R$ ${value.toLocaleString('pt-BR', {
                        notation: 'compact',
                        compactDisplay: 'short'
                      })}`
                    }
                    tick={{ fontSize: isMobile ? 10 : 12 }}
                  />
                  <Tooltip
                    formatter={(value: any) =>
                      [`R$ ${Number(value).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 'Valor de Propostas Concluídas']
                    }
                    contentStyle={isMobile ? { fontSize: '10px', padding: '5px' } : undefined}
                  />
                  <Area
                    type="monotone"
                    dataKey="value"
                    stroke={COLORS.primary}
                    fillOpacity={1}
                    fill="url(#colorRevenue)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </ChartCard>

          {/* Gráfico de valor por metro quadrado */}
          <ChartCard
            title="Valor por Metro Quadrado"
            description={selectedServiceTypeForValue ? `Últimos 6 meses - ${SERVICE_TYPE_LABELS[selectedServiceTypeForValue] || selectedServiceTypeForValue}` : "Últimos 6 meses"}
            icon={<Ruler className="h-4 w-4 text-gray-500" />}
            headerContent={
              <div className="flex items-center space-x-2">
                <span className="text-xs text-gray-500">Filtrar por tipo:</span>
                <Select value={selectedServiceTypeForValue || "ALL"} onValueChange={handleServiceTypeChangeForValue} disabled={loadingValueChart}>
                  <SelectTrigger className="h-8 w-48">
                    {loadingValueChart ? (
                      <div className="flex items-center">
                        <Loader2 className="h-3 w-3 animate-spin mr-2" />
                        <span>Carregando...</span>
                      </div>
                    ) : (
                      <SelectValue placeholder="Todos os tipos" />
                    )}
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">Todos os tipos</SelectItem>
                    {SERVICE_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            }
          >
            <div className="h-64 w-full relative">
              {loadingValueChart && (
                <div className="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-10">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              )}
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={valuePerSquareMeterData}
                  margin={isMobile ? { top: 5, right: 5, left: 0, bottom: 5 } : { top: 5, right: 30, left: 0, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f0f0f0" />
                  <XAxis dataKey="name" tick={{ fontSize: isMobile ? 10 : 12 }} />
                  <YAxis
                    tickFormatter={(value) => `R$ ${value}`}
                    tick={{ fontSize: isMobile ? 10 : 12 }}
                  />
                  <Tooltip
                    formatter={(value: any) =>
                      [`R$ ${Number(value).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 'Valor/m²']
                    }
                  />
                  <Bar
                    dataKey="value"
                    fill={COLORS.secondary}
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </ChartCard>
        </div>

        {/* Seção de projetos por status */}
        <div className="grid grid-cols-1 gap-4">
          <ChartCard
            title="Projetos por Status"
            description="Visão geral da situação atual"
            icon={<BarChart3 className="h-4 w-4 text-gray-500" />}
          >
            <div className="h-72 w-full">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={projectStatusData}
                  layout="vertical"
                  margin={isMobile ? { top: 5, right: 10, left: 20, bottom: 5 } : { top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                  <XAxis type="number" />
                  <YAxis
                    type="category"
                    dataKey="name"
                    tick={{ fontSize: isMobile ? 10 : 12 }}
                    width={isMobile ? 70 : 100}
                  />
                  <Tooltip
                    formatter={(value, name, props) => {
                      // Se for a coluna de concluídos, mostra o ano
                      if (props?.payload?.name?.startsWith('Concluídos')) {
                        return [`${value} propostas concluídas em ${currentYear}`, 'Quantidade'];
                      }
                      return [`${value} propostas`, 'Quantidade'];
                    }}
                    contentStyle={isMobile ? { fontSize: '10px', padding: '5px' } : undefined}
                  />
                  <Bar
                    dataKey="value"
                    name="name"
                    radius={[0, 4, 4, 0]}
                  >
                    {projectStatusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
            <div className="flex flex-wrap items-center gap-x-4 gap-y-2 mt-4">
              {projectStatusData.map((status, index) => (
                <div key={index} className="flex items-center min-w-[120px]">
                  <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: status.color }}></div>
                  <span className="text-xs whitespace-nowrap">{status.name}: {status.value}</span>
                </div>
              ))}
            </div>
          </ChartCard>
        </div>

        {/* Links rápidos */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-gray-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center">
                <Users className="h-4 w-4 mr-2 text-blue-500" />
                Gerenciar Clientes
              </CardTitle>
            </CardHeader>
            <CardContent className="text-sm text-gray-500">
              Cadastre, edite e gerencie seus clientes
            </CardContent>
            <CardFooter className="pt-0">
              <Link href="/views/crm/customers" className="text-blue-500 text-xs font-medium flex items-center">
                Acessar <ChevronRight className="h-3 w-3 ml-1" />
              </Link>
            </CardFooter>
          </Card>

          <Card className="hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-gray-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center">
                <FileText className="h-4 w-4 mr-2 text-purple-500" />
                Gerenciar Propostas
              </CardTitle>
            </CardHeader>
            <CardContent className="text-sm text-gray-500">
              Crie e acompanhe suas propostas
            </CardContent>
            <CardFooter className="pt-0">
              <Link href="/views/crm/proposals/management" className="text-purple-500 text-xs font-medium flex items-center">
                Acessar <ChevronRight className="h-3 w-3 ml-1" />
              </Link>
            </CardFooter>
          </Card>

          <Card className="hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-gray-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center">
                <Clock className="h-4 w-4 mr-2 text-amber-500" />
                Projetos em Andamento
              </CardTitle>
            </CardHeader>
            <CardContent className="text-sm text-gray-500">
              Acompanhe os projetos em execução
            </CardContent>
            <CardFooter className="pt-0">
              <Link href="/views/crm/proposals/accepted" className="text-amber-500 text-xs font-medium flex items-center">
                Acessar <ChevronRight className="h-3 w-3 ml-1" />
              </Link>
            </CardFooter>
          </Card>

          <Card className="hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-gray-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center">
                <CircleDollarSign className="h-4 w-4 mr-2 text-green-500" />
                Indicadores de Desempenho
              </CardTitle>
            </CardHeader>
            <CardContent className="text-sm text-gray-500">
              Visualize métricas e KPIs detalhados
            </CardContent>
            <CardFooter className="pt-0">
              <Link href="/views/crm/kpi" className="text-green-500 text-xs font-medium flex items-center">
                Acessar <ChevronRight className="h-3 w-3 ml-1" />
              </Link>
            </CardFooter>
          </Card>
        </div>
      </div>
    </ContentWrapper>
  );
}