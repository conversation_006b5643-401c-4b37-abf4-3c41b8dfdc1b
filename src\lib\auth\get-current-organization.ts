import { auth } from "@/src/providers/auth";

export async function getCurrentOrganization() {
  const session = await auth();
  const organizationId = session?.membership?.organizationId;
  
  if (!organizationId) {
    throw new Error("No organization found for current user");
  }
  
  return {
    organizationId,
    role: session.membership?.role,
    organizationName: session.membership?.organizationName
  };
}