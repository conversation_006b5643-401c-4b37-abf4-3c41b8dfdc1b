"use client";

import { findProposal, loadProposals, updateProposalsPositions } from "@/src/actions/proposals";
import { useRouter } from "next/navigation";
import { useEffect, useState, useMemo } from "react";
import { DragDropContext, Droppable, Draggable, DropResult } from "@hello-pangea/dnd";
import { Proposal, ProposalSituation } from "@/src/types/core/proposal";
import { Customer } from "@/src/types/core/customer";
import { ProposalTemplateInterface } from "@/src/types/core/proposal-template";
import { ServicesScope } from "@/src/types/core/services-scope";
import { toast } from "@/src/hooks/use-toast";
import ProposalCard from "../../management/components/proposal-card";
import ProposalForm from "../../_components/proposal-form";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/src/components/ui/sheet";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/src/components/ui/dialog";
import { Button } from "@/src/components/ui/button";
import { AlertCircle, FileText, Lock, Edit, CheckCircle2, FilePlus } from "lucide-react";
import LoadingBar from "@/src/components/loading-bar";
import { DateRangePicker } from "@/src/app/views/crm/kpi/components/date-range-picker";
import { DateRange } from "react-day-picker";
import { CustomerSearch } from "./customer-search";
import { ServiceTypeFilter } from "./service-type-filter";

// Definição das situações de proposta para as colunas do Kanban
const proposalSituations = [
  { value: "NEW", label: "Nova proposta" },
  { value: "UNDER_ANALYSIS", label: "Em análise" },
  { value: "PROPOSAL_SENT", label: "Proposta enviada" },
  { value: "PROPOSAL_ACCEPTED", label: "Proposta aceita" },
  { value: "SIGN_REQUESTED", label: "Solicitação de assinatura" },
  { value: "SIGNED", label: "Assinado" },
  { value: "PROJECT_IN_PROGRESS", label: "Projeto em andamento" },
  { value: "PROJECT_FINISHED", label: "Projeto concluído" },
  { value: "LOST", label: "Perdido" },
];

export default function KanbanByPeriod() {
  const router = useRouter();
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [templates, setTemplates] = useState<ProposalTemplateInterface[]>([]);
  const [sheetOpen, setSheetOpen] = useState(false);
  const [proposal, setProposal] = useState<Proposal | undefined>(undefined);
  const [serviceScopes, setServiceScopes] = useState<ServicesScope[]>([]);
  const [isContractValidated, setIsContractValidated] = useState(false);
  const [showContractDialog, setShowContractDialog] = useState(false);
  const [showNoReturnDialog, setShowNoReturnDialog] = useState(false);
  const [proposalToCheck, setProposalToCheck] = useState<Proposal | null>(null);

  // Estado para o filtro de datas
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().getFullYear(), 0, 1), // 1º de janeiro do ano atual
    to: new Date(new Date().getFullYear(), 11, 31)  // 31 de dezembro do ano atual
  });

  // Estado para o filtro de cliente
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>("");

  // Estado para o filtro de tipo de serviço
  const [selectedServiceTypes, setSelectedServiceTypes] = useState<string[]>([]);

  // Verificar se a proposta está em uma situação que requer template de contrato
  const shouldUseContractTemplate = useMemo(() => {
    if (!proposal) return false;

    // Situações que usam templates de contrato
    const contractSituations = [
      "PROPOSAL_ACCEPTED", // Adicionado para mostrar apenas templates de contrato na coluna "Proposta aceita"
      "SIGN_REQUESTED",
      "SIGNED",
      "PROJECT_IN_PROGRESS",
      "PROJECT_FINISHED"
    ];

    return contractSituations.includes(proposal.situation);
  }, [proposal]);

  // Atualizar o estado isContractValidated e recarregar templates quando a proposta mudar
  useEffect(() => {
    setIsContractValidated(shouldUseContractTemplate);

    // Se temos uma proposta selecionada, recarregar os templates
    if (proposal) {
      fetchTemplates();
    }
  }, [shouldUseContractTemplate, proposal]);

  // Função para buscar propostas com filtro de data
  const fetchProposals = async () => {
    try {
      setIsLoading(true);

      const result = await loadProposals({
        ignorePermissions: false,
        customerId: selectedCustomerId || undefined,
        serviceTypes: selectedServiceTypes,
        dateRange:
          dateRange?.from && dateRange?.to
            ? { from: dateRange.from, to: dateRange.to }
            : undefined,
      });
      const proposals = Array.isArray(result) ? result : result.data;
      const proposalsWithDate = proposals.map((proposal) => ({
        ...proposal,
        startDate: proposal?.startDate ? new Date(proposal.startDate) : new Date(),
        endDate: proposal?.endDate ? new Date(proposal.endDate) : new Date(),
        area: Number(proposal.area),
        budget: Number(proposal.budget),
        workTotalCost: proposal.workTotalCost ? Number(proposal.workTotalCost) : undefined,
        downPayment: proposal.downPayment ? Number(proposal.downPayment) : undefined,
        installmentAmount: proposal.installmentAmount ? Number(proposal.installmentAmount) : undefined,
        installmentNumber: proposal.installmentNumber ? Number(proposal.installmentNumber) : undefined,
        customService: proposal.customService || undefined,
        cep: proposal.cep || undefined,
        address: proposal.address || undefined,
        city: proposal.city || undefined,
        state: proposal.state || undefined,
        serviceType: proposal.serviceType || undefined,
        proposalTemplateId: proposal.proposalTemplateId || undefined,
        fileEditorId: proposal.fileEditorId || undefined,
        fileId: proposal.fileId || undefined
      }));

      setProposals(proposalsWithDate);
    } catch (error) {
      console.error("Erro ao buscar projetos:", error);
      setProposals([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Carregar dados iniciais
  useEffect(() => {
    const loadInitialData = async () => {
      await Promise.all([
        fetchCustomers(),
        fetchTemplates(),
        fetchServiceScopes()
      ]);
      fetchProposals();
    };

    loadInitialData();
  }, []);

  // Atualizar propostas quando os filtros mudarem
  useEffect(() => {
    fetchProposals();
  }, [dateRange, selectedCustomerId, selectedServiceTypes]);

  // Atualizar propostas quando a proposta mudar
  useEffect(() => {
    if (proposal) {
      fetchProposals();
    }
  }, [proposal]);

  // Verificar se deve reabrir o formulário ao retornar do document-editor
  useEffect(() => {
    const checkReturnToForm = async () => {
      const returnInfo = sessionStorage.getItem('returnToProposalForm');
      if (returnInfo) {
        try {
          const { proposalId, isEditing } = JSON.parse(returnInfo);

          // Limpar a informação do sessionStorage
          sessionStorage.removeItem('returnToProposalForm');

          if (isEditing && proposalId) {
            // Buscar a proposta pelo ID e reabrir o formulário
            const proposalData = await findProposal(proposalId);
            if (proposalData) {
              // Carregar os dados necessários para o formulário
              const [customersData, templatesData, scopesData] = await Promise.all([
                fetchCustomers(),
                fetchTemplates(),
                fetchServiceScopes(),
              ]);

              setCustomers(customersData);
              setTemplates(templatesData);
              setServiceScopes(scopesData);

              // Definir a proposta e abrir o formulário
              setProposal(proposalData);
              setSheetOpen(true);
            }
          }
        } catch (error) {
          console.error('Erro ao reabrir formulário:', error);
        }
      }
    };

    checkReturnToForm();
  }, []);

  // Função para lidar com o arrastar e soltar
  const onDragEnd = async (result: DropResult) => {
    const { source, destination, draggableId } = result;

    // Se não houver destino, não faz nada
    if (!destination) return;

    const sourceColumnId = source.droppableId;
    const destinationColumnId = destination.droppableId;

    // Se a posição não mudou, não faz nada
    if (
      sourceColumnId === destinationColumnId &&
      source.index === destination.index
    ) {
      return;
    }

    const proposalId = draggableId;
    const movedProposal = proposals.find((p) => p.id === proposalId);
    if (!movedProposal) return;

    // Definir os grupos de colunas
    const initialColumns = ["NEW", "UNDER_ANALYSIS", "PROPOSAL_SENT", "PROPOSAL_ACCEPTED"];
    const advancedColumns = ["SIGN_REQUESTED", "SIGNED", "PROJECT_IN_PROGRESS", "PROJECT_FINISHED"];
    const lostColumn = "LOST";

    // Se a proposta está sendo movida de/para a coluna "Perdido", permitir o movimento
    if (sourceColumnId === lostColumn || destinationColumnId === lostColumn) {
      // Permitir o movimento sem verificações adicionais
    }
    // Verificar se a proposta está sendo movida para a coluna "Proposta aceita"
    else if (destinationColumnId === "PROPOSAL_ACCEPTED") {
      // Verificar se a proposta está sendo movida de uma coluna avançada para "Proposta aceita"
      if (advancedColumns.includes(sourceColumnId)) {
        // Mostrar diálogo explicativo sobre não poder retroceder
        setProposalToCheck(movedProposal);
        setShowNoReturnDialog(true);
        return; // Impedir o movimento
      }

      // Permitir o movimento - o vínculo com o template será removido no servidor
    }
    // Verificar se a proposta está sendo movida entre os grupos de colunas (inicial para avançado)
    else if (initialColumns.includes(sourceColumnId) && advancedColumns.includes(destinationColumnId)) {
      // Verificar se a proposta está na coluna "Proposta aceita"
      if (sourceColumnId !== "PROPOSAL_ACCEPTED") {
        // Mostrar diálogo explicativo
        setProposalToCheck(movedProposal);
        setShowContractDialog(true);
        return; // Impedir o movimento
      }

      // Verificar se a proposta tem um template de contrato associado
      const hasContractTemplate = movedProposal.proposalTemplate &&
        movedProposal.proposalTemplate.type === "CONTRACT";

      if (!hasContractTemplate) {
        // Mostrar diálogo explicativo
        setProposalToCheck(movedProposal);
        setShowContractDialog(true);
        return; // Impedir o movimento
      }
    }
    // Verificar se a proposta está sendo movida entre os grupos de colunas (avançado para inicial)
    else if (advancedColumns.includes(sourceColumnId) && initialColumns.includes(destinationColumnId)) {
      // Mostrar diálogo explicativo sobre não poder retroceder
      setProposalToCheck(movedProposal);
      setShowNoReturnDialog(true);
      return; // Impedir o movimento
    }

    // Clonar os projetos para evitar mutação direta do estado
    let updatedProposals = [...proposals];

    // Remover o projeto da posição original
    updatedProposals = updatedProposals.filter((p) => p.id !== proposalId);

    // Atualizar o status se mudou de coluna
    if (sourceColumnId !== destinationColumnId) {
      movedProposal.situation = destinationColumnId as ProposalSituation;
    }

    // Obter todos os projetos na coluna de destino já ordenados
    const destinationProposals = updatedProposals
      .filter((p) => p.situation === destinationColumnId)
      .sort((a, b) => (a.order ?? 0) - (b.order ?? 0));

    // Inserir o projeto movido na posição desejada na coluna de destino
    destinationProposals.splice(destination.index, 0, movedProposal);

    // Atualizar a ordem dos projetos na coluna de destino
    const updatedDestinationProposal = destinationProposals.map((p, index) => ({
      ...p,
      order: index,
    }));

    // Atualizar os projetos na coluna de destino no estado
    updatedProposals = [
      ...updatedProposals.filter((p) => p.situation !== destinationColumnId),
      ...updatedDestinationProposal,
    ];

    // Se mudou de coluna, atualizar a ordem na coluna de origem
    if (sourceColumnId !== destinationColumnId) {
      const sourceProposals = updatedProposals
        .filter((p) => p.situation === sourceColumnId)
        .sort((a, b) => (a.order ?? 0) - (b.order ?? 0));

      const updatedSourceProjects = sourceProposals.map((p, index) => ({
        ...p,
        order: index,
      }));

      updatedProposals = [
        ...updatedProposals.filter((p) => p.situation !== sourceColumnId),
        ...updatedSourceProjects,
      ];
    }

    // Atualizar o estado local
    setProposals(updatedProposals);

    // Enviar as atualizações para o servidor
    try {
      setIsLoading(true);
      const proposalsToUpdate = [
        ...updatedDestinationProposal,
        ...(sourceColumnId !== destinationColumnId
          ? updatedProposals.filter((p) => p.situation === sourceColumnId)
          : []),
      ];

      // Remover duplicatas
      const uniqueProposalsToUpdate = Array.from(
        new Map(proposalsToUpdate.map((p) => [p.id, p])).values()
      );

      await updateProposalsPositions(uniqueProposalsToUpdate);
    } catch (error) {
      console.error("Erro ao atualizar projetos:", error);
      // Refazer a requisição para recuperar o estado original
      fetchProposals();
    } finally {
      fetchProposals();
      setIsLoading(false)
    }
  };

  // Função para lidar com a mudança de status da proposta
  const handleStatusChange = async (proposalId: string, newStatus: string) => {
    try {
      setIsLoading(true);

      // Encontrar a proposta
      const proposalToUpdate = proposals.find(p => p.id === proposalId);
      if (!proposalToUpdate) return;

      // Atualizar o status
      const updatedProposal = {
        ...proposalToUpdate,
        situation: newStatus as ProposalSituation
      };

      // Criar uma cópia das propostas
      const updatedProposals = proposals.map(p =>
        p.id === proposalId ? updatedProposal : p
      );

      // Atualizar o estado local
      setProposals(updatedProposals);

      // Enviar a atualização para o servidor
      await updateProposalsPositions([updatedProposal]);

      // Recarregar as propostas
      fetchProposals();
    } catch (error) {
      console.error("Erro ao atualizar status da proposta:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao atualizar o status da proposta.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Funções para buscar dados
  const fetchCustomers = async () => {
    try {
      const res = await fetch("/api/customers?pageSize=30"); // Carregar todos os clientes
      if (!res.ok) throw new Error("Erro ao carregar clientes");

      const result = await res.json();

      if (result && result.data) {
        const formattedCustomers = result.data.map((customer: any) => ({
          ...customer,
          documentType: customer.documentType || 'CPF' // Provide a default value if null
        })) as Customer[];

        setCustomers(formattedCustomers);
        return formattedCustomers;
      }
      return [];
    } catch (error) {
      console.error("Erro ao carregar clientes:", error);
      return [];
    }
  };

  // Função para determinar o tipo de template com base na situação da proposta
  const getTemplateTypeForSituation = (situation: ProposalSituation | string) => {
    // Situações que devem mostrar apenas templates de contrato
    const contractSituations = [
      "PROPOSAL_ACCEPTED",
      "SIGN_REQUESTED",
      "SIGNED",
      "PROJECT_IN_PROGRESS",
      "PROJECT_FINISHED"
    ];

    // Situações que devem mostrar apenas templates de proposta
    const proposalSituations = [
      "NEW",
      "UNDER_ANALYSIS",
      "PROPOSAL_SENT"
    ];

    if (contractSituations.includes(situation)) {
      return "CONTRACT";
    } else if (proposalSituations.includes(situation)) {
      return "PROPOSAL";
    }

    // Se não for nenhuma das situações específicas, retorna null (sem filtro)
    return null;
  };

  const fetchTemplates = async () => {
    try {
      const res = await fetch("/api/templates?pageSize=1000&getFiles=true"); // Carregar todos os templates
      if (!res.ok) throw new Error("Erro ao carregar templates");

      const result = await res.json();

      if (result && result.data) {
        // Se temos uma proposta selecionada, filtrar templates com base na situação
        if (proposal) {
          const templateType = getTemplateTypeForSituation(proposal.situation);

          if (templateType) {
            // Filtrar templates pelo tipo apropriado, mas incluir o template atual da proposta
            const filteredTemplates = result.data.filter((template: ProposalTemplateInterface) =>
              template.type === templateType ||
              (proposal.proposalTemplateId && template.id === proposal.proposalTemplateId)
            );

            setTemplates(filteredTemplates);
            return filteredTemplates;
          }
        }

        // Se não há proposta selecionada ou não há filtro específico, usar todos os templates
        setTemplates(result.data);
        return result.data;
      }
      return [];
    } catch (error) {
      console.error("Erro ao carregar templates:", error);
      return [];
    }
  };

  const fetchServiceScopes = async () => {
    try {
      const params = new URLSearchParams();
      params.append('type', 'PROPOSAL_SERVICE');
      params.append('pageSize', '1000'); // Carregar todos os escopos

      const res = await fetch(`/api/service-scopes?${params.toString()}`);
      if (!res.ok) throw new Error("Erro ao carregar escopo de serviços");

      const result = await res.json();

      if (result && result.data) {
        setServiceScopes(result.data);
        return result.data;
      }
      return [];
    } catch (error) {
      console.error("Erro ao carregar escopos de serviço:", error);
      return [];
    }
  };

  // Função para abrir o formulário de edição de proposta
  const handleProposalSheet = async (proposal: Proposal) => {
    try {
      setIsLoading(true);

      // Carregar dados necessários para o formulário
      const [templatesData] = await Promise.all([
        fetchCustomers(),
        fetchTemplates(),
        fetchServiceScopes()
      ]);

      // Verificar se o template da proposta está na lista de templates carregados
      if (proposal.proposalTemplateId) {
        const templateExists = templatesData.some(t => t.id === proposal.proposalTemplateId);

        if (!templateExists) {
          // Se o template não estiver na lista, buscar especificamente esse template
          try {
            const res = await fetch(`/api/templates/${proposal.proposalTemplateId}`);
            if (res.ok) {
              const templateData = await res.json();

              // Adicionar o template à lista se ele não estiver lá
              if (templateData && !templatesData.some(t => t.id === templateData.id)) {
                templatesData.push(templateData);
                // setTemplates([...templatesData]);
              }
            }
          } catch {
            console.error('Erro ao buscar template específico');
          }
        }
      }

      setProposal(proposal);
      setSheetOpen(true);
    } catch (error) {
      console.error("Erro ao carregar dados para o formulário:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao carregar os dados. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Função para lidar com o clique no card
  const handleCardClick = (proposal: Proposal) => {
    if (proposal.situation === "SIGNED") {
      router.push("/views/crm/proposals/to-start");
    } else if (proposal.situation === "PROJECT_IN_PROGRESS") {
      router.push("/views/crm/proposals/accepted");
    } else if (proposal.situation === "LOST") {
      router.push("/views/crm/proposals/lost");
    } else if (proposal.situation === "PROJECT_FINISHED") {
      router.push("/views/crm/proposals/completed");
    } else {
      handleProposalSheet(proposal);
    }
  };

  // Função para obter a cor da coluna
  const getColumnColor = (situation: string) => {
    switch (situation) {
      case "NEW":
        return "from-blue-50 to-blue-100 border-blue-200";
      case "UNDER_ANALYSIS":
        return "from-blue-50 to-blue-100 border-blue-200";
      case "PROPOSAL_SENT":
        return "from-blue-50 to-blue-100 border-blue-200";
      case "PROPOSAL_ACCEPTED":
        return "from-blue-50 to-blue-100 border-blue-200";
      case "SIGN_REQUESTED":
        return "from-greenLogo-50 to-greenLogo-100 border-gray-200";
      case "SIGNED":
        return "from-greenLogo-50 to-greenLogo-100 border-gray-200";
      case "PROJECT_IN_PROGRESS":
        return "from-greenLogo-50 to-greenLogo-100 border-gray-200";
      case "PROJECT_FINISHED":
        return "from-greenLogo-50 to-greenLogo-100 border-gray-200";
      case "LOST":
        return "from-red-50 to-red-100 border-red-200";
      default:
        return "from-greenLogo-50 to-greenLogo-100 border-gray-200";
    }
  };

  // Função para obter a cor do cabeçalho da coluna
  const getHeaderColor = (situation: string) => {
    switch (situation) {
      case "NEW":
        return "bg-blue-800 text-white";
      case "UNDER_ANALYSIS":
        return "bg-blue-800 text-white";
      case "PROPOSAL_SENT":
        return "bg-blue-800 text-white";
      case "PROPOSAL_ACCEPTED":
        return "bg-blue-800 text-white";
      case "SIGN_REQUESTED":
        return "bg-greenLogo-500 text-white";
      case "SIGNED":
        return "bg-greenLogo-500 text-white";
      case "PROJECT_IN_PROGRESS":
        return "bg-greenLogo-500 text-white";
      case "PROJECT_FINISHED":
        return "bg-greenLogo-500 text-white";
      case "LOST":
        return "bg-red-800 text-white";
      default:
        return "bg-gray-800 text-white";
    }
  };

  return (
    <div className="w-full pt-2 flex flex-col">
      <LoadingBar isLoading={isLoading} />

      {/* Filtros */}
      <div className="w-full p-5 mb-6 bg-gradient-to-r from-green-50 to-white rounded-lg shadow-md border border-green-100">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div>
            <h3 className="text-lg font-semibold mb-1 text-green-800">Filtrar propostas</h3>
            <p className="text-sm text-green-600">Selecione um cliente, tipo de serviço e/ou um intervalo de datas para filtrar as propostas</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
            {/* Filtro de cliente */}
            <div className="w-full sm:w-64">
              <CustomerSearch
                onCustomerChange={setSelectedCustomerId}
              />
            </div>

            {/* Filtro de tipo de serviço */}
            <div className="w-full sm:w-64">
              <ServiceTypeFilter
                onServiceTypesChange={setSelectedServiceTypes}
              />
            </div>

            {/* Filtro de datas */}
            <div className="w-full sm:w-64">
              <DateRangePicker
                dateRange={dateRange}
                onDateRangeChange={setDateRange}
                className="w-full"
              />
            </div>
          </div>
        </div>
      </div>

      <DragDropContext onDragEnd={onDragEnd}>
        <div className="w-full overflow-hidden">
          <div className="flex flex-nowrap gap-3 pb-6 pr-4 overflow-x-auto md:overflow-x-auto custom-scrollbar" style={{ WebkitOverflowScrolling: 'touch', paddingLeft: '4px' }}>
            {proposalSituations.map((situation) => (
              <Droppable key={situation.value} droppableId={situation.value}>
                {(provided, snapshot) => {
                  // Filtrar propostas para esta coluna
                  const columnProposals = proposals
                    .filter(proposal => proposal.situation === situation.value)
                    .sort((a, b) => (a.order ?? 0) - (b.order ?? 0));

                  return (
                    <div
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className={`flex flex-col border rounded-xl shadow-lg min-h-[500px] min-w-[240px] w-[240px] max-w-[240px] overflow-hidden transition-all duration-300 ${snapshot.isDraggingOver ? 'shadow-xl ring-2 ring-opacity-50 ring-blue-300 scale-[1.02]' : 'hover:shadow-xl hover:translate-y-[-2px]'} ${getColumnColor(situation.value)} bg-gradient-to-b flex-shrink-0`}
                    >
                      <div className={`p-2 ${getHeaderColor(situation.value)} shadow-md relative overflow-hidden border-b-2 border-white/10`}>
                        <div className="flex items-center justify-between relative z-10 px-1">
                          <h2 className="text-base font-semibold drop-shadow-md text-white whitespace-nowrap overflow-hidden text-ellipsis max-w-[190px]" style={{ fontSize: '0.9rem' }}>
                            {situation.label}
                          </h2>
                          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-white bg-opacity-30 text-xs font-bold shadow-inner text-white drop-shadow-sm ml-1 flex-shrink-0">
                            {columnProposals.length}
                          </div>
                        </div>
                      </div>
                      <div className="flex-1 p-2 overflow-y-auto max-h-[calc(100vh-15rem)]">
                        {columnProposals.length === 0 && (
                          <div className="flex items-center justify-center h-24 rounded-lg bg-white bg-opacity-50 border border-dashed border-gray-300 text-gray-400 text-sm">
                            Nenhuma proposta
                          </div>
                        )}

                        {columnProposals.map((proposal, index) => (
                          <Draggable
                            key={proposal.id}
                            draggableId={proposal.id!.toString()}
                            index={index}
                          >
                            {(provided, snapshot) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                className={`mb-3 transition-transform duration-200 ${snapshot.isDragging ? 'rotate-1 scale-105' : ''}`}
                                style={{
                                  ...provided.draggableProps.style,
                                }}
                              >
                                <ProposalCard
                                  proposal={proposal}
                                  onClick={() => handleCardClick(proposal)}
                                  isLoading={isLoading}
                                  columnType={situation.value}
                                />
                              </div>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </div>
                    </div>
                  );
                }}
              </Droppable>
            ))}
          </div>
        </div>
      </DragDropContext>

      {/* Sheet para edição de proposta */}
      <Sheet
        open={sheetOpen}
        onOpenChange={(val) => {
          // Se estiver fechando o form (val = false)
          if (!val) {
            // Fechar o form
            setSheetOpen(false);
            // Atualizar o kanban
            fetchProposals();
          }
        }}
      >
        <SheetContent className="w-[90%] sm:min-w-[450px] h-screen p-4">
          <SheetHeader>
            <SheetTitle />
            <SheetDescription />
          </SheetHeader>
          {proposal && (
            <ProposalForm
              scopes={serviceScopes}
              proposal={proposal}
              templates={templates}
              customers={customers}
              onChange={() => fetchProposals()}
              onCancelClick={() => {
                setSheetOpen(false);
                // Atualizar o kanban ao fechar o formulário
                fetchProposals();
              }}
              onStatusChange={handleStatusChange}
              isContractValidated={isContractValidated}
              isEditing={true} // Indica que estamos editando uma proposta existente
              isLoading={isLoading}
            />
          )}
        </SheetContent>
      </Sheet>

      {/* Diálogo de aviso sobre conversão para contrato */}
      <Dialog open={showContractDialog} onOpenChange={setShowContractDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-amber-600">
              <AlertCircle className="h-5 w-5" />
              Conversão para Contrato Necessária
            </DialogTitle>
            <DialogDescription className="pt-4 text-base">
              <div className="space-y-4">
                <p>
                  Esta proposta ainda não foi convertida em um <strong>contrato</strong>.
                </p>
                <p>
                  Para avançar para as etapas de assinatura e execução do projeto, é necessário editar a proposta e selecionar um <strong>template de contrato</strong>.
                </p>
                <div className="bg-amber-50 p-4 rounded-md border border-amber-200">
                  <h3 className="font-medium text-amber-800 flex items-center gap-2 mb-2">
                    <FileText className="h-4 w-4" /> Como converter em contrato:
                  </h3>
                  {proposalToCheck && proposalToCheck.situation === "PROPOSAL_ACCEPTED" ? (
                    <ol className="list-decimal pl-5 space-y-2 text-amber-700">
                      <li>Clique no botão &ldquo;Editar Proposta&rdquo; abaixo</li>
                      <li>Na seção &ldquo;Vincular template&rdquo;, selecione um template do tipo <strong>contrato</strong></li>
                      <li>Salve as alterações</li>
                      <li>Agora você poderá mover esta proposta para as próximas etapas</li>
                    </ol>
                  ) : (
                    <>
                      <ol className="list-decimal pl-5 space-y-2 text-amber-700">
                        <li>Primeiro, mova esta proposta para a coluna &ldquo;Proposta aceita&rdquo;</li>
                        <li>Ao mover para &ldquo;Proposta aceita&rdquo;, você precisará selecionar um template de contrato</li>
                        <li>Em seguida, edite a proposta e selecione um template do tipo <strong>contrato</strong></li>
                        <li>Salve as alterações</li>
                        <li>Agora você poderá mover esta proposta para as próximas etapas</li>
                      </ol>
                      <div className="mt-3 p-2 bg-amber-50 border border-amber-200 rounded-md">
                        <p className="text-amber-700 text-sm flex items-start gap-2">
                          <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                          <span>Importante: Ao mover para &ldquo;Proposta aceita&rdquo;, você precisará editar a proposta e selecionar um template de contrato antes de prosseguir para as próximas etapas.</span>
                        </p>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="sm:justify-center gap-2 mt-4">
            <Button
              variant="outline"
              onClick={() => setShowContractDialog(false)}
            >
              Fechar
            </Button>
            {proposalToCheck && proposalToCheck.situation === "PROPOSAL_ACCEPTED" && (
              <Button
                className="bg-green-500 hover:bg-green-600"
                onClick={() => {
                  setShowContractDialog(false);
                  if (proposalToCheck) {
                    handleProposalSheet(proposalToCheck);
                  }
                }}
              >
                <Edit className="h-4 w-4 mr-2" /> Editar Proposta
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo de aviso sobre não poder retroceder */}
      <Dialog open={showNoReturnDialog} onOpenChange={setShowNoReturnDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <Lock className="h-5 w-5" />
              Operação Não Permitida
            </DialogTitle>
            <DialogDescription className="pt-4 text-base">
              <div className="space-y-4">
                <p>
                  Esta proposta já avançou para uma etapa de assinatura ou execução do projeto e <strong>não pode retornar</strong> para etapas anteriores de proposta.
                </p>
                <div className="bg-red-50 p-4 rounded-md border border-red-200">
                  <h3 className="font-medium text-red-800 flex items-center gap-2 mb-2">
                    <FileText className="h-4 w-4" /> Por que não é possível retroceder:
                  </h3>
                  <ul className="space-y-3 text-red-700">
                    <li className="flex items-start gap-2">
                      <CheckCircle2 className="h-5 w-5 mt-0.5 flex-shrink-0" />
                      <span>A proposta já foi convertida em contrato e está em uma etapa de assinatura ou execução do projeto</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Lock className="h-5 w-5 mt-0.5 flex-shrink-0" />
                      <span>O processo de assinatura já foi iniciado ou concluído, o que torna o documento legalmente vinculante</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <AlertCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
                      <span>Retroceder neste estágio poderia causar inconsistências nos registros e problemas legais</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <FilePlus className="h-5 w-5 mt-0.5 flex-shrink-0" />
                      <span>Alterações necessárias devem ser feitas através de aditivos contratuais formais</span>
                    </li>
                  </ul>
                </div>
                <p className="text-gray-600 italic">
                  Se você realmente precisa fazer alterações substanciais, considere criar uma nova proposta baseada nesta ou consultar o suporte para orientações específicas.
                </p>
              </div>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="sm:justify-center mt-4">
            <Button
              onClick={() => setShowNoReturnDialog(false)}
              className="bg-red-500 hover:bg-red-600"
            >
              Entendi
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
