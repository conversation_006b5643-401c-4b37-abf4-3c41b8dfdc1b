import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

export async function GET(
  req: Request,
  { params }: { params: { userId: string } }
) {
  try {
    const { organizationId } = await getCurrentOrganization();
    // Ler parâmetros da query string
    const { searchParams } = new URL(req.url);
    const search = searchParams.get("search")?.trim();
    const customerId = searchParams.get("customerId")?.trim();
    const page = parseInt(searchParams.get("page") || "1", 10);
    const pageSize = parseInt(searchParams.get("pageSize") || "20", 10);
    const skip = (page - 1) * pageSize;

    // Montar filtro dinâmico
    const proposalWhere: any = {
      customer: {
        organizationId,
      },
    };
    if (search) {
      proposalWhere.name = { contains: search, mode: "insensitive" };
    }
    if (customerId) {
      proposalWhere.customerId = customerId;
    }

    // Buscar total de propostas para paginação
    const total = await prisma.proposal.count({ where: proposalWhere });
    // Buscar propostas paginadas
    const allProposals = await prisma.proposal.findMany({
      where: proposalWhere,
      select: {
        id: true,
        name: true,
        situation: true,
        customer: {
          select: {
            name: true,
          },
        },
      },
      skip,
      take: pageSize,
    });
    console.log("GET - Total de propostas encontradas:", allProposals.length);

    // Buscar permissões existentes
    const existingPermissions = await prisma.proposalPermission.findMany({
      where: {
        userId: params.userId,
        organizationId,
      },
      select: {
        proposalId: true,
      },
    });
    console.log("GET - Permissões existentes:", existingPermissions);

    // Criar um mapa de propostas com permissão
    const proposalsWithPermission = new Set(existingPermissions.map(p => p.proposalId));

    // Retornar todas as propostas, marcando como habilitadas as que têm permissão
    const permissions = allProposals.map(proposal => ({
      proposalId: proposal.id,
      name: proposal.name,
      situation: proposal.situation,
      customer: proposal.customer,
      enabled: proposalsWithPermission.has(proposal.id),
    }));

    console.log("GET - Retornando permissões:", permissions);
    return NextResponse.json({
      data: permissions,
      total,
      page,
      pageSize,
    });
  } catch (error) {
    console.error("GET - Erro:", error);
    return NextResponse.json(
      { error: "Erro ao carregar permissões de propostas" },
      { status: 500 }
    );
  }
}

export async function POST(
  req: Request,
  { params }: { params: { userId: string } }
) {
  try {
    const { organizationId } = await getCurrentOrganization();
    const { proposalId, enabled } = await req.json();
    console.log("POST - Atualizando permissão:", { proposalId, enabled, userId: params.userId, organizationId });

    if (enabled) {
      // Se está habilitado, adicionar a permissão
      const permission = await prisma.proposalPermission.create({
        data: {
          proposalId,
          userId: params.userId,
          organizationId,
        },
      });
      console.log("POST - Permissão criada:", permission);
    } else {
      // Se está desabilitado, remover a permissão
      const deleted = await prisma.proposalPermission.deleteMany({
        where: {
          proposalId,
          userId: params.userId,
          organizationId,
        },
      });
      console.log("POST - Permissões removidas:", deleted);
    }

    // Verificar o estado atual após a operação
    const currentPermission = await prisma.proposalPermission.findFirst({
      where: {
        proposalId,
        userId: params.userId,
        organizationId,
      },
    });
    console.log("POST - Estado atual da permissão:", currentPermission);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("POST - Erro:", error);
    return NextResponse.json(
      { error: "Erro ao atualizar permissão da proposta" },
      { status: 500 }
    );
  }
}

export async function PATCH(
  req: Request,
  { params }: { params: { userId: string } }
) {
  try {
    const { organizationId } = await getCurrentOrganization();
    const { enabled } = await req.json();
    // Buscar todas as propostas da organização
    const allProposals = await prisma.proposal.findMany({
      where: {
        customer: {
          organizationId,
        },
      },
      select: { id: true },
    });
    const allProposalIds = allProposals.map((p) => p.id);

    if (enabled) {
      // Adicionar permissão para todas as propostas que ainda não têm
      // Buscar permissões já existentes
      const existingPermissions = await prisma.proposalPermission.findMany({
        where: {
          userId: params.userId,
          organizationId,
        },
        select: { proposalId: true },
      });
      const existingIds = new Set(existingPermissions.map((p) => p.proposalId));
      const toCreate = allProposalIds.filter((id) => !existingIds.has(id));
      // Criar permissões em lote
      await prisma.proposalPermission.createMany({
        data: toCreate.map((proposalId) => ({
          proposalId,
          userId: params.userId,
          organizationId,
        })),
        skipDuplicates: true,
      });
    } else {
      // Remover todas as permissões desse usuário para todas as propostas da organização
      await prisma.proposalPermission.deleteMany({
        where: {
          userId: params.userId,
          organizationId,
          proposalId: { in: allProposalIds },
        },
      });
    }
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("PATCH - Erro:", error);
    return NextResponse.json(
      { error: "Erro ao atualizar todas as permissões de propostas" },
      { status: 500 }
    );
  }
} 