import sharp from "sharp";
import XlsxTemplate from "xlsx-template";

export async function mergeVariablesXlsx(
  bufferFile: Buffer<ArrayBufferLike>,
  variables: Record<string, unknown>
) {
  // Converter URLs de imagem para Base64 se houver no objeto de variáveis
  if (variables.items && Array.isArray(variables.items)) {
    for (const item of variables.items) {
      if (
        item.image &&
        typeof item.image === "string" &&
        item.image.startsWith("http")
      ) {
        item.image = await getImageBase64FromUrl(item.image);
      }
    }
  }

  // Criar instância do XlsxTemplate
  const template = new XlsxTemplate(bufferFile);

  // Substituir os placeholders no primeiro sheet (sheetNumber = 1)
  template.substitute(1, variables);

  // Gerar o arquivo final como Buffer
  const outputBuffer = template.generate({
    type: "nodebuffer",
  }) as Buffer<ArrayBufferLike>;

  return outputBuffer;
}

async function getImageBase64FromUrl(url: string): Promise<string> {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Erro ao baixar imagem em: ${url}`);
  }
  const arrayBuffer = await response.arrayBuffer();
  const inputBuffer = Buffer.from(arrayBuffer);

  // Redimensiona a imagem para 300x200 (ajuste as dimensões conforme necessário)
  const resizedBuffer = await sharp(inputBuffer)
    .resize({ width: 100, height: 25, fit: "inside" })
    .toBuffer();

  // Retorna a string Base64 da imagem redimensionada
  return resizedBuffer.toString("base64");
}

// async function streamToBuffer(stream: Readable): Promise<Buffer> {
//   const chunks: Buffer[] = [];
//   for await (const chunk of stream) {
//     chunks.push(chunk);
//   }
//   return Buffer.concat(chunks);
// }
