"use server";

import { prisma } from "@/src/lib/prisma";
import { Prisma } from "@prisma/client";
import { parseCurrencyToNumber, parseObject } from "@/src/lib/utils";
import { RepairBudget } from "@/src/types/core/repair-budget";
import { RepairBudgetSchema } from "@/src/app/views/crm/proposals/services-budget/schemas/repair-budget.schema";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

export async function saveRepairBudget(repairBudget: RepairBudgetSchema) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const {
      id,
      proposalId,
      serviceScopeId,
      financialWeight,
      gravity,
      gut,
      igrf,
      measurementDate,
      serviceCost,
      tendency,
      totalCost,
      urgency,
      startDate,
      endDate,
      equipmentAmount,
      equipmentDescription,
      laborAmount,
    } = repairBudget;

    // Verify proposal belongs to organization
    const proposal = await prisma.proposal.findFirst({
      where: {
        id: proposalId,
        customer: {
          organizationId,
        },
      },
    });

    if (!proposal) {
      throw new Error(
        "Proposta não encontrada ou não pertence a sua organização"
      );
    }

    // Verify serviceScope belongs to organization
    const serviceScope = await prisma.serviceScope.findFirst({
      where: {
        id: serviceScopeId,
        organizationId,
      },
    });

    if (!serviceScope) {
      throw new Error(
        "Serviço não encontrado ou não pertence a sua organização"
      );
    }

    const where = {
      id,
    };

    const include = {
      serviceScope: true,
      proposal: true,
    };

    // Calcular o valor de IMP
    const imp = await loadIMP(serviceScopeId);

    const data = {
      measurementDate,
      startDate,
      endDate,
      financialWeight: Number(financialWeight),
      gravity: Number(gravity),
      gut: Number(gut),
      igrf: Number(igrf),
      serviceCost: parseCurrencyToNumber(serviceCost),
      tendency: Number(tendency),
      totalCost: parseCurrencyToNumber(totalCost.toString()),
      urgency: Number(urgency),
      equipmentAmount: equipmentAmount ? Number(equipmentAmount) : null,
      equipmentDescription: equipmentDescription || null,
      laborAmount: laborAmount ? Number(laborAmount) : null,
      imp: Number(imp || 0), // Adicionar o valor de IMP
      buildingPercentage: Math.min(
        (parseCurrencyToNumber(serviceCost) /
          parseCurrencyToNumber(totalCost)) *
          100,
        100
      ),
      proposal: {
        connect: { id: proposalId },
      },
      serviceScope: {
        connect: { id: serviceScopeId },
      },
    } as any;

    const updatedRepairBudget = id
      ? await prisma.repairBudget.update({ where, data, include })
      : await prisma.repairBudget.create({
          data,
          include,
        });

    return parseObject(updatedRepairBudget) as RepairBudget;
  } catch (error) {
    console.error(error);
    throw error; // Re-throw the error to be handled by the caller
  }
}

export async function loadRepairBudgets(proposalId: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const repairBudget = await prisma.repairBudget.findMany({
      where: {
        proposalId,
        proposal: {
          customer: {
            organizationId,
          },
        },
      },
      include: {
        serviceScope: true,
        laborEquipament: true,
        planningFrequencyItems: true,
        // Incluir as medições de produtividade para exibir a data da última medição
        productivity: {
          include: {
            periodicity: true,
          },
          orderBy: {
            startDate: "desc", // Ordenar pela data mais recente
          },
          take: 1, // Pegar apenas a medição mais recente
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Atualizar o valor de IMP para cada RepairBudget
    for (const budget of repairBudget) {
      // Calcular o valor de IMP
      const imp = await loadIMP(budget.serviceScopeId);

      // Atualizar o valor de IMP no banco de dados
      await prisma.repairBudget.update({
        where: { id: budget.id },
        data: { imp: Number(imp || 0) },
      });

      // Atualizar o valor de IMP no objeto
      // Convert to Decimal or the appropriate type
      budget.imp =
        imp !== null && imp !== undefined
          ? typeof imp === "number"
            ? new Prisma.Decimal(imp)
            : imp
          : new Prisma.Decimal(0);
    }

    // Type-safe console log
    console.log(
      "Repair budgets with productivity data:",
      repairBudget.map((rb: any) => ({
        id: rb.id,
        serviceScope: rb.serviceScope?.name || "Unknown",
        imp: rb.imp,
        productivity: rb.productivity
          ? rb.productivity.map((p: any) => ({
              id: p.id,
              startDate: p.startDate,
              periodicityId: p.periodicityId,
            }))
          : [],
      }))
    );

    return parseObject(repairBudget) as RepairBudget[];
  } catch (error) {
    console.error(error);
    throw error; // Re-throw the error to be handled by the caller
  }
}

export async function removeRepairBudget(id: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const repairBudget = await prisma.repairBudget.findFirst({
      where: {
        id,
        proposal: {
          customer: {
            organizationId,
          },
        },
      },
    });

    if (!repairBudget) {
      throw new Error(
        "Reparo não encontrado ou não pertence a sua organização"
      );
    }

    await prisma.productivity.deleteMany({ where: { repairBudgetId: id } });
    await prisma.laborEquipmentAmount.deleteMany({
      where: { repairBudgetId: id },
    });

    await prisma.repairBudget.delete({ where: { id } });

    return { message: "Orçamento de reparo removido com sucesso!" };
  } catch (error) {
    console.error(error);
    throw error; // Re-throw the error to be handled by the caller
  }
}

export async function removeRepairBudgets(proposalId: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    await prisma.repairBudget.deleteMany({
      where: {
        proposalId,
        proposal: {
          customer: {
            organizationId,
          },
        },
      },
    });

    return { message: "Orçamento de reparos removido com sucesso!" };
  } catch (error) {
    console.error(error);
    throw error; // Re-throw the error to be handled by the caller
  }
}

export async function loadIMP(serviceScopeId: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const repairBudgets = await prisma.repairBudget.findMany({
      where: {
        serviceScopeId,
        serviceScope: {
          organizationId,
        },
      },
    });

    if (repairBudgets.length) {
      const IMP =
        repairBudgets.reduce(
          (acc, repairBudget) => acc + Number(repairBudget.serviceCost),
          0
        ) / repairBudgets.length;

      return IMP;
    }

    return 0;
  } catch (error) {
    console.error(error);
    throw error; // Re-throw the error to be handled by the caller
  }
}

export async function findRepairBudgetById(repairBudgetId: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const repairBudget = await prisma.repairBudget.findFirst({
      where: {
        id: repairBudgetId,
        proposal: {
          customer: {
            organizationId,
          },
        },
      },
      include: {
        laborEquipament: true,
      },
    });

    return parseObject(repairBudget) as RepairBudget;
  } catch (error) {
    console.error(error);
    throw error; // Re-throw the error to be handled by the caller
  }
}
