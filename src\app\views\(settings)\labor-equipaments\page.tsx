"use client";

import { removeLabortype } from "@/src/actions/laborType";
import ContentWrapper from "@/src/components/content-wrapper";
import { constructActionColumn, contructColumn } from "@/src/lib/table/columns";
import { useToast } from "@/src/hooks/use-toast";
import { formatDate } from "@/src/lib/utils";
import { useRef, useState } from "react";
import { LaborEquipamentsSchema } from './schemas/labor-equipament.schema';
import DialogLaborEquipmentDetails from "./components/dialog-labor-equipaments";
import LaborEquipamentsTable, { LaborEquipamentsTableRef } from "./components/labor-equipaments-table";

export default function Page() {
    const { toast } = useToast();
    const [itemLaborEquipament, setItemLaborEquipament] = useState<any>(undefined);
    const [open, setOpen] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const tableRef = useRef<LaborEquipamentsTableRef>(null);



    const deleteLaborEquipament = async (id: string) => {
        try {
            const data = await removeLabortype(id);

            if (data) {
                toast({
                    title: "Sucesso",
                    description: data.message,
                    variant: "default"
                });
                // Recarregar a tabela mantendo a página atual
                tableRef.current?.refresh(currentPage);
            }
        }
        catch (error) {
            console.error(error);
            toast({
                title: "Erro",
                description: "Erro ao excluir item",
                variant: "destructive"
            });
        }
    };

    const handleDialogVisibility = (open: boolean, laborEquipament?: LaborEquipamentsSchema) => {
        setItemLaborEquipament(laborEquipament);
        if (!open) {
            setItemLaborEquipament(undefined);
            // Recarregar a tabela mantendo a página atual após fechar o diálogo
            tableRef.current?.refresh(currentPage);
        }
        setOpen(open);
    };


    const columns = [
        contructColumn("name", "Nome"),
        contructColumn("description", "Descrição"),
        contructColumn("createdAt", "Criado em", (row: any) =>
            formatDate(row.createdAt, "DATE")
        ),

        constructActionColumn([
            {
                action: "edit",
                callback: (labor) => handleDialogVisibility(true, labor as LaborEquipamentsSchema),
            },
            {
                action: "delete",
                dialogTitle: "Deseja realmente excluir o item?",
                dialogDescription: "Ao excluir esse item, todos os orçamentos de reparo e seus serviços vinculadas serão excluídas. Deseja continuar?",
                callback: (data: any) => { void deleteLaborEquipament(data.id); },
            },
        ]),
    ];



    return (
        <ContentWrapper title="Equipamentos e mão de obra">
            <LaborEquipamentsTable
                ref={tableRef}
                columns={columns}
                onAddClick={() => handleDialogVisibility(true)}
                onPageChange={(page) => setCurrentPage(page)}
            />
            <DialogLaborEquipmentDetails
                isOpen={open}
                onOpenChange={setOpen}
                laborEquipament={itemLaborEquipament}
                loadLaborEquipament={async () => await tableRef.current?.refresh(currentPage)}
                currentPage={currentPage}
            />
        </ContentWrapper>
    )
}
