"use client";

import { useEffect } from 'react';

// Este componente ajusta o viewport para dispositivos móveis
export default function ViewportFix() {
  useEffect(() => {
    // Função para ajustar a altura do viewport
    const fixViewport = () => {
      // Primeiro, definimos a altura do viewport
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
      
      // Adiciona padding extra no final da página em dispositivos móveis
      if (window.innerWidth <= 768) {
        document.body.style.paddingBottom = '70px';
      } else {
        document.body.style.paddingBottom = '';
      }
    };

    // Executar no carregamento
    fixViewport();
    
    // Adicionar listener para redimensionamento
    window.addEventListener('resize', fixViewport);
    
    // Limpar listener ao desmontar
    return () => window.removeEventListener('resize', fixViewport);
  }, []);

  return null;
}
