"use server";
import { prisma } from "@/src/lib/prisma";

export async function testPrisma() {
  try {
    // Buscar uma proposta existente
    const proposal = await prisma.proposal.findFirst();
    
    if (proposal) {
      console.log("Proposta encontrada:", proposal);
      
      // Tentar atualizar apenas o campo serviceType
      const updatedProposal = await prisma.proposal.update({
        where: { id: proposal.id },
        data: { serviceType: "TESTE" },
      });
      
      console.log("Proposta atualizada:", updatedProposal);
      return { success: true, proposal: updatedProposal };
    }
    
    return { success: false, message: "Nenhuma proposta encontrada" };
  } catch (error) {
    console.error("Erro ao testar Prisma:", error);
    return { success: false, error };
  }
}
