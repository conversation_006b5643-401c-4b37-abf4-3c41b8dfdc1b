import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";
import { S3Client, ListObjectsV2Command, ListObjectsV2CommandOutput } from "@aws-sdk/client-s3";
import {
  MINIO_BUCKET_NAME,
  MINIO_ENDPOINT,
  MINIO_ROOT_USER,
  MINIO_ROOT_PASSWORD,
  STORAGE_REGION,
} from "@/src/lib/env/variables";

// Função para remover acentos
function normalizeName(str: string) {
  return str
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "") // Remove acentos
    .replace(/\s+/g, "") // Remove espaços
    .toLowerCase();
}

const s3 = new S3Client({
  endpoint: MINIO_ENDPOINT!,
  region: STORAGE_REGION!,
  credentials: {
    accessKeyId: MINIO_ROOT_USER!,
    secretAccessKey: MINIO_ROOT_PASSWORD!,
  },
  forcePathStyle: true,
});

async function listAllObjects(prefix: string) {
  let ContinuationToken: string | undefined = undefined;
  let allObjects: any[] = [];
  do {
    const listCommand = new ListObjectsV2Command({
      Bucket: MINIO_BUCKET_NAME!,
      Prefix: prefix,
      ContinuationToken,
    });
    const listResult = await s3.send(listCommand) as ListObjectsV2CommandOutput;
    allObjects = allObjects.concat(listResult.Contents || []);
    ContinuationToken = listResult.IsTruncated ? listResult.NextContinuationToken : undefined;
  } while (ContinuationToken);
  return allObjects;
}

export async function POST() {
  try {
    // Buscar todos os arquivos do MinIO (com paginação)
    const objects = await listAllObjects("matriz/");

    // Buscar todos os arquivos do banco
    const allFiles = await prisma.file.findMany();
    let totalUpdated = 0;
    let totalNotFound = 0;
    const logs: any[] = [];

    for (const obj of objects) {
      if (!obj.Key) continue;
      if (obj.Key.endsWith("/")) continue;
      const fileName = obj.Key.split("/").pop()!;
      const normFileName = normalizeName(fileName);
      // Procurar no banco por name igual (ignorando case, espaços, acentos)
      const fileDb = allFiles.find(f => normalizeName(f.name) === normFileName);
      if (fileDb) {
        if (fileDb.path !== obj.Key) {
          await prisma.file.update({
            where: { id: fileDb.id },
            data: { path: obj.Key },
          });
          totalUpdated++;
          logs.push({ fileId: fileDb.id, oldPath: fileDb.path, newPath: obj.Key, status: "updated" });
        }
      } else {
        totalNotFound++;
        logs.push({ fileName, status: "not-found-in-db" });
      }
    }
    return NextResponse.json({ updated: totalUpdated, notFound: totalNotFound, logs });
  } catch (error: any) {
    return NextResponse.json({ error: error?.message || String(error) }, { status: 500 });
  }
} 