"use client"
import { CustomInput } from "@/src/components/app-input";
import { Proposal } from "@/src/types/core/proposal";
import { ServicesScope } from "@/src/types/core/services-scope";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, FormProvider, useForm } from "react-hook-form";
import { productivityDataSchema, ProductivityDataSchema } from "../schemas/productivity-data.schema";
import { PeriodicityInterface } from "@/src/types/core/periodicity";
import { useCenterToast } from "@/src/hooks/use-center-toast";
import { Button } from "@/src/components/ui/button";
import { Label } from "@/src/components/ui/label";
import { Input } from "@/src/components/ui/input";
import { saveRepairBudgetHistogram } from "@/src/actions/histogram";
import Loading from "@/src/components/app-loading";
import { useEffect, useState } from "react";

interface RepairBudgetFormProps {
    proposal: Proposal;
    onCancelClick: () => void;
    repairBudget?: any;
    closedAndFetch: () => void
    // repairBudgetProductivity?: any;
    services: ServicesScope[];
    periodSelected: PeriodicityInterface
}

export default function ProductivityData({
    proposal,
    onCancelClick,
    closedAndFetch,
    repairBudget,
    services,
    periodSelected,
    // repairBudgetProductivity
}: RepairBudgetFormProps
) {
    const [loading, setLoading] = useState(false);
    const [repairBudgetProductivity, setRepairBudgetProductivity] = useState<any>();
    // CRITICAL FIX: Adicionar estado para armazenar o label do período
    const [periodLabel, setPeriodLabel] = useState<string>('');

    // Estado para controlar a exibição da mensagem de alerta em tempo real
    const [exceedingWarning, setExceedingWarning] = useState<{
        isExceeding: boolean;
        currentTotal: number;
        newValue: number;
        totalAfterAddition: number;
        maxAllowed: number;
        exceeding: number;
        maxRecommended: number;
    } | null>(null);
    const { success: showSuccess, error: showError } = useCenterToast();

    const defaultValues = async () => {
        const defaultValues: ProductivityDataSchema = {
            proposalId: proposal.id,
            buildingPercentage: Number(repairBudgetProductivity?.buildingPercentage) || repairBudget?.calculatedPercentage || 0,
            predictedPeriodPercentage: Number(repairBudgetProductivity?.predictedPeriodPercentage) || 0,
            realPeriodPercentage: Number(repairBudgetProductivity?.realPeriodPercentage) || 0,
            // Definir a descrição apenas se existir um registro de produtividade com descrição
            description: repairBudgetProductivity && repairBudgetProductivity.description ? repairBudgetProductivity.description : '',
            // Usar o ID do serviço do orçamento selecionado, se disponível
            serviceId: repairBudgetProductivity?.serviceId || repairBudget?.serviceId,
            workersQuantity: repairBudgetProductivity?.workersQuantity || 0,
            toolsQuantity: repairBudgetProductivity?.toolsQuantity || 0,
            toolsDescription: repairBudgetProductivity?.toolsDescription || '',
            // Usar o ID do orçamento selecionado, se disponível
            repairBudgetId: repairBudgetProductivity?.repairBudgetId || repairBudget?.id,
            // Usar o ID do período selecionado
            periodicity: repairBudgetProductivity?.periodicityId || periodSelected?.id,
            // Data da medição (usando o campo startDate)
            // Se for uma medição existente, usar a data salva; caso contrário, usar a data de hoje
            startDate: repairBudgetProductivity?.startDate ? new Date(repairBudgetProductivity.startDate) : new Date(),
        };

        return defaultValues;
    }

    // CRITICAL FIX: Efeito para atualizar o label do período quando periodSelected mudar
    useEffect(() => {
        if (periodSelected) {
            console.log('CRITICAL FIX: periodSelected changed, updating period label');

            // Definir o label do período
            const newPeriodLabel = (() => {
                // Tentar usar o label do período selecionado
                if (periodSelected.label) {
                    console.log('CRITICAL FIX: Using selected period label for initial state:', periodSelected.label);
                    return periodSelected.label;
                }

                // Se não tiver label no período selecionado, tentar criar um label a partir do ID
                if (periodSelected.id && periodSelected.id.includes('-')) {
                    // Extrair mês e ano do ID
                    const [monthStr, yearStr] = periodSelected.id.split('-');
                    const month = parseInt(monthStr, 10); // Mês real (1-12)
                    const displayMonth = month - 1; // Mês ajustado (0-11) para usar no array
                    const year = parseInt(yearStr, 10);

                    // Obter o nome do mês em português
                    const monthNames = [
                        'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
                        'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
                    ];

                    // Criar o label no formato "Abril / 2025"
                    const generatedLabel = `${monthNames[displayMonth]} / ${year}`;
                    console.log('CRITICAL FIX: Generated label from ID for initial state:', generatedLabel);
                    return generatedLabel;
                }

                // Se nada funcionar, retornar um valor padrão
                return 'Período selecionado';
            })();

            // Atualizar o estado com o label do período
            setPeriodLabel(newPeriodLabel);
            console.log('CRITICAL FIX: Set initial period label state to:', newPeriodLabel);
        }
    }, [periodSelected]);

    // Efeito para carregar os dados iniciais quando o componente é montado
    useEffect(() => {
        console.log('CRITICAL FIX: Component mounted or repairBudget/periodSelected changed');
        console.log('CRITICAL FIX: repairBudget:', repairBudget);
        console.log('CRITICAL FIX: periodSelected:', periodSelected);

        // Verificar se temos dados de produtividade
        if (repairBudget?.productivity) {
            console.log('CRITICAL FIX: Productivity data in repairBudget:', repairBudget.productivity);

            // Se for um array, verificar o primeiro item
            if (Array.isArray(repairBudget.productivity) && repairBudget.productivity.length > 0) {
                console.log('CRITICAL FIX: First productivity item:', repairBudget.productivity[0]);
            }
        }

        // Verificar se temos um orçamento e um período selecionado
        if (!repairBudget || !periodSelected) {
            console.log('CRITICAL FIX: Missing repairBudget or periodSelected, skipping initialization');
            return;
        }

        // Verificar se já temos dados de produtividade no orçamento
        if (repairBudget.productivity && repairBudget.productivity.length > 0) {
            console.log('CRITICAL FIX: Searching for productivity in period:', periodSelected.id);

            // Encontrar a produtividade para o período selecionado
            const productivityForPeriod = repairBudget.productivity.find((p: any) => {
                if (!p || !p.periodicity) {
                    console.log('CRITICAL FIX: Invalid productivity item or missing periodicity:', p);
                    return false;
                }

                // Verificar se os IDs são iguais (caso simples)
                if (p.periodicity.id === periodSelected.id) {
                    console.log('CRITICAL FIX: Found productivity by ID match');
                    return true;
                }

                // Verificar se os conteúdos são iguais (formato estruturado)
                if (p.periodicity.content === periodSelected.content) {
                    console.log('CRITICAL FIX: Found productivity by content match');
                    return true;
                }

                // Verificar se o período selecionado está no formato mês-ano (MM-YYYY)
                if (periodSelected.id && periodSelected.id.includes('-') && /^\d{1,2}-\d{4}$/.test(periodSelected.id)) {
                    const [month, year] = periodSelected.id.split('-').map(Number);
                    const expectedContent = `month=${month};year=${year}`;

                    // Verificar se o conteúdo da medição corresponde ao formato esperado
                    if (p.periodicity.content === expectedContent) {
                        console.log('CRITICAL FIX: Found productivity by expected content match');
                        return true;
                    }

                    // Verificar se o conteúdo da medição contém informações de mês e ano que correspondem
                    if (p.periodicity.content &&
                        p.periodicity.content.includes(`month=${month}`) &&
                        p.periodicity.content.includes(`year=${year}`)) {
                        console.log('CRITICAL FIX: Found productivity by month and year in content');
                        return true;
                    }
                }

                return false;
            });

            if (productivityForPeriod) {
                console.log('CRITICAL FIX: Found productivity in repairBudget for selected period:', productivityForPeriod);

                // Atualizar o estado com os dados encontrados
                setRepairBudgetProductivity(productivityForPeriod);

                // Não resetar o formulário aqui, pois o efeito que observa repairBudgetProductivity fará isso
                return;
            }
        }

        // Se não encontramos dados de produtividade no orçamento, limpar o estado e resetar o formulário
        console.log('CRITICAL FIX: No productivity found in repairBudget, resetting form');

        // Limpar o estado de repairBudgetProductivity para garantir que dados antigos não sejam usados
        setRepairBudgetProductivity(undefined);

        // Resetar o formulário com valores padrão
        methods.reset({
            proposalId: proposal.id,
            buildingPercentage: repairBudget?.calculatedPercentage || 0,
            predictedPeriodPercentage: 0,
            realPeriodPercentage: 0,
            description: '',
            serviceId: repairBudget?.serviceId,
            workersQuantity: 0,
            toolsQuantity: 0,
            toolsDescription: '',
            repairBudgetId: repairBudget?.id,
            periodicity: periodSelected?.id,
            startDate: new Date(),
        });

        // Carregar os dados de produtividade para o período selecionado
        fetchRepairBudgetProductivity();
    }, [repairBudget, periodSelected]);

    // Efeito para atualizar o formulário quando repairBudgetProductivity muda
    useEffect(() => {
        if (repairBudgetProductivity) {
            setLoading(true);

            console.log('CRITICAL FIX: Updating form with productivity data');
            console.log('CRITICAL FIX: repairBudgetProductivity:', repairBudgetProductivity);

            // Verificar se temos informações de periodicidade
            if (!repairBudgetProductivity.periodicity) {
                console.log('CRITICAL FIX: No periodicity information in repairBudgetProductivity');

                // CRITICAL FIX: Tentar buscar informações de periodicidade do período selecionado
                if (periodSelected) {
                    console.log('CRITICAL FIX: Using periodSelected as fallback:', periodSelected);

                    // Criar um objeto de periodicidade a partir do período selecionado
                    repairBudgetProductivity.periodicity = {
                        id: periodSelected.id,
                        label: periodSelected.label,
                        content: periodSelected.content,
                        order: periodSelected.order
                    };

                    console.log('CRITICAL FIX: Created periodicity from periodSelected:', repairBudgetProductivity.periodicity);
                } else {
                    setLoading(false);
                    return;
                }
            }

            console.log('CRITICAL FIX: periodicity:', repairBudgetProductivity.periodicity);

            // Verificar se há uma data de medição salva
            const savedDate = repairBudgetProductivity.startDate
                ? new Date(repairBudgetProductivity.startDate)
                : new Date(); // Usar a data de hoje como fallback

            console.log('CRITICAL FIX: Using saved date for existing measurement:', savedDate);

            // Garantir que os valores de evolução sejam números
            const predictedPercentage = Number(repairBudgetProductivity.predictedPeriodPercentage) || 0;
            const realPercentage = Number(repairBudgetProductivity.realPeriodPercentage) || 0;

            console.log('CRITICAL FIX: Setting form values for existing measurement:');
            console.log('- predictedPeriodPercentage:', predictedPercentage);
            console.log('- realPeriodPercentage:', realPercentage);
            console.log('- periodicityId:', repairBudgetProductivity.periodicityId || repairBudgetProductivity.periodicity.id);

            // Verificar se o período da medição corresponde ao período selecionado
            const periodicityId = repairBudgetProductivity.periodicityId || repairBudgetProductivity.periodicity.id;

            // CRITICAL FIX: Definir o label do período
            const periodLabelValue = (() => {
                // Tentar usar o label da produtividade primeiro
                if (repairBudgetProductivity.periodicity?.label) {
                    console.log('CRITICAL FIX: Using productivity periodicity label for state:', repairBudgetProductivity.periodicity.label);
                    return repairBudgetProductivity.periodicity.label;
                }

                // Se não tiver label na produtividade, tentar usar o label do período selecionado
                if (periodSelected?.label) {
                    console.log('CRITICAL FIX: Using selected period label for state:', periodSelected.label);
                    return periodSelected.label;
                }

                // Se não tiver label no período selecionado, tentar criar um label a partir do ID
                if (periodSelected?.id && periodSelected.id.includes('-')) {
                    // Extrair mês e ano do ID
                    const [monthStr, yearStr] = periodSelected.id.split('-');
                    const month = parseInt(monthStr, 10); // Mês real (1-12)
                    const displayMonth = month - 1; // Mês ajustado (0-11) para usar no array
                    const year = parseInt(yearStr, 10);

                    // Obter o nome do mês em português
                    const monthNames = [
                        'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
                        'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
                    ];

                    // Criar o label no formato "Abril / 2025"
                    const generatedLabel = `${monthNames[displayMonth]} / ${year}`;
                    console.log('CRITICAL FIX: Generated label from ID for state:', generatedLabel);
                    return generatedLabel;
                }

                // Se nada funcionar, retornar um valor padrão
                console.log('CRITICAL FIX: Using default label for state');
                return 'Período selecionado';
            })();

            // Atualizar o estado com o label do período
            setPeriodLabel(periodLabelValue);
            console.log('CRITICAL FIX: Set period label state to:', periodLabelValue);

            // CRITICAL FIX: Sempre atualizar o formulário, mesmo que o período não corresponda
            // Isso garante que os dados sejam exibidos corretamente ao editar uma medição
            console.log('CRITICAL FIX: Updating form with measurement data');

            // Criar um objeto com os valores do formulário
            const formValues = {
                id: repairBudgetProductivity.id,
                proposalId: proposal.id,
                periodicity: periodicityId,
                buildingPercentage: Number(repairBudgetProductivity.buildingPercentage) || repairBudget?.calculatedPercentage || 0,
                predictedPeriodPercentage: predictedPercentage,
                realPeriodPercentage: realPercentage,
                // Definir a descrição apenas se existir no registro
                description: repairBudgetProductivity.description ? repairBudgetProductivity.description : '',
                serviceId: repairBudgetProductivity.serviceId || repairBudget?.serviceId,
                workersQuantity: repairBudgetProductivity.workersQuantity || 0,
                toolsQuantity: repairBudgetProductivity.toolsQuantity || 0,
                toolsDescription: repairBudgetProductivity.toolsDescription || '',
                repairBudgetId: repairBudgetProductivity.repairBudgetId || repairBudget?.id,
                startDate: savedDate,
            };

            console.log('CRITICAL FIX: Form values:', formValues);

            // Resetar o formulário com os valores
            methods.reset(formValues);

            // Definir os valores diretamente no formulário para garantir que sejam exibidos
            setTimeout(() => {
                console.log('CRITICAL FIX: Setting form values directly');
                methods.setValue('predictedPeriodPercentage', predictedPercentage);
                methods.setValue('realPeriodPercentage', realPercentage);

                // CRITICAL FIX: Definir explicitamente o valor do campo de período
                methods.setValue('periodicity', periodicityId);

                // Verificar se os valores foram definidos corretamente
                const currentValues = methods.getValues();
                console.log('CRITICAL FIX: Current form values after direct setting:');
                console.log('- predictedPeriodPercentage:', currentValues.predictedPeriodPercentage);
                console.log('- realPeriodPercentage:', currentValues.realPeriodPercentage);
                console.log('- periodicity:', currentValues.periodicity);

                // Forçar a renderização do componente
                methods.trigger();

                // CRITICAL FIX: Forçar uma nova renderização do componente após um delay maior
                setTimeout(() => {
                    console.log('CRITICAL FIX: Forcing re-render after delay');
                    setLoading(false);
                }, 300);
            }, 100);

            // Não definir loading como false aqui, pois será definido no setTimeout acima
        } else {
            console.log('CRITICAL FIX: No repairBudgetProductivity data, form will use default values');
            setLoading(false);
        }
    }, [repairBudgetProductivity, periodSelected]);

    const methods = useForm<ProductivityDataSchema>({
        resolver: zodResolver(productivityDataSchema),
        defaultValues,
    });

    // Função para verificar a soma total de realPeriodPercentage para um serviço
    const checkTotalRealPercentage = async (repairBudgetId: string, currentId?: string, newRealPercentage?: number) => {
        try {
            console.log('Verificando percentual total para:');
            console.log('- repairBudgetId:', repairBudgetId);
            console.log('- proposalId:', proposal.id);
            console.log('- excludeId (currentId):', currentId);
            console.log('- newRealPercentage:', newRealPercentage);

            // Buscar todos os registros de produtividade para este serviço e proposta
            // CORREÇÃO: Usar repairBudgetId como serviceId na API
            const url = `/api/check-total-percentage?serviceId=${repairBudgetId}&proposalId=${proposal.id}${currentId ? `&excludeId=${currentId}` : ''}`;
            console.log('URL da API:', url);

            try {
                const response = await fetch(url);

                if (!response.ok) {
                    console.error('Erro ao verificar percentual total:', response.statusText);
                    throw new Error('Erro ao verificar percentual total');
                }

                const data = await response.json();
                console.log('Resposta da API:', data);

                // Calcular o total incluindo o novo valor
                const totalPercentage = data.totalPercentage + (newRealPercentage || 0);
                console.log('Total calculado (atual + novo):', data.totalPercentage, '+', newRealPercentage, '=', totalPercentage);

                return {
                    success: true,
                    totalPercentage,
                    currentTotal: data.totalPercentage
                };
            } catch (apiError) {
                console.error('Erro na chamada da API:', apiError);

                // Implementar um fallback para calcular o total manualmente
                console.log('Usando fallback para calcular o total manualmente');

                // Verificar se temos acesso aos dados de produtividade no repairBudget
                if (repairBudget && repairBudget.productivity && Array.isArray(repairBudget.productivity)) {
                    console.log('Usando dados de produtividade do repairBudget para o cálculo');
                    console.log('Dados de produtividade disponíveis:', repairBudget.productivity);

                    try {
                        // Filtrar os registros para excluir o atual (se estiver editando)
                        const filteredRecords = repairBudget.productivity.filter((p: any) => {
                            // Verificar se o registro é válido
                            if (!p || typeof p !== 'object') {
                                console.log('Registro inválido encontrado:', p);
                                return false;
                            }

                            // Verificar se o ID é válido
                            if (!p.id) {
                                console.log('Registro sem ID encontrado:', p);
                                return false;
                            }

                            // Excluir o registro atual se estiver editando
                            const shouldInclude = !currentId || p.id !== currentId;
                            if (!shouldInclude) {
                                console.log('Excluindo registro atual do cálculo:', p);
                            }

                            return shouldInclude;
                        });

                        console.log('Registros filtrados para cálculo:', filteredRecords);

                        // Calcular a soma total de realPeriodPercentage
                        const totalFromRecords = filteredRecords.reduce((sum: number, record: any) => {
                            // Garantir que o valor seja um número válido
                            let percentage = 0;
                            try {
                                percentage = Number(record.realPeriodPercentage);
                                if (isNaN(percentage)) {
                                    console.log('Valor NaN encontrado:', record);
                                    percentage = 0;
                                }
                            } catch (e) {
                                console.error('Erro ao converter valor:', e);
                                percentage = 0;
                            }

                            return sum + percentage;
                        }, 0);

                        console.log('Total calculado do repairBudget:', totalFromRecords);

                        // Calcular o total incluindo o novo valor
                        const totalPercentage = totalFromRecords + (newRealPercentage || 0);
                        console.log('Total calculado (fallback + novo):', totalFromRecords, '+', newRealPercentage, '=', totalPercentage);

                        return {
                            success: true,
                            totalPercentage,
                            currentTotal: totalFromRecords
                        };
                    } catch (fallbackError) {
                        console.error('Erro no cálculo do fallback:', fallbackError);
                    }
                }

                // Se não temos dados suficientes, retornar apenas o novo valor
                console.log('Sem dados suficientes para o fallback, usando apenas o novo valor');
                return {
                    success: true,
                    totalPercentage: newRealPercentage || 0,
                    currentTotal: 0
                };
            }
        } catch (error) {
            console.error('Erro ao verificar percentual total:', error);
            return { success: false, totalPercentage: 0, currentTotal: 0, message: 'Erro ao verificar percentual total' };
        }
    };

    const handleSubmit = async () => {
        setLoading(true);
        try {
            methods.trigger();
            const formValues = methods.getValues();

            // Garantir que os valores de evolução sejam números
            formValues.predictedPeriodPercentage = Number(formValues.predictedPeriodPercentage) || 0;
            formValues.realPeriodPercentage = Number(formValues.realPeriodPercentage) || 0;

            // Verificar se a soma do realPeriodPercentage não ultrapassa o buildingPercentage
            const buildingPercentage = Number(formValues.buildingPercentage) || 0;
            const currentId = repairBudgetProductivity?.id;

            console.log('Validando percentuais:');
            console.log('- buildingPercentage (percentual total da obra):', buildingPercentage);
            console.log('- realPeriodPercentage (percentual sendo adicionado):', formValues.realPeriodPercentage);
            console.log('- ID do registro atual (se for atualização):', currentId);

            // Verificar a soma total de realPeriodPercentage para este serviço
            const totalCheck = await checkTotalRealPercentage(
                formValues.serviceId,
                currentId,
                formValues.realPeriodPercentage
            );

            if (totalCheck.success && totalCheck.totalPercentage > buildingPercentage) {
                // A soma ultrapassa o buildingPercentage, exibir diálogo de confirmação
                console.log('Validação: total excede o percentual da obra, exibindo diálogo de confirmação');
                console.log('- Total após adição:', totalCheck.totalPercentage);
                console.log('- Percentual máximo permitido:', buildingPercentage);

                // Calcular quanto está ultrapassando
                const excedente = totalCheck.totalPercentage - buildingPercentage;
                const valorMaximo = buildingPercentage - totalCheck.currentTotal;

                // Atualizar as informações para o tooltip
                setExceedingWarning({
                    isExceeding: true,
                    currentTotal: totalCheck.currentTotal,
                    newValue: formValues.realPeriodPercentage,
                    totalAfterAddition: totalCheck.totalPercentage,
                    maxAllowed: buildingPercentage,
                    exceeding: excedente,
                    maxRecommended: valorMaximo
                });

                console.log('Valor excede o total, mas continuando com o salvamento');
                console.log(`Valor máximo disponível: ${valorMaximo.toFixed(2)}%`);

                // Não interromper o processamento, continuar com o salvamento
                // O tooltip já mostra a informação sobre o valor máximo
            }

            // Se chegou aqui, ou o total não excede o percentual da obra, ou excede mas permitimos continuar
            if (totalCheck.totalPercentage <= buildingPercentage) {
                console.log('Validação passou: total não excede o percentual da obra');
            } else {
                console.log('Validação: total excede o percentual da obra, mas permitindo salvamento');
            }
            console.log('- Total após adição:', totalCheck.totalPercentage);
            console.log('- Percentual máximo permitido:', buildingPercentage);

            const formValid = productivityDataSchema.safeParse(formValues).success;

            formValues.proposalId = proposal.id;

            // Verificar se o ID do período é válido (existe no banco de dados)
            console.log('Form values before submit:', formValues);
            console.log('predictedPeriodPercentage:', formValues.predictedPeriodPercentage);
            console.log('realPeriodPercentage:', formValues.realPeriodPercentage);

            // Verificar se estamos editando uma medição existente
            if (repairBudgetProductivity?.id) {
                console.log('CRITICAL: Editing existing measurement with ID:', repairBudgetProductivity.id);
                console.log('CRITICAL: Using periodicity from existing measurement:', repairBudgetProductivity.periodicityId);

                // Usar o periodicityId da medição existente
                formValues.periodicity = repairBudgetProductivity.periodicityId;
            }
            // Se o ID do período contiver um hífen (formato mês-ano), precisamos criar um PlanningFrequencyItem
            else if (formValues.periodicity && typeof formValues.periodicity === 'string' && formValues.periodicity.includes('-')) {
                try {
                    console.log('CRITICAL: Creating new PlanningFrequencyItem for period:', formValues.periodicity);

                    // Extrair mês e ano do ID
                    const parts = formValues.periodicity.split('-');

                    // Verificar se temos exatamente duas partes (mês e ano)
                    if (parts.length !== 2) {
                        console.error('Invalid periodicity format:', formValues.periodicity);
                        if (isMounted) {
                            showError("Formato de período inválido. Tente novamente.");
                        }
                        return;
                    }

                    // Converter para números (não ajustar o mês aqui, pois estamos armazenando o valor real)
                    const month = parseInt(parts[0], 10); // Mês real (1-12)
                    const year = parseInt(parts[1], 10);

                    console.log(`CRITICAL: Original month-year: ${parts[0]}-${parts[1]}, storing as: month=${month}, year=${year}`);

                    // Verificar se mês e ano são números válidos
                    if (isNaN(month) || isNaN(year)) {
                        console.error('Invalid month or year:', month, year);
                        if (isMounted) {
                            showError("Mês ou ano inválido. Tente novamente.");
                        }
                        return;
                    }

                    // Verificar se o mês está no intervalo válido (1-12)
                    if (month < 1 || month > 12) {
                        console.error('Month out of range:', month);
                        if (isMounted) {
                            showError("Mês fora do intervalo válido (deve ser entre 1 e 12). Tente novamente.");
                        }
                        return;
                    }

                    console.log(`CRITICAL: Month validation passed: ${month} is in valid range (1-12)`);

                    // IMPORTANTE: Armazenar o mês exatamente como está no ID (1-12)
                    // NÃO ajustar o mês aqui, pois queremos armazenar o mês real (1-12)
                    const content = `month=${month};year=${year}`;

                    console.log(`CRITICAL: Creating PlanningFrequencyItem with content: ${content}`);

                    // Obter o nome do mês em português (ajustar para o índice 0-11)
                    const displayMonth = month - 1; // Converter de 1-12 para 0-11 para indexar o array
                    console.log(`CRITICAL: Display month (adjusted for Date): ${displayMonth}`);

                    const monthNames = [
                        'janeiro', 'fevereiro', 'março', 'abril', 'maio', 'junho',
                        'julho', 'agosto', 'setembro', 'outubro', 'novembro', 'dezembro'
                    ];
                    const monthName = monthNames[displayMonth];

                    // Criar o label no formato "Abril / 2025"
                    const label = `${monthName.charAt(0).toUpperCase() + monthName.slice(1)} / ${year}`;

                    console.log(`CRITICAL FIX: Generated label: ${label}`);

                    // Criar um novo PlanningFrequencyItem para o período selecionado
                    const newPlanningItem = {
                        proposalId: proposal.id,
                        content: content, // Usar o formato estruturado
                        label: label, // Usar o label formatado em vez de periodSelected.label
                        repairBudgetId: formValues.repairBudgetId, // Incluir o repairBudgetId
                        order: 0
                    };

                    console.log(`Creating PlanningFrequencyItem with structured content: ${content}`);

                    // Chamar a API para criar o PlanningFrequencyItem
                    const response = await fetch('/api/planning-frequency-item', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(newPlanningItem),
                    });

                    // Verificar se o componente ainda está montado
                    if (!isMounted) {
                        console.log('Component unmounted after API call, skipping state updates and toasts');
                        return;
                    }

                    if (!response.ok) {
                        throw new Error('Falha ao criar item de planejamento');
                    }

                    const newItem = await response.json();
                    console.log('Created new planning item:', newItem);

                    // Usar o ID do novo item como periodicityId
                    formValues.periodicity = newItem.id;
                } catch (planningError) {
                    console.error('Error creating planning item:', planningError);
                    // Verificar se o componente ainda está montado antes de exibir toast de erro
                    if (isMounted) {
                        showError("Erro ao criar item de planejamento. Tente novamente.");
                    }
                    return;
                }
            }

            if (formValid) {
                // Se for uma atualização, usar o ID existente
                if (repairBudgetProductivity?.id) {
                    formValues.id = repairBudgetProductivity.id;
                    console.log('Updating existing measurement with ID:', formValues.id);
                    console.log('Using periodicity:', formValues.periodicity);

                    // Verificar se temos informações sobre o período
                    if (repairBudgetProductivity.periodicity) {
                        console.log('Existing periodicity data:', repairBudgetProductivity.periodicity);
                        console.log('Periodicity content:', repairBudgetProductivity.periodicity.content);

                        // Extrair mês e ano do content, se disponível
                        if (repairBudgetProductivity.periodicity.content &&
                            repairBudgetProductivity.periodicity.content.includes('month=') &&
                            repairBudgetProductivity.periodicity.content.includes('year=')) {

                            const contentParts = repairBudgetProductivity.periodicity.content.split(';');
                            const monthPart = contentParts.find((part: string) => part.startsWith('month='));
                            const yearPart = contentParts.find((part: string) => part.startsWith('year='));

                            if (monthPart && yearPart) {
                                const month = parseInt(monthPart.replace('month=', ''), 10);
                                const year = parseInt(yearPart.replace('year=', ''), 10);
                                console.log(`Extracted from content: month=${month}, year=${year}`);
                            }
                        }
                    }
                }

                // CRITICAL FIX: Adicionar informações de debug
                console.log('CRITICAL FIX: Saving productivity data with values:');
                console.log('- proposalId:', formValues.proposalId);
                console.log('- repairBudgetId:', formValues.repairBudgetId);
                console.log('- periodicity:', formValues.periodicity);
                console.log('- predictedPeriodPercentage:', formValues.predictedPeriodPercentage);
                console.log('- realPeriodPercentage:', formValues.realPeriodPercentage);
                console.log('- isEditing:', !!repairBudgetProductivity?.id);
                console.log('- id:', formValues.id);

                const data = await saveRepairBudgetHistogram(formValues);

                // Verificar se o componente ainda está montado
                if (!isMounted) {
                    console.log('Component unmounted after saving data, skipping state updates and toasts');
                    return;
                }

                if (data) {
                    // CRITICAL FIX: Recarregar os dados após salvar para garantir que tudo esteja atualizado
                    console.log('CRITICAL FIX: Reloading data after save');

                    try {
                        // Construir a URL com os parâmetros
                        const apiUrl = `/api/productivity?proposalId=${proposal.id}&repairBudgetId=${formValues.repairBudgetId}&periodId=${formValues.periodicity}`;
                        console.log('CRITICAL FIX: Reload API URL:', apiUrl);

                        // Fazer a requisição para recarregar os dados
                        const response = await fetch(apiUrl);

                        if (response.ok) {
                            const reloadedData = await response.json();
                            console.log('CRITICAL FIX: Reloaded data after save:', reloadedData);

                            // Verificar se temos dados de produtividade
                            if (reloadedData.productivity && reloadedData.productivity.length > 0) {
                                console.log('CRITICAL FIX: Found productivity data in reload response');

                                // CRITICAL FIX: Atualizar o estado com os dados recarregados
                                if (reloadedData.productivity[0]) {
                                    console.log('CRITICAL FIX: Updating state with reloaded data');
                                    setRepairBudgetProductivity(reloadedData.productivity[0]);
                                }
                            }
                        } else {
                            console.error('CRITICAL FIX: Error reloading data, status:', response.status);
                        }
                    } catch (reloadError) {
                        console.error('CRITICAL FIX: Error reloading data:', reloadError);
                    }

                    // Atualizar os valores diretamente via SQL para garantir que sejam salvos corretamente
                    try {
                        const predictedValue = Number(formValues.predictedPeriodPercentage) || 0;
                        const realValue = Number(formValues.realPeriodPercentage) || 0;

                        console.log('CRITICAL FIX: Calling update-productivity-values API:');
                        console.log('- ID:', data.id);
                        console.log('- predictedPeriodPercentage:', predictedValue);
                        console.log('- realPeriodPercentage:', realValue);

                        const response = await fetch('/api/update-productivity-values', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                id: data.id,
                                predictedPeriodPercentage: predictedValue,
                                realPeriodPercentage: realValue
                            }),
                        });

                        // Verificar se o componente ainda está montado
                        if (!isMounted) {
                            console.log('Component unmounted after API call, skipping state updates and toasts');
                            return;
                        }

                        const result = await response.json();
                        console.log('CRITICAL FIX: Update API response:', result);

                        // Verificar novamente se o componente ainda está montado
                        if (!isMounted) {
                            console.log('Component unmounted after parsing response, skipping state updates and toasts');
                            return;
                        }

                        if (result.success) {
                            showSuccess(repairBudgetProductivity?.id ? "Medição atualizada com sucesso!" : "Medição salva com sucesso!");
                        } else {
                            console.error('API error:', result.error);
                            showError("Erro ao atualizar valores. Tente novamente.");
                        }
                    } catch (apiError) {
                        console.error('Error calling update-productivity-values API:', apiError);
                        // Verificar se o componente ainda está montado antes de exibir toast de erro
                        if (isMounted) {
                            showError("Erro ao atualizar valores. Tente novamente.");
                        }
                    }
                }
            }
        } catch (err) {
            console.error(err);
            // Verificar se o componente ainda está montado antes de exibir toast de erro
            if (isMounted) {
                showError("Erro ao salvar a medição. Tente novamente.");
            }
        } finally {
            // Chamar closedAndFetch e atualizar loading apenas se o componente ainda estiver montado
            if (isMounted) {
                closedAndFetch();
                setLoading(false);
            }
        }
    };

    // Variável para controlar se o componente está montado
    const [isMounted, setIsMounted] = useState(true);

    // Definir isMounted como true quando o componente montar
    useEffect(() => {
        console.log('Component mounted, setting isMounted to true');
        setIsMounted(true);
        return () => {
            // Definir isMounted como false quando o componente desmontar
            console.log('Component unmounted, setting isMounted to false');
            setIsMounted(false);
        };
    }, []);



    const fetchRepairBudgetProductivity = async () => {
        setLoading(true)
        try {
            // Verificar se repairBudget e periodSelected existem
            if (!repairBudget || !periodSelected) {
                console.error('CRITICAL FIX: Missing required parameters for API call');
                // Exibir toast apenas se o componente ainda estiver montado
                if (isMounted) {
                    showError("Erro ao carregar dados de produtividade. Parâmetros inválidos.");
                }
                return;
            }

            console.log('CRITICAL FIX: Fetching productivity data from API for:');
            console.log('- proposalId:', proposal.id);
            console.log('- repairBudgetId:', repairBudget.id);
            console.log('- periodId:', periodSelected.id);

            // Construir a URL com os parâmetros
            const apiUrl = `/api/productivity?proposalId=${proposal.id}&repairBudgetId=${repairBudget.id}&periodId=${periodSelected.id}`;
            console.log('CRITICAL FIX: API URL:', apiUrl);

            // Fazer a requisição
            const response = await fetch(apiUrl);
            console.log('CRITICAL FIX: API response status:', response.status);

            if (!response.ok) {
                throw new Error(`API returned ${response.status}`);
            }

            const data = await response.json();
            console.log('CRITICAL FIX: API response data:', data);

            // Verificar se o componente ainda está montado antes de atualizar o estado
            if (!isMounted) {
                console.log('CRITICAL FIX: Component unmounted, skipping state updates');
                return;
            }

            if (data) {
                console.log('CRITICAL FIX: Productivity data loaded from API');

                // Verificar se temos dados de produtividade
                if (data.productivity && data.productivity.length > 0) {
                    console.log('CRITICAL FIX: Found productivity data in API response:', data.productivity);

                    // Verificar se temos todas as produtividades
                    if (data.allProductivities && data.allProductivities.length > 0) {
                        console.log('CRITICAL FIX: Found all productivities in API response:', data.allProductivities);

                        // Adicionar todas as produtividades ao repairBudget para uso posterior
                        repairBudget.allProductivities = data.allProductivities;

                        // CRITICAL FIX: Usar o total já medido da API
                        if (data.totalMeasured !== undefined) {
                            console.log('CRITICAL FIX: Using totalMeasured from API response:', data.totalMeasured);
                            repairBudget.totalMeasured = data.totalMeasured;
                        } else {
                            // Calcular o total já medido em todos os períodos
                            let totalMedido = 0;
                            data.allProductivities.forEach((p: any) => {
                                const realValue = Number(p.realPeriodPercentage) || 0;
                                console.log(`CRITICAL FIX: Produtividade ${p.id}: ${realValue}%`);
                                totalMedido += realValue;
                            });

                            console.log('CRITICAL FIX: Total já medido calculado manualmente:', totalMedido);
                            repairBudget.totalMeasured = totalMedido;
                        }

                        // CRITICAL FIX: Verificar se temos uma medição para o período atual
                        const hasMeasurementForCurrentPeriod = data.productivity && data.productivity.length > 0;
                        console.log('CRITICAL FIX: Has measurement for current period:', hasMeasurementForCurrentPeriod);

                        // CRITICAL FIX: Se não temos medição para o período atual, mas temos medições em outros períodos,
                        // verificar o valor máximo disponível
                        if (!hasMeasurementForCurrentPeriod && repairBudget.totalMeasured > 0) {
                            console.log('CRITICAL FIX: No measurement for current period, but has measurements in other periods');
                            console.log('CRITICAL FIX: Total measured in other periods:', repairBudget.totalMeasured);

                            // Calcular o valor máximo disponível
                            const buildingPercentage = repairBudget.buildingPercentage || 0;
                            const valorMaximo = buildingPercentage - repairBudget.totalMeasured;
                            console.log('CRITICAL FIX: Building percentage:', buildingPercentage);
                            console.log('CRITICAL FIX: Maximum available value:', valorMaximo);

                            // Atualizar as informações para o tooltip
                            setExceedingWarning({
                                isExceeding: false,
                                currentTotal: repairBudget.totalMeasured,
                                newValue: 0,
                                totalAfterAddition: repairBudget.totalMeasured,
                                maxAllowed: buildingPercentage,
                                exceeding: 0,
                                maxRecommended: valorMaximo
                            });
                        }
                    }

                    // Encontrar a produtividade para o período selecionado (já deve estar marcada com isSelectedPeriod)
                    const productivityForPeriod = data.productivity[0];

                    if (productivityForPeriod) {
                        console.log('CRITICAL FIX: Using productivity from API response:', productivityForPeriod);

                        // Garantir que os valores sejam números
                        productivityForPeriod.predictedPeriodPercentage = Number(productivityForPeriod.predictedPeriodPercentage) || 0;
                        productivityForPeriod.realPeriodPercentage = Number(productivityForPeriod.realPeriodPercentage) || 0;

                        // Atualizar o estado com os dados encontrados
                        setRepairBudgetProductivity(productivityForPeriod);

                        // Definir os valores diretamente no formulário
                        methods.setValue('predictedPeriodPercentage', productivityForPeriod.predictedPeriodPercentage);
                        methods.setValue('realPeriodPercentage', productivityForPeriod.realPeriodPercentage);

                        // Definir a descrição apenas se existir no registro
                        if (productivityForPeriod.description) {
                            methods.setValue('description', productivityForPeriod.description);
                        }

                        // Atualizar o tooltip com as informações corretas
                        checkExceedingValue(productivityForPeriod.realPeriodPercentage);

                        setLoading(false);
                        return;
                    }
                }

                // Se não encontrou produtividade específica, usar os dados do orçamento
                console.log('CRITICAL FIX: No specific productivity found in API response, using repair budget data');

                // CRITICAL FIX: Verificar se temos o total já medido da API
                if (data.totalMeasured !== undefined) {
                    console.log('CRITICAL FIX: Using totalMeasured from API response:', data.totalMeasured);
                    repairBudget.totalMeasured = data.totalMeasured;

                    // CRITICAL FIX: Calcular o valor máximo disponível
                    const buildingPercentage = repairBudget.buildingPercentage || 0;
                    const valorMaximo = buildingPercentage - data.totalMeasured;
                    console.log('CRITICAL FIX: Building percentage:', buildingPercentage);
                    console.log('CRITICAL FIX: Maximum available value:', valorMaximo);

                    // CRITICAL FIX: Atualizar as informações para o tooltip
                    setExceedingWarning({
                        isExceeding: false,
                        currentTotal: data.totalMeasured,
                        newValue: 0,
                        totalAfterAddition: data.totalMeasured,
                        maxAllowed: buildingPercentage,
                        exceeding: 0,
                        maxRecommended: valorMaximo
                    });
                }

                // Verificar se o período foi carregado corretamente
                if (data.planningFrequencyItems && data.planningFrequencyItems.length > 0) {
                    console.log('CRITICAL FIX: Using planning frequency items from API response:', data.planningFrequencyItems);

                    // Tentar encontrar o item para o período selecionado
                    const planningItem = data.planningFrequencyItems.find((item: any) =>
                        item.id === periodSelected.id
                    );

                    if (planningItem) {
                        console.log('CRITICAL FIX: Found planning item for selected period:', planningItem);
                    }
                }

                // Criar um objeto com os dados básicos
                const basicData = {
                    id: undefined, // Será uma nova medição
                    proposalId: proposal.id,
                    repairBudgetId: repairBudget.id,
                    serviceId: repairBudget.serviceId || data.serviceScope?.id,
                    periodicityId: periodSelected.id,
                    buildingPercentage: repairBudget.buildingPercentage || repairBudget.calculatedPercentage || 0,
                    predictedPeriodPercentage: 0,
                    realPeriodPercentage: 0,
                    description: '',
                    periodicity: periodSelected
                };

                console.log('CRITICAL FIX: Using basic data for new measurement:', basicData);

                // Atualizar o estado com os dados básicos
                setRepairBudgetProductivity(basicData);

                // Definir os valores padrão no formulário
                methods.setValue('predictedPeriodPercentage', 0);
                methods.setValue('realPeriodPercentage', 0);
                methods.setValue('description', '');
            } else {
                console.error('API returned null data');

                // Limpar o campo de descrição quando não houver registro de medição
                console.log('No productivity data found, clearing description field');
                methods.setValue('description', '');

                // Limpar a referência ao registro de produtividade
                setRepairBudgetProductivity(null);
            }
        } catch (err) {
            console.error('Error in fetchRepairBudgetProductivity:', err);
            // Exibir toast apenas se o componente ainda estiver montado
            if (isMounted) {
                showError("Erro ao carregar dados de produtividade. Tente novamente.");
            }
        } finally {
            // Atualizar o estado de loading apenas se o componente ainda estiver montado
            if (isMounted) {
                setLoading(false);
            }
        }
    }

    // Função para corrigir o PlanningFrequencyItem
    const fixPlanningFrequencyItem = async (periodicityId: string) => {
        if (!isMounted) return;

        try {
            console.log('Fixing PlanningFrequencyItem:');
            console.log('- proposalId:', proposal.id);
            console.log('- repairBudgetId:', repairBudget?.id);
            console.log('- periodicityId:', periodicityId);

            // Chamar a API para corrigir o PlanningFrequencyItem
            const response = await fetch('/api/fix-planning-frequency-items', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    proposalId: proposal.id,
                    repairBudgetId: repairBudget?.id,
                    periodicityId: periodicityId,
                }),
            });

            if (!response.ok) {
                throw new Error('Falha ao corrigir item de planejamento');
            }

            const result = await response.json();
            console.log('Fix PlanningFrequencyItem result:', result);

            return result.success;
        } catch (error) {
            console.error('Error fixing PlanningFrequencyItem:', error);
            return false;
        }
    };

    useEffect(() => {
        // Verificar se o componente está montado antes de continuar
        if (!isMounted) {
            console.log('Component not mounted, skipping useEffect');
            return;
        }

        // Se não houver dados de produtividade, mas houver um orçamento selecionado,
        // pré-selecionar o serviço do orçamento
        if (repairBudget && repairBudget.serviceId) {
            methods.setValue('serviceId', repairBudget.serviceId);
            methods.setValue('repairBudgetId', repairBudget.id);

            // Se temos um período selecionado, tentar corrigir o PlanningFrequencyItem
            if (periodSelected && periodSelected.id) {
                fixPlanningFrequencyItem(periodSelected.id);
            }

            // Usar o percentual já calculado, se disponível
            console.log('RepairBudget:', repairBudget); // Log para depuração

            if (repairBudget.calculatedPercentage !== undefined) {
                // Usar o percentual já calculado
                console.log('Using pre-calculated percentage:', repairBudget.calculatedPercentage);
                methods.setValue('buildingPercentage', repairBudget.calculatedPercentage);
            } else {
                // Verificar se temos os valores necessários para o cálculo
                if (repairBudget.serviceCost !== undefined && repairBudget.totalBudget !== undefined) {
                    // Garantir que os valores sejam números
                    const serviceCost = Number(repairBudget.serviceCost);
                    const totalBudget = Number(repairBudget.totalBudget);

                    console.log('ServiceCost:', serviceCost, 'TotalBudget:', totalBudget); // Log para depuração

                    if (!isNaN(serviceCost) && !isNaN(totalBudget) && totalBudget > 0) {
                        // Calcular o percentual (serviço / orçamento total * 100)
                        const percentage = (serviceCost / totalBudget) * 100;
                        // Arredondar para 2 casas decimais
                        const roundedPercentage = Math.round(percentage * 100) / 100;

                        console.log('Calculated Percentage:', percentage, 'Rounded:', roundedPercentage); // Log para depuração

                        // Definir o valor do campo buildingPercentage
                        methods.setValue('buildingPercentage', roundedPercentage);
                    } else {
                        console.log('Invalid values for calculation:', { serviceCost, totalBudget }); // Log para depuração
                    }
                } else {
                    console.log('Missing values for calculation:', {
                        serviceCost: repairBudget.serviceCost,
                        totalBudget: repairBudget.totalBudget
                    }); // Log para depuração
                }
            }

            // Se for uma nova medição (não tem repairBudgetProductivity), definir a data de hoje
            if (!repairBudgetProductivity) {
                console.log('Setting today\'s date for new measurement');
                methods.setValue('startDate', new Date());

                // Limpar o campo de descrição para novas medições
                console.log('Clearing description field for new measurement');
                methods.setValue('description', '');
            }
        }

        // Definir o valor do campo de periodicidade com o ID do período selecionado
        if (periodSelected && periodSelected.id) {
            methods.setValue('periodicity', periodSelected.id);
        }

        // Criar uma variável para controlar se o componente ainda está montado
        let isComponentMounted = true;

        // Definir uma função para verificar se o componente está montado antes de chamar fetchRepairBudgetProductivity
        const fetchDataIfMounted = async () => {
            // Verificar se o componente ainda está montado
            if (isComponentMounted && isMounted) {
                await fetchRepairBudgetProductivity();
            }
        };

        // Chamar a função
        fetchDataIfMounted();

        // Cleanup function para definir isComponentMounted como false quando o componente desmontar
        return () => {
            isComponentMounted = false;
        };
    }, [repairBudget, periodSelected, isMounted]);

    // Função para verificar e atualizar as informações do tooltip
    const checkExceedingValue = async (value: number) => {
        if (!repairBudget) {
            return;
        }

        try {
            console.log('CRITICAL FIX: Verificando valor excedente para:', value);
            console.log('CRITICAL FIX: RepairBudget:', repairBudget);

            // Obter o percentual total da obra
            const buildingPercentage = repairBudget.buildingPercentage || 0;
            console.log('CRITICAL FIX: Percentual total da atividade:', buildingPercentage);

            // Obter o ID atual da produtividade (se estiver editando)
            const currentId = repairBudgetProductivity?.id || null;
            console.log('CRITICAL FIX: ID da produtividade atual (se estiver editando):', currentId);

            // CRITICAL FIX: Verificar se temos o total já medido no repairBudget
            if (repairBudget.totalMeasured !== undefined) {
                console.log('CRITICAL FIX: Usando total já medido do repairBudget:', repairBudget.totalMeasured);

                // Calcular o total após adição
                const totalAfterAddition = repairBudget.totalMeasured + value;
                console.log('CRITICAL FIX: Total após adição:', totalAfterAddition);

                // O valor máximo disponível é o valor total da atividade menos a soma dos valores realizados nos outros meses
                const valorMaximo = buildingPercentage - repairBudget.totalMeasured;
                console.log('CRITICAL FIX: Valor máximo disponível:', valorMaximo.toFixed(2) + '%');

                // Atualizar as informações para o tooltip
                setExceedingWarning({
                    isExceeding: totalAfterAddition > buildingPercentage,
                    currentTotal: repairBudget.totalMeasured,
                    newValue: value,
                    totalAfterAddition: totalAfterAddition,
                    maxAllowed: buildingPercentage,
                    exceeding: totalAfterAddition - buildingPercentage,
                    maxRecommended: valorMaximo
                });

                if (totalAfterAddition > buildingPercentage) {
                    console.log(`CRITICAL FIX: ALERTA: Valor excede o total em ${(totalAfterAddition - buildingPercentage).toFixed(2)}%`);
                }

                return;
            }

            // Se não temos o total já medido no repairBudget, verificar a soma total de todos os períodos exceto o atual
            console.log('CRITICAL FIX: Total já medido não encontrado no repairBudget, chamando checkTotalRealPercentage');
            const totalCheck = await checkTotalRealPercentage(
                repairBudget.id,
                currentId,
                value
            );

            if (totalCheck.success) {
                // Exibir informações detalhadas no console para depuração
                console.log('CRITICAL FIX: Resultado da verificação de percentual total:');
                console.log('- Total já medido em outros períodos:', totalCheck.currentTotal);
                console.log('- Valor sendo adicionado agora:', value);
                console.log('- Total após adição:', totalCheck.totalPercentage);
                console.log('- Percentual total da atividade:', buildingPercentage);

                // Verificar se temos dados de produtividade no repairBudget
                if (repairBudget.allProductivities && Array.isArray(repairBudget.allProductivities)) {
                    console.log('CRITICAL FIX: Verificando todas as produtividades disponíveis no repairBudget');
                    console.log('CRITICAL FIX: Total de produtividades:', repairBudget.allProductivities.length);

                    // Filtrar as produtividades para excluir a atual (se estiver editando)
                    const filteredProductivities = repairBudget.allProductivities.filter((p: any) => {
                        return !currentId || p.id !== currentId;
                    });

                    console.log('CRITICAL FIX: Produtividades filtradas (excluindo a atual):', filteredProductivities.length);

                    // Calcular a soma total de realPeriodPercentage
                    let manualTotal = 0;
                    filteredProductivities.forEach((p: any) => {
                        const realValue = Number(p.realPeriodPercentage) || 0;
                        console.log(`CRITICAL FIX: Produtividade ${p.id}: ${realValue}%`);
                        manualTotal += realValue;
                    });

                    console.log('CRITICAL FIX: Total calculado manualmente:', manualTotal);

                    // Se o total calculado manualmente for diferente do retornado pela API, usar o maior
                    if (Math.abs(manualTotal - totalCheck.currentTotal) > 0.01) {
                        console.log('CRITICAL FIX: Diferença entre total manual e API:', Math.abs(manualTotal - totalCheck.currentTotal));
                        console.log('CRITICAL FIX: Usando o maior valor entre manual e API');

                        // Usar o maior valor entre o calculado manualmente e o retornado pela API
                        const currentTotal = Math.max(manualTotal, totalCheck.currentTotal);
                        console.log('CRITICAL FIX: Total final a ser usado:', currentTotal);

                        // Recalcular o total após adição
                        const totalAfterAddition = currentTotal + value;
                        console.log('CRITICAL FIX: Total após adição recalculado:', totalAfterAddition);

                        // O valor máximo disponível é o valor total da atividade menos a soma dos valores realizados nos outros meses
                        const valorMaximo = buildingPercentage - currentTotal;
                        console.log('CRITICAL FIX: Valor máximo disponível recalculado:', valorMaximo.toFixed(2) + '%');

                        // Atualizar as informações para o tooltip com os valores recalculados
                        setExceedingWarning({
                            isExceeding: totalAfterAddition > buildingPercentage,
                            currentTotal: currentTotal,
                            newValue: value,
                            totalAfterAddition: totalAfterAddition,
                            maxAllowed: buildingPercentage,
                            exceeding: totalAfterAddition - buildingPercentage,
                            maxRecommended: valorMaximo
                        });

                        if (totalAfterAddition > buildingPercentage) {
                            console.log(`CRITICAL FIX: ALERTA: Valor excede o total em ${(totalAfterAddition - buildingPercentage).toFixed(2)}%`);
                        }

                        return;
                    }
                }

                // Se não temos dados adicionais ou o total calculado manualmente é similar ao da API,
                // usar os valores retornados pela API

                // O valor máximo disponível é o valor total da atividade menos a soma dos valores realizados nos outros meses
                const valorMaximo = buildingPercentage - totalCheck.currentTotal;
                console.log('CRITICAL FIX: Valor máximo disponível (API):', valorMaximo.toFixed(2) + '%');

                // Atualizar as informações para o tooltip
                setExceedingWarning({
                    isExceeding: totalCheck.totalPercentage > buildingPercentage,
                    currentTotal: totalCheck.currentTotal,
                    newValue: value,
                    totalAfterAddition: totalCheck.totalPercentage,
                    maxAllowed: buildingPercentage,
                    exceeding: totalCheck.totalPercentage - buildingPercentage,
                    maxRecommended: valorMaximo
                });

                if (totalCheck.totalPercentage > buildingPercentage) {
                    console.log(`CRITICAL FIX: ALERTA: Valor excede o total em ${(totalCheck.totalPercentage - buildingPercentage).toFixed(2)}%`);
                }
            }
        } catch (error) {
            console.error("CRITICAL FIX: Erro ao verificar valor:", error);
        }
    };

    const handleCancel = () => {
        // Verificar se o componente ainda está montado antes de atualizar o estado
        if (isMounted) {
            // Resetar o formulário e garantir que o campo de descrição esteja vazio
            methods.reset();
            methods.setValue('description', '');

            // Limpar o alerta
            setExceedingWarning(null);

            // Chamar onCancelClick para fechar o modal
            onCancelClick();
        }
    };

    // Não precisamos mais da função continueWithSaving, pois o salvamento ocorre diretamente

    return (
        <div className="flex flex-col gap-6">
            <FormProvider {...methods}>
                <form className="flex flex-col gap-6">

                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <CustomInput
                            name="serviceId"
                            label="Serviço"
                            placeholder="Serviço"
                            disabled
                            type="select"
                            items={services.map((service) => ({
                                value: service.id,
                                label: service.name,
                            }))}
                        />
                        {/* CRITICAL FIX: Usar o mesmo padrão do CustomInput para o campo de período */}
                        <div>
                            <div className="flex mb-2">
                                <Label
                                    className="font-bold text-gray-700"
                                    htmlFor="periodicity"
                                >
                                    Período
                                </Label>
                            </div>
                            <div className="relative">
                                <div className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm shadow-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 cursor-not-allowed opacity-50">
                                    {/* CRITICAL FIX: Usar o estado periodLabel para exibir o período */}
                                    <div className="flex items-center justify-between w-full whitespace-nowrap [&>span]:line-clamp-1">
                                        <span style={{ pointerEvents: 'none' }}>
                                            {periodLabel || (() => {
                                                // CRITICAL FIX: Garantir que o label do período seja exibido corretamente
                                                console.log('CRITICAL FIX: Rendering period label directly (fallback)');

                                                // Tentar usar o label da produtividade primeiro
                                                if (repairBudgetProductivity?.periodicity?.label) {
                                                    console.log('CRITICAL FIX: Using productivity periodicity label:', repairBudgetProductivity.periodicity.label);
                                                    return repairBudgetProductivity.periodicity.label;
                                                }

                                                // Se não tiver label na produtividade, tentar usar o label do período selecionado
                                                if (periodSelected?.label) {
                                                    console.log('CRITICAL FIX: Using selected period label:', periodSelected.label);
                                                    return periodSelected.label;
                                                }

                                                // Se não tiver label no período selecionado, tentar criar um label a partir do ID
                                                if (periodSelected?.id && periodSelected.id.includes('-')) {
                                                    // Extrair mês e ano do ID
                                                    const [monthStr, yearStr] = periodSelected.id.split('-');
                                                    const month = parseInt(monthStr, 10); // Mês real (1-12)
                                                    const displayMonth = month - 1; // Mês ajustado (0-11) para usar no array
                                                    const year = parseInt(yearStr, 10);

                                                    // Obter o nome do mês em português
                                                    const monthNames = [
                                                        'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
                                                        'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
                                                    ];

                                                    // Criar o label no formato "Abril / 2025"
                                                    const generatedLabel = `${monthNames[displayMonth]} / ${year}`;
                                                    console.log('CRITICAL FIX: Generated label from ID:', generatedLabel);
                                                    return generatedLabel;
                                                }

                                                // Se nada funcionar, retornar um valor padrão
                                                console.log('CRITICAL FIX: Using default label');
                                                return 'Período selecionado';
                                            })()}
                                        </span>
                                        {/* Adicionar o ícone de seta para baixo para parecer com o SelectTrigger */}
                                        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 opacity-50">
                                            <path d="M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.26618 11.9026 7.38064 11.95 7.49999 11.95C7.61933 11.95 7.73379 11.9026 7.81819 11.8182L10.0682 9.56819Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            {/* Campo oculto para manter o valor no formulário */}
                            <input
                                type="hidden"
                                name="periodicity"
                                value={repairBudgetProductivity?.periodicityId || periodSelected?.id || ''}
                            />
                        </div>
                        <CustomInput
                            name="startDate"
                            label="Data da medição"
                            type="date"
                            required
                        />
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        {/* Campo 1: Percentual Total */}
                        <div className="space-y-2">
                            <div className="flex items-center h-6">
                                <Label htmlFor="buildingPercentage" className="font-bold text-gray-700">
                                    Percentual Total relacionados à obra
                                </Label>
                            </div>
                            <div className="relative">
                                <Input
                                    id="buildingPercentage"
                                    name="buildingPercentage"
                                    type="text"
                                    className="hide-arrows h-10"
                                    disabled
                                    value={Number(methods.getValues().buildingPercentage || 0).toFixed(2)}
                                />
                                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                                    %
                                </span>
                            </div>
                        </div>

                        {/* Campo 2: Evolução previsto */}
                        <div className="space-y-2">
                            <div className="flex items-center h-6">
                                <Label htmlFor="predictedPeriodPercentage" className="font-bold text-gray-700">
                                    Evolução previsto para o período
                                </Label>
                            </div>
                            <div className="relative">
                                <Controller
                                    name="predictedPeriodPercentage"
                                    control={methods.control}
                                    defaultValue={repairBudgetProductivity?.predictedPeriodPercentage || 0}
                                    render={({ field }) => (
                                        <Input
                                            id="predictedPeriodPercentage"
                                            type="number"
                                            className="hide-arrows h-10"
                                            required
                                            {...field}
                                            value={field.value || 0}
                                        />
                                    )}
                                />
                                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                                    %
                                </span>
                            </div>
                        </div>

                        {/* Campo 3: Evolução realizado */}
                        <div className="space-y-2">
                            <div className="flex items-center h-6">
                                <Label
                                    className="font-bold text-gray-700"
                                    htmlFor="realPeriodPercentage"
                                >
                                    Evolução realizado no período
                                </Label>

                                {/* Tooltip com informação sobre o valor máximo */}
                                <div className="ml-2 inline-block relative">
                                    <div className="group">
                                        <button
                                            type="button"
                                            className="cursor-help text-blue-500 hover:text-blue-600 bg-blue-50 rounded-full w-5 h-5 flex items-center justify-center"
                                            aria-label="Informações sobre valor máximo"
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <path d="M12 16v-4"></path>
                                                <path d="M12 8h.01"></path>
                                            </svg>
                                        </button>
                                        <div className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 w-64 rounded-md bg-gray-800 text-white text-xs p-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 pointer-events-none z-50 shadow-lg">
                                            {/* Seta do tooltip */}
                                            <div className="absolute bottom-0 left-1/2 -translate-x-1/2 transform translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800"></div>

                                            <div className="font-medium mb-2">Valor máximo disponível:</div>
                                            <div>
                                                {repairBudget && (
                                                    <>
                                                        <span className="text-white font-bold">
                                                            {(() => {
                                                                // Calcular o valor máximo disponível
                                                                const totalAtividade = repairBudget.buildingPercentage || 0;
                                                                const totalJaMedido = exceedingWarning?.currentTotal || 0;

                                                                // Arredondar para 2 casas decimais para evitar problemas de precisão
                                                                const valorMaximo = Math.round((totalAtividade - totalJaMedido) * 100) / 100;

                                                                // Garantir que não seja negativo
                                                                return Math.max(0, valorMaximo).toFixed(2);
                                                            })()}%
                                                        </span>
                                                        <div className="text-gray-300 text-xs mt-1">
                                                            (total da atividade {(repairBudget.buildingPercentage || 0).toFixed(2)}% menos {(exceedingWarning?.currentTotal || 0).toFixed(2)}% já medido em outros períodos)
                                                        </div>
                                                    </>
                                                )}
                                            </div>
                                            <div className="mt-2 text-gray-300">
                                                Você pode inserir um valor maior, mas estará ultrapassando o total da atividade.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="relative">
                                <Controller
                                    name="realPeriodPercentage"
                                    control={methods.control}
                                    defaultValue={repairBudgetProductivity?.realPeriodPercentage || 0}
                                    render={({ field }) => (
                                        <Input
                                            id="realPeriodPercentage"
                                            type="number"
                                            className="hide-arrows h-10"
                                            required
                                            {...field}
                                            value={field.value || 0}
                                            onBlur={(e) => {
                                                field.onBlur();
                                                const value = parseFloat(e.target.value);
                                                if (!isNaN(value)) {
                                                    checkExceedingValue(value);
                                                } else {
                                                    setExceedingWarning(null);
                                                }
                                            }}
                                        />
                                    )}
                                />
                                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                                    %
                                </span>
                            </div>
                        </div>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-1">
                        <CustomInput
                            name="description"
                            label="Breve descrição"
                            type="textarea"
                        />
                    </div>

                </form>
                <div className="flex flex-col sm:flex-row gap-4 justify-end">
                    <Button
                        type="button"
                        variant="outline"
                        className="border-blue-500 text-blue-500 hover:text-blue-400 hover:border-blue-400 w-full sm:w-auto"
                        onClick={handleCancel}
                        disabled={loading}
                    >
                        Cancelar
                    </Button>
                    <Button
                        type="button"
                        className="bg-green-500 hover:bg-green-400 w-full sm:w-auto"
                        onClick={handleSubmit}
                        disabled={loading}
                    >
                        {repairBudgetProductivity && repairBudgetProductivity.id ? "Atualizar" : "Salvar"}
                    </Button>
                </div>
            </FormProvider>
            {loading && <Loading />}
        </div>

    )
}
