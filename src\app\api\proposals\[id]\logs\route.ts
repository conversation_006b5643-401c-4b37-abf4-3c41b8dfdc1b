import { prisma } from "@/src/lib/prisma";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";
import { NextResponse } from "next/server";
import { ProposalSituation } from "@/src/types/core/proposal";

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { organizationId } = await getCurrentOrganization();

    // Verify if the proposal belongs to the organization
    const proposal = await prisma.proposal.findFirst({
      where: {
        id: params.id,
        customer: {
          organizationId,
        },
      },
    });

    if (!proposal) {
      return NextResponse.json(
        { error: "Proposal not found" },
        { status: 404 }
      );
    }

    const logs = await prisma.logProposal.findMany({
      where: {
        proposalId: params.id,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    return NextResponse.json(logs);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { organizationId } = await getCurrentOrganization();
    const { oldStatus, newStatus } = await request.json();

    // Verify if the proposal belongs to the organization
    const proposal = await prisma.proposal.findFirst({
      where: {
        id: params.id,
        customer: {
          organizationId,
        },
      },
    });

    if (!proposal) {
      return NextResponse.json(
        { error: "Proposal not found" },
        { status: 404 }
      );
    }

    // Create a new log entry
    const log = await prisma.logProposal.create({
      data: {
        proposalId: params.id,
        oldStatus: oldStatus as ProposalSituation,
        newStatus: newStatus as ProposalSituation,
      },
    });

    return NextResponse.json(log);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}