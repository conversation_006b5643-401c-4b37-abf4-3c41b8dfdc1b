import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const key = searchParams.get("key");

  // Verifica se a chave foi fornecida
  if (!key) {
    return NextResponse.json({ error: "Key not provided" }, { status: 400 });
  }

  // Buscar a versão atual do documento
  const file = await prisma.fileEditor.findUnique({
    where: { key }, // key já é do tipo string
    select: { version: true },
  });

  return NextResponse.json({
    url: `${process.env.NEXT_PUBLIC_APP_URL}/api/file-editor/${key}?version=${file?.version || 0}`,
  });
}
