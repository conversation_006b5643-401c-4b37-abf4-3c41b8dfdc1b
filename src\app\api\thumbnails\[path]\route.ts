"use server";
import { prisma } from "@/src/lib/prisma";
import { storageProvider } from "@/src/lib/storage";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string } }
) {
  try {
    const fileId = decodeURIComponent(params.path);
    console.log(`Rota de API de miniaturas: Tentando obter miniatura para arquivo com ID: ${fileId}`);

    // Buscar o arquivo no banco de dados primeiro para obter o caminho da miniatura
    const fileRecord = await prisma.file.findUnique({
      where: { id: fileId },
    });

    console.log("[THUMBNAIL-API] fileId recebido:", fileId);
    console.log("[THUMBNAIL-API] fileRecord:", fileRecord);

    if (!fileRecord) {
      console.log(`[THUMBNAIL-API] Registro de arquivo não encontrado no banco de dados: ${fileId}`);
      return NextResponse.json({ error: "File record not found." }, { status: 404 });
    }

    // Verificar se o arquivo tem uma miniatura
    if (!fileRecord.thumbnailPath) {
      console.log(`[THUMBNAIL-API] Arquivo não possui miniatura: ${fileId}`);
      // Se não tiver miniatura, redirecionar para a API de arquivos normal
      console.log(`[THUMBNAIL-API] Redirecionando para /api/files/${fileId}`);
      return NextResponse.redirect(new URL(`/api/files/${fileId}`, request.url));
    }

    console.log(`[THUMBNAIL-API] Miniatura encontrada no banco de dados: ${fileRecord.thumbnailPath}`);

    // Buscar a miniatura no storage
    const thumbnail = await storageProvider.get(fileRecord.thumbnailPath);
    console.log(`[THUMBNAIL-API] Resultado do storageProvider.get para ${fileRecord.thumbnailPath}:`, thumbnail);
    if (thumbnail && thumbnail.stream) {
      const contentType = thumbnail.contentType || 'image/jpeg';
      console.log(`[THUMBNAIL-API] Tipo do arquivo retornado: ${contentType}`);
      console.log(`[THUMBNAIL-API] Stream retornado existe:`, !!thumbnail.stream);
      return new Response(thumbnail.stream, {
        headers: {
          "Content-Type": contentType,
          "Content-Disposition": "inline",
          "Cache-Control": "public, max-age=31536000", // Cache por 1 ano
        },
      });
    } else {
      console.log(`[THUMBNAIL-API] Miniatura não encontrada no storage: ${fileRecord.thumbnailPath}`);
      // Se a miniatura não for encontrada, redirecionar para a API de arquivos normal
      console.log(`[THUMBNAIL-API] Redirecionando para /api/files/${fileId}`);
      return NextResponse.redirect(new URL(`/api/files/${fileId}`, request.url));
    }
  } catch (error) {
    console.error("Error fetching thumbnail:", error);

    // Fornecer mensagem de erro mais detalhada
    let errorMessage = "Failed to fetch the thumbnail.";
    if (error instanceof Error) {
      errorMessage = `Error: ${error.message}`;
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
