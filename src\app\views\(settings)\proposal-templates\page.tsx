"use client";
import ContentWrapper from "@/src/components/content-wrapper";
import { useSearchParams } from "next/navigation";
import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { Shield } from "lucide-react";

import {
  removeProposalTemplate,
  saveProposalTemplate,
} from "@/src/actions/proposal-templates";
import {
  removeReportTemplate,
  saveReportTemplate,
} from "@/src/actions/report-template";
import FileUpload from "@/src/components/file-upload";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog";
import { useToast } from "@/src/hooks/use-toast";
import { constructActionColumn, contructColumn } from "@/src/lib/table/columns";
import { ProposalTemplateInterface } from "@/src/types/core/proposal-template";
import { ReportTemplateInterface } from "@/src/types/core/report-template";
import { reportTypes } from "@/src/constants";

import { useRouter } from "next/navigation";
import { useRef } from "react";
import { FileText } from "lucide-react";
import ProposalTemplatesTable, { ProposalTemplatesTableRef } from "./components/proposal-templates-table";

export default function ProposalTemplates() {
  const { toast } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();
  const documentType = searchParams.get('type') || undefined;
  const [openUploadDialog, setOpenUploadDialog] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<ProposalTemplateInterface | ReportTemplateInterface | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const tableRef = useRef<ProposalTemplatesTableRef>(null);
  const { data: session } = useSession();
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  useEffect(() => {
    async function checkPermission() {
      if (!session?.user?.id) {
        setHasPermission(false);
        return;
      }
      // Montar a rota completa com query string
      const route = `/views/proposal-templates?type=${documentType}`;
      const res = await fetch(`/api/users/${session.user.id}/permissions`);
      if (!res.ok) {
        setHasPermission(false);
        return;
      }
      const data = await res.json();
      // Procurar permissão exata para a rota + query string
      const found = data.permissions.find((p: any) => p.route === route);
      if (found) {
        setHasPermission(found.enabled);
      } else {
        // Se não houver permissão específica, permitir por padrão
        setHasPermission(true);
      }
    }
    checkPermission();
  }, [session, documentType]);

  if (hasPermission === false) {
    return (
      <ContentWrapper>
        <div className="flex items-center justify-center h-64">
          <div className="w-full max-w-md">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <Shield className="h-6 w-6 text-red-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 text-center">Acesso Restrito</h2>
            <p className="text-center text-gray-600">
              Você não tem permissão para acessar esta página.
            </p>
          </div>
        </div>
      </ContentWrapper>
    );
  }

  if (hasPermission === null) {
    // Carregando...
    return (
      <ContentWrapper>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Verificando permissões...</p>
        </div>
      </ContentWrapper>
    );
  }

  // Definir o tipo de documento com base no parâmetro da URL
  const getDocumentTypeInfo = () => {
    switch (documentType) {
      case "PROPOSAL":
        return { label: "Proposta", value: "PROPOSAL", isReport: false };
      case "CONTRACT":
        return { label: "Contrato", value: "CONTRACT", isReport: false };
      case "INSPECTION":
        return { label: "Inspeção e Consultoria", value: "INSPECTION", isReport: true, reportType: "REPORT" };
      case "SUPERVISION":
        return { label: "Fiscalização e Gerenciamento", value: "SUPERVISION", isReport: false };
      case "PROJECT":
        return { label: "Projeto", value: "PROJECT", isReport: true, reportType: "PROJECT" };
      case "CONSULTANCY":
        return { label: "Consultoria", value: "CONSULTANCY", isReport: true, reportType: "CONSULTANCY" };
      default:
        return { label: "Proposta", value: "PROPOSAL", isReport: false };
    }
  };

  const documentTypeInfo = getDocumentTypeInfo();

  const deleteTemplate = async (id: string) => {
    try {
      // Determinar se estamos lidando com um template de relatório ou de proposta
      let data;
      if (documentTypeInfo.isReport) {
        data = await removeReportTemplate(id);
      } else {
        data = await removeProposalTemplate(id);
      }

      if (data) {
        if (data.error) {
          // Se houver erro, mostrar mensagem de erro
          toast({
            title: "Não foi possível excluir",
            description: data.message,
            variant: "destructive"
          });
        } else {
          // Se for sucesso, mostrar mensagem de sucesso
          toast({
            title: "Sucesso",
            description: data.message,
            variant: "default"
          });
          // Recarregar a tabela mantendo a página atual
          tableRef.current?.refresh(currentPage);
        }
      }
    } catch (error) {
      console.error(error);
      toast({
        title: "Erro",
        description: "Erro ao excluir template",
        variant: "destructive"
      });
    }
  };

  const columns = [
    contructColumn("title", "Titulo"),
    contructColumn("description", "Descrição"),
    ...(documentTypeInfo.isReport ? [
      contructColumn("type", "Tipo", (row: any) => {
        const typeKey = row.type;
        const typeLabel = reportTypes.find((r) => r.value === typeKey);
        return typeLabel?.label || "";
      })
    ] : []),
    contructColumn("createdAt", "Criado em", (row: any) =>
      new Date(row.createdAt).toLocaleDateString("pt-BR")
    ),
    constructActionColumn<ProposalTemplateInterface | ReportTemplateInterface>([
      {
        action: "view",
        customIcon: FileText,
        customIconClass: "text-amber-500",
        callback: ({ fileEditorId }) => {
          // Armazenar a rota de origem completa (incluindo parâmetros de consulta) na sessionStorage antes de redirecionar
          sessionStorage.setItem('documentEditorReferrer', window.location.pathname + window.location.search);
          return router.push(`/document-editor/${fileEditorId}`);
        },
      },
      {
        action: "edit",
        callback: (template) => {
          // Definir o template que será editado
          if (documentTypeInfo.isReport) {
            // Para templates de relatório, manter o tipo original
            setEditingTemplate(template);
          } else {
            // Para templates de proposta, sobrescrever o tipo com o tipo da tela atual
            const templateWithCorrectType = {
              ...template,
              type: documentTypeInfo.value // Usar o tipo da tela atual
            };
            setEditingTemplate(templateWithCorrectType);
          }
          setOpenUploadDialog(true);
        },
      },
      {
        action: "delete",
        dialogTitle: "Deseja realmente excluir o template?",
        dialogDescription: "Esta operação não poderá ser desfeita",
        callback: ({ id }) => deleteTemplate(id!),
      },
    ]),
  ];



  function submitFormData(formData: FormData, id?: string) {
    if (id) {
      formData.append("id", id);
    }

    if (documentTypeInfo.isReport) {
      // Para templates de relatório
      formData.append("type", documentTypeInfo.reportType || "REPORT");
      return saveReportTemplate(formData);
    } else {
      // Para templates de proposta
      formData.append("type", documentTypeInfo.value);
      return saveProposalTemplate(formData);
    }
  }

  // Função para obter o título com base no tipo de documento
  function getTitleByType(type?: string) {
    switch (type) {
      case "PROPOSAL":
        return "Templates de Proposta";
      case "CONTRACT":
        return "Templates de Contrato";
      case "INSPECTION":
        return "Templates de Inspeção";
      case "SUPERVISION":
        return "Templates de Fiscalização e Gerenciamento";
      case "PROJECT":
        return "Templates de Projeto";
      case "CONSULTANCY":
        return "Templates de Consultoria";
      default:
        return "Templates do Sistema";
    }
  }

  return (
    <>
      <ContentWrapper title={getTitleByType(documentType)}>
        <ProposalTemplatesTable
          ref={tableRef}
          columns={columns}
          documentType={documentTypeInfo.value} // Sempre usar o tipo de documento da tela atual
          isReport={documentTypeInfo.isReport} // Indicar se estamos lidando com templates de relatório
          onAddClick={() => {
            setOpenUploadDialog(true);
          }}
          onPageChange={(page) => setCurrentPage(page)}
        />
      </ContentWrapper>
      <Dialog
        open={openUploadDialog}
        onOpenChange={(open) => {
          setOpenUploadDialog(open);
          if (!open) {
            setEditingTemplate(null);
            // Recarregar a tabela mantendo a página atual
            tableRef.current?.refresh(currentPage);
          }
        }}
      >
        <DialogContent className="rounded-lg">
          <DialogHeader>
            <DialogTitle className="font-bold text-xl">
              Template de {documentTypeInfo.label.toLowerCase()}
            </DialogTitle>
            <DialogDescription>Enviar template de {documentTypeInfo.label.toLowerCase()}</DialogDescription>

            <FileUpload
              submitFormData={(data) => submitFormData(data, editingTemplate?.id)}

              fileTypes={documentTypeInfo.isReport && documentTypeInfo.value !== "CONSULTANCY"
                ? (documentTypeInfo.value === "PROJECT"
                  ? [{ label: "Projeto", value: "PROJECT" }]
                  : [{ label: "Laudo", value: "REPORT" }])
                : [documentTypeInfo]}
              defaultFileType={documentTypeInfo.isReport
                ? (documentTypeInfo.value === "PROJECT" ? "PROJECT" : "REPORT")
                : documentTypeInfo.value}
              onUploadSuccess={() => {
                setOpenUploadDialog(false);
                setEditingTemplate(null);
                tableRef.current?.refresh(currentPage);
              }}
              editingTemplate={editingTemplate}
            />
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </>
  );
}
