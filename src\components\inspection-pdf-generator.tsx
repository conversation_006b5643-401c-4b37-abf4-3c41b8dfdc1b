"use client";

import { InspectionParameter } from "@/src/types/core/inspection-paramenters";
import { formatDate } from "@/src/lib/utils";
import { prisma } from "@/src/lib/prisma";

// Estender o tipo jsPDF para incluir a propriedade lastAutoTable
declare module 'jspdf' {
  interface jsPDF {
    lastAutoTable: {
      finalY: number;
    };
  }
}

interface InspectionPdfGeneratorProps {
  inspection: InspectionParameter;
  proposal: any;
  customer: any;
}

export async function generateInspectionPdf({
  inspection,
  proposal,
  customer,
}: InspectionPdfGeneratorProps) {
  try {
    // Função auxiliar para carregar uma imagem de forma síncrona
    const loadImageSync = (url: string): Promise<HTMLImageElement> => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.crossOrigin = 'Anonymous';
        img.onload = () => resolve(img);
        img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
        img.src = url;
      });
    };
    // Importar as bibliotecas dinamicamente
    const jspdfModule = await import('jspdf');
    const jsPDF = jspdfModule.jsPDF;
    // Removendo importação não utilizada de html2canvas
    const autoTableModule = await import('jspdf-autotable');
    const autoTable = autoTableModule.default;

    // Criar um novo documento PDF
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // Configurações de margens
    const margin = 15;
    const contentWidth = pageWidth - 2 * margin;

    // Adicionar propriedade lastAutoTable ao objeto pdf para compatibilidade
    pdf.lastAutoTable = { finalY: margin };

    // Adicionar cabeçalho
    pdf.setFillColor(255, 255, 255);
    pdf.rect(margin, margin, contentWidth, 30, 'F');

    // Título do relatório
    const inspectionDate = inspection.inspectionDate
      ? formatDate(inspection.inspectionDate, "DATE")
      : formatDate(new Date(), "DATE");

    pdf.setFontSize(14);
    pdf.setTextColor(0, 0, 0);
    pdf.setFont("helvetica", "bold");
    pdf.text(`Relatório ${inspectionDate} até ${inspectionDate} nº ${inspection.numberInspection || ''}`, margin, margin + 10);

    // Função para adicionar placeholder do logo
    const addLogoPlaceholder = () => {
      pdf.setFillColor(240, 240, 240);
      pdf.rect(pageWidth - 60, margin, 40, 20, 'F');
      pdf.setFontSize(8);
      pdf.setFont("helvetica", "bold");
      pdf.text("Logo", pageWidth - 40, margin + 10);
    };

    // Adicionar logo - usando uma imagem diretamente do jsPDF
    try {
      // Criar uma imagem para carregar o logo
      const img = new Image();
      img.crossOrigin = 'Anonymous'; // Importante para evitar problemas de CORS

      // URL da imagem do logo (caminho absoluto para garantir que seja encontrado)
      img.src = window.location.origin + '/logos/img8.jpg';

      // Quando a imagem for carregada, adicionar ao PDF
      img.onload = function () {
        try {
          pdf.addImage(img, 'JPEG', pageWidth - 60, margin, 40, 20);
        } catch (err) {
          console.error("Erro ao adicionar imagem ao PDF:", err);
          addLogoPlaceholder();
        }
      };

      // Se houver erro no carregamento da imagem
      img.onerror = function () {
        console.error("Erro ao carregar a imagem do logo");
        addLogoPlaceholder();
      };

      // Definir um timeout para garantir que o placeholder seja mostrado se a imagem demorar muito
      setTimeout(() => {
        if (!img.complete) {
          addLogoPlaceholder();
        }
      }, 1000);
    } catch (error) {
      console.error("Erro ao carregar o logo:", error);
      addLogoPlaceholder();
    }

    // Informações do relatório
    let yPos = margin + 40;

    // Tabela de informações do relatório
    autoTable(pdf, {
      startY: yPos,
      head: [['', '']],
      body: [
        ['Relatório nº', `${inspection.numberInspection || ''}`],
        ['Data do relatório', `${inspectionDate}`],
        ['Dia da semana', new Date(inspection.inspectionDate).toLocaleDateString('pt-BR', { weekday: 'long' })]
      ],
      theme: 'grid',
      headStyles: { fillColor: [220, 220, 220], textColor: [0, 0, 0], fontStyle: 'bold' },
      styles: { fontSize: 10, cellPadding: 5 },
      columnStyles: { 0: { fontStyle: 'bold', cellWidth: 40 } },
      margin: { left: pageWidth - margin - 80 }
    });

    // Título do relatório periódico
    yPos = pdf.lastAutoTable.finalY + 10;
    pdf.setFontSize(12);
    pdf.setFont("helvetica", "bold");
    pdf.text("Relatório Periódico de Obra (RPO)", pageWidth / 2, yPos, { align: "center" });

    // Tabela de informações da obra
    yPos += 10;
    autoTable(pdf, {
      startY: yPos,
      head: [['', '']],
      body: [
        ['Obra', proposal?.name || ''],
        ['Local', `${proposal?.address || ''}, ${proposal?.city || ''}, ${proposal?.state || ''}`],
        ['Contratante', customer?.name || '']
      ],
      theme: 'grid',
      headStyles: { fillColor: [220, 220, 220], textColor: [0, 0, 0], fontStyle: 'bold' },
      styles: { fontSize: 10, cellPadding: 5 },
      columnStyles: { 0: { fontStyle: 'bold', cellWidth: 30 } }
    });

    // Tabela de prazos
    yPos = pdf.lastAutoTable.finalY + 5;
    autoTable(pdf, {
      startY: yPos,
      head: [['', '']],
      body: [
        ['Contrato', ''],
        ['Prazo contratual', proposal?.startDate && proposal?.endDate ?
          `${Math.ceil((new Date(proposal.endDate).getTime() - new Date(proposal.startDate).getTime()) / (1000 * 60 * 60 * 24))} dias` : ''],
        ['Prazo decorrido', proposal?.startDate ?
          `${Math.ceil((new Date().getTime() - new Date(proposal.startDate).getTime()) / (1000 * 60 * 60 * 24))} dias` : ''],
        ['Prazo a vencer', proposal?.endDate ?
          `${Math.ceil((new Date(proposal.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} dias` : '']
      ],
      theme: 'grid',
      headStyles: { fillColor: [220, 220, 220], textColor: [0, 0, 0], fontStyle: 'bold' },
      styles: { fontSize: 10, cellPadding: 5 },
      columnStyles: { 0: { fontStyle: 'bold', cellWidth: 30 } },
      margin: { left: pageWidth - margin - 80 }
    });

    // Tabela de condição climática
    yPos = pdf.lastAutoTable.finalY + 10;

    // Obter dados de pluviosidade
    let pluviosityValue = "0";
    try {
      if (proposal?.city && inspection.inspectionDate) {
        // Buscar dados de pluviosidade no banco de dados
        const dateKey = new Date(inspection.inspectionDate).toISOString().split("T")[0];
        const city = proposal.city.toLowerCase().replace(/\s+/g, '-');

        const pluviosity = await prisma.pluviosity.findFirst({
          where: {
            dateKey,
            city,
          },
          select: {
            value: true,
          },
        });

        pluviosityValue = pluviosity?.value?.toString() ?? "0";
      }
    } catch (error) {
      console.error("Erro ao obter dados de pluviosidade:", error);
    }

    // Determinar condição climática
    const getWeatherCondition = (value: string): string => {
      const numValue = parseFloat(value);

      if (numValue === 0) return "Dia claro, sem precipitação";
      if (numValue <= 0.2) return "Garoa leve";
      if (numValue <= 2.5) return "Chuva fraca";
      if (numValue <= 7.6) return "Chuva moderada";
      if (numValue <= 50) return "Chuva forte";
      return "Chuva muito forte";
    };

    // Determinar se é praticável
    const isPracticable = parseFloat(pluviosityValue) <= 2.5 ? "Praticável" : "Não Praticável";

    autoTable(pdf, {
      startY: yPos,
      head: [['Precipitação (mm)', 'Condição Climática', 'Situação']],
      body: [
        [pluviosityValue, getWeatherCondition(pluviosityValue), isPracticable]
      ],
      theme: 'grid',
      headStyles: { fillColor: [220, 220, 220], textColor: [0, 0, 0], fontStyle: 'bold' },
      styles: { fontSize: 10, cellPadding: 5 }
    });

    // Tabela de mão de obra
    yPos = pdf.lastAutoTable.finalY + 10;

    // Preparar dados de mão de obra
    const laborData = inspection.laborEquipament?.filter(item =>
      item.labor.type === 'LABOR'
    ) || [];

    // Removendo array não utilizado
    let totalLabor = 0;

    // Categorias de mão de obra
    const laborCategories = ['Ajudante', 'Carpinteiro', 'Eletricista', 'Encarregado', 'Pedreiro', 'Pintores', 'Servente'];
    const laborCounts = {};

    // Inicializar contagens
    laborCategories.forEach(category => {
      laborCounts[category] = 0;
    });

    // Contar mão de obra por categoria
    laborData.forEach(item => {
      const category = item.labor.name;
      if (laborCategories.includes(category)) {
        laborCounts[category] = (laborCounts[category] || 0) + (item.amount || 0);
        totalLabor += (item.amount || 0);
      }
    });

    // Criar linha para a tabela
    const laborRow = laborCategories.map(category => laborCounts[category] || 0);

    pdf.setFontSize(12);
    pdf.setFont("helvetica", "bold");
    pdf.text(`Mão de obra (${totalLabor})`, margin, yPos);

    yPos += 5;
    autoTable(pdf, {
      startY: yPos,
      head: [laborCategories],
      body: [laborRow],
      theme: 'grid',
      headStyles: { fillColor: [220, 220, 220], textColor: [0, 0, 0], fontStyle: 'bold' },
      styles: { fontSize: 10, cellPadding: 5, halign: 'center' }
    });

    // Adicionar "Mão de Obra Direta" no final da tabela
    pdf.setFontSize(10);
    pdf.setFont("helvetica", "bold");
    pdf.text(`Mão de Obra Direta (${totalLabor})`, pageWidth - margin - 50, pdf.lastAutoTable.finalY + 5);

    // Tabela de equipamentos
    yPos = pdf.lastAutoTable.finalY + 15;

    // Preparar dados de equipamentos
    const equipmentData = inspection.laborEquipament?.filter(item =>
      item.labor.type === 'EQUIPAMENT'
    ) || [];

    const equipmentCategories = [
      'Carro de mão reforçado',
      'Container para retirada de entulho',
      'Maquita',
      'Picareta',
      'Pá quadrada com cabo',
      'Pá se bico com cabo'
    ];
    const equipmentCounts = {};

    // Inicializar contagens
    equipmentCategories.forEach(category => {
      equipmentCounts[category] = 0;
    });

    // Contar equipamentos por categoria
    equipmentData.forEach(item => {
      const category = item.labor.name;
      if (equipmentCategories.includes(category)) {
        equipmentCounts[category] = (equipmentCounts[category] || 0) + (item.amount || 0);
      }
    });

    // Criar linha para a tabela
    const equipmentRow = equipmentCategories.map(category => equipmentCounts[category] || 0);

    pdf.setFontSize(12);
    pdf.setFont("helvetica", "bold");
    pdf.text(`Equipamentos (${equipmentData.length})`, margin, yPos);

    yPos += 5;
    autoTable(pdf, {
      startY: yPos,
      head: [equipmentCategories],
      body: [equipmentRow],
      theme: 'grid',
      headStyles: { fillColor: [220, 220, 220], textColor: [0, 0, 0], fontStyle: 'bold' },
      styles: { fontSize: 9, cellPadding: 5, halign: 'center' }
    });

    // Tabela de atividades
    yPos = pdf.lastAutoTable.finalY + 15;
    pdf.setFontSize(12);
    pdf.setFont("helvetica", "bold");
    pdf.text("Atividades (1)", margin, yPos);

    yPos += 5;
    autoTable(pdf, {
      startY: yPos,
      head: [['', '']],
      body: [
        [inspection.technicalData, 'Em Andamento']
      ],
      theme: 'grid',
      headStyles: { fillColor: [220, 220, 220], textColor: [0, 0, 0], fontStyle: 'bold' },
      styles: { fontSize: 10, cellPadding: 5 },
      columnStyles: { 0: { cellWidth: contentWidth - 40 }, 1: { cellWidth: 40 } }
    });

    // Tabela de comentários
    yPos = pdf.lastAutoTable.finalY + 15;
    pdf.setFontSize(12);
    pdf.setFont("helvetica", "bold");
    pdf.text("Comentários (1)", margin, yPos);

    yPos += 5;
    autoTable(pdf, {
      startY: yPos,
      head: [['', '']],
      body: [
        [`${customer?.name || ''}\n${formatDate(inspection.inspectionDate, "DATETIME")}\n${inspection.observation}`, '']
      ],
      theme: 'grid',
      headStyles: { fillColor: [220, 220, 220], textColor: [0, 0, 0], fontStyle: 'bold' },
      styles: { fontSize: 10, cellPadding: 5 },
      columnStyles: { 0: { cellWidth: contentWidth } }
    });

    // Adicionar fotos se disponíveis
    if (inspection.photos && inspection.photos.length > 0) {
      // Adicionar nova página para fotos
      pdf.addPage();

      pdf.setFontSize(14);
      pdf.setFont("helvetica", "bold");
      pdf.text(`Relatório ${inspectionDate} até ${inspectionDate} nº ${inspection.numberInspection || ''}`, margin, margin + 10);

      yPos = margin + 20;
      pdf.setFontSize(12);
      pdf.text(`Fotos (${inspection.photos.length})`, margin, yPos);

      yPos += 10;

      // Adicionar fotos em grade 2x2
      const photoWidth = contentWidth / 2 - 5;
      const photoHeight = photoWidth * 0.75; // Proporção 4:3

      // Processar as fotos em lotes de 4
      for (let i = 0; i < inspection.photos.length; i += 4) {
        // Verificar se precisa adicionar nova página
        if (yPos + photoHeight * 2 + 20 > pageHeight) {
          pdf.addPage();
          yPos = margin;
        }

        // Processar até 4 fotos por vez (2 linhas de 2 fotos)
        const photosInBatch = inspection.photos.slice(i, i + 4);

        // Processar cada foto no lote
        for (let j = 0; j < photosInBatch.length; j++) {
          const photo = photosInBatch[j];
          const col = j % 2;
          const row = Math.floor(j / 2);

          const xPos = margin + col * (photoWidth + 10);
          const yPhotoPos = yPos + row * (photoHeight + 10);

          // Função para adicionar placeholder em caso de erro
          const addPlaceholder = () => {
            pdf.setFillColor(200, 200, 200);
            pdf.rect(xPos, yPhotoPos, photoWidth, photoHeight, 'F');

            // Adicionar descrição da foto
            if (photo.description) {
              pdf.setFontSize(8);
              pdf.setFont("helvetica", "normal");
              pdf.text(photo.description, xPos, yPhotoPos + photoHeight + 5, { maxWidth: photoWidth });
            }
          };

          try {
            // Tentar carregar a imagem real
            if (photo.file && photo.fileId) {
              try {
                // Construir a URL da imagem
                const imageUrl = window.location.origin + `/api/files/${encodeURIComponent(photo.fileId)}`;

                // Carregar a imagem de forma síncrona
                const img = await loadImageSync(imageUrl);

                // Adicionar a imagem ao PDF
                pdf.addImage(img, 'JPEG', xPos, yPhotoPos, photoWidth, photoHeight);

                // Adicionar descrição da foto
                if (photo.description) {
                  pdf.setFontSize(8);
                  pdf.setFont("helvetica", "normal");
                  pdf.text(photo.description, xPos, yPhotoPos + photoHeight + 5, { maxWidth: photoWidth });
                }

                console.log(`Imagem adicionada com sucesso: ${imageUrl}`);
              } catch (err) {
                console.error("Erro ao carregar ou adicionar imagem ao PDF:", err);
                addPlaceholder();
              }
            } else {
              // Se não houver arquivo, usar placeholder
              console.log("Arquivo de imagem não encontrado, usando placeholder");
              addPlaceholder();
            }
          } catch (error) {
            console.error("Erro ao processar imagem:", error);
            addPlaceholder();
          }
        }

        yPos += photoHeight * 2 + 20;
      }
    }

    // Adicionar número de página
    const totalPages = pdf.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      pdf.setPage(i);
      pdf.setFontSize(10);
      pdf.setFont("helvetica", "normal");
      pdf.text(`${i} / ${totalPages}`, pageWidth - margin, pageHeight - margin);
    }

    // Gerar o PDF como um blob URL em vez de salvá-lo diretamente
    const pdfBlob = pdf.output('blob');
    const blobUrl = URL.createObjectURL(pdfBlob);
    const fileName = `relatorio_inspecao_${inspection.numberInspection || Date.now()}.pdf`;

    return {
      success: true,
      blobUrl,
      fileName,
      blob: pdfBlob
    };
  } catch (error) {
    console.error("Erro ao gerar PDF:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}
