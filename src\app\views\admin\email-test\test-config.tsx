"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/src/components/ui/card";
import { toast } from "@/src/hooks/use-toast";
import { Loader2, CheckCircle, XCircle, AlertCircle, Bug } from "lucide-react";
import { Input } from "@/src/components/ui/input";

export default function EmailConfigTest() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [debugEmail, setDebugEmail] = useState("");
  const [debugLoading, setDebugLoading] = useState(false);

  const testEmailConfig = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('Iniciando teste de configuração de email...');
      const response = await fetch('/api/email-test');
      const data = await response.json();

      console.log('Resposta do teste de configuração:', data);

      if (response.ok && data.success) {
        setResult(data);
        toast({
          title: "Sucesso",
          description: "Configurações de email verificadas com sucesso",
        });
      } else {
        setError(data.error || 'Falha ao verificar configurações de email');
        setResult(data);
        toast({
          title: "Erro",
          description: data.error || "Falha ao verificar configurações de email",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Erro ao testar configurações de email:', error);
      setError('Erro ao testar configurações de email');
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao testar as configurações de email",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const testDebugEmail = async () => {
    if (!debugEmail) {
      toast({
        title: "Erro",
        description: "Por favor, informe um email para teste",
        variant: "destructive",
      });
      return;
    }

    setDebugLoading(true);
    try {
      console.log('Iniciando teste de debug de email...');
      const response = await fetch(`/api/email-debug?email=${encodeURIComponent(debugEmail)}`);
      const data = await response.json();

      console.log('Resposta do teste de debug:', data);

      if (response.ok && data.success) {
        toast({
          title: "Sucesso",
          description: `Email de teste enviado com sucesso para ${debugEmail}`,
        });
      } else {
        toast({
          title: "Erro",
          description: data.error || "Falha ao enviar email de teste",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Erro ao testar email de debug:', error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao testar o email de debug",
        variant: "destructive",
      });
    } finally {
      setDebugLoading(false);
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Teste de Configuração de Email</CardTitle>
        <CardDescription>
          Verifique se as configurações de email estão corretas e se é possível conectar ao servidor SMTP.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {result && (
          <div className="mb-4 p-4 rounded-md border">
            <h3 className="text-lg font-medium mb-2">Configurações de Email</h3>
            <div className="grid grid-cols-2 gap-2">
              <div className="font-medium">Host:</div>
              <div>{result.config?.host || 'Não definido'}</div>

              <div className="font-medium">Porta:</div>
              <div>{result.config?.port || 'Não definido'}</div>

              <div className="font-medium">Usuário:</div>
              <div>{result.config?.user || 'Não definido'}</div>

              <div className="font-medium">Email de Origem:</div>
              <div>{result.config?.from || 'Não definido'}</div>
            </div>

            {error ? (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-800">
                <div className="flex items-center gap-2 mb-2">
                  <XCircle className="h-5 w-5 text-red-600" />
                  <span className="font-medium">Erro na configuração</span>
                </div>
                <p>{error}</p>
                {result.details && (
                  <div className="mt-2 text-sm">
                    <p><strong>Detalhes:</strong> {result.details.message}</p>
                    {result.details.code && <p><strong>Código:</strong> {result.details.code}</p>}
                    {result.details.response && <p><strong>Resposta:</strong> {result.details.response}</p>}
                  </div>
                )}
              </div>
            ) : (
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md text-green-800">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span>Configurações verificadas com sucesso</span>
                </div>
              </div>
            )}
          </div>
        )}

        {!result && !error && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-md text-blue-800">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-blue-600" />
              <span>Clique no botão abaixo para testar as configurações de email</span>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex flex-col space-y-4">
        <Button
          onClick={testEmailConfig}
          disabled={loading}
          className="w-full"
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Verificando...
            </>
          ) : (
            "Testar Configurações de Email"
          )}
        </Button>

        <div className="w-full pt-4 border-t">
          <h3 className="text-sm font-medium mb-2">Teste de Debug de Email</h3>
          <div className="flex items-center space-x-2">
            <Input
              placeholder="Digite um email para teste"
              value={debugEmail}
              onChange={(e) => setDebugEmail(e.target.value)}
              className="flex-1"
            />
            <Button
              onClick={testDebugEmail}
              disabled={debugLoading || !debugEmail}
              variant="outline"
              className="whitespace-nowrap"
            >
              {debugLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Enviando...
                </>
              ) : (
                <>
                  <Bug className="mr-2 h-4 w-4" />
                  Testar Email
                </>
              )}
            </Button>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Este teste envia um email real para o endereço informado e exibe logs detalhados no console do servidor.
          </p>
        </div>
      </CardFooter>
    </Card>
  );
}
