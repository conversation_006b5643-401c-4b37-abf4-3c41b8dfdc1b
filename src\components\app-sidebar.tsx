"use client";

import { cn } from "@/lib/utils";
import { Building2, ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState, useEffect } from "react";
import { NavUser } from "./nav-user";
import logoGreen from "@public/logos/logo.svg";
import { routeData } from "../lib/routes/routes";
import { OrganizationSwitcher } from "./organization-switcher";
import { useIsMobile } from "@src/hooks/use-mobile";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { getUserRoutesPermissions } from "@/src/actions/route-permissions";
import { useSession } from "next-auth/react";

interface SidebarProps {
  user: any;
  membership?: {
    id: string;
    role: string;
    organizationId: string;
    organizationName: string;
  };
}

export function AppSidebar({ user, membership }: SidebarProps) {
  const isMobile = useIsMobile();
  const [isCollapsed, setIsCollapsed] = useState(isMobile);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [filteredRoutes, setFilteredRoutes] = useState<any[]>([]);
  const [loadingRoutes, setLoadingRoutes] = useState(true);
  const pathname = usePathname();
  const { status } = useSession();

  // Função recursiva para coletar todas as rotas do menu
  const collectRoutes = (items: any[]): string[] => {
    let routes: string[] = [];
    for (const item of items) {
      if (item.url && item.url !== "") {
        routes.push(item.url);
      }
      if (item.items && item.items.length > 0) {
        routes = routes.concat(collectRoutes(item.items));
      }
    }
    return routes;
  };

  // Função recursiva para filtrar itens baseado nas permissões já carregadas
  const filterItemsByPermissionsLocal = (items: any[], permissions: Record<string, boolean>): any[] => {
    const filteredItems: any[] = [];
    for (const item of items) {
      const hasAccess = !item.url || item.url === "" || permissions[item.url];
      if (hasAccess) {
        const filteredItem = { ...item };
        if (item.items && item.items.length > 0) {
          const filteredSubItems = filterItemsByPermissionsLocal(item.items, permissions);
          filteredItem.items = filteredSubItems;
          if (filteredSubItems.length === 0 && (!item.url || item.url === "")) {
            continue;
          }
        }
        filteredItems.push(filteredItem);
      }
    }
    return filteredItems;
  };

  // Filtrar rotas quando o componente monta ou quando user/membership muda
  useEffect(() => {
    if (status !== "authenticated") {
      setLoadingRoutes(true);
      return;
    }
    setLoadingRoutes(true);
    const filterRoutes = async () => {
      try {
        // Coletar todas as rotas do menu
        const allRoutes = collectRoutes(routeData);
        // Buscar permissões em lote
        const permissions = await getUserRoutesPermissions(allRoutes);
        // Filtrar menu localmente
        const filtered = filterItemsByPermissionsLocal(routeData, permissions);
        setFilteredRoutes(filtered);
      } catch (error) {
        console.error("Erro ao filtrar rotas:", error);
        setFilteredRoutes([]);
      } finally {
        setLoadingRoutes(false);
      }
    };
    filterRoutes();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, membership, status]);

  // Atualiza o estado do sidebar quando o tamanho da tela muda
  useEffect(() => {
    if (isMobile) {
      setIsCollapsed(true);
    }

    // Adiciona um listener para o evento personalizado de toggle do sidebar
    const handleToggleSidebar = () => {
      setIsCollapsed(false);
    };

    window.addEventListener('toggle-sidebar', handleToggleSidebar);

    return () => {
      window.removeEventListener('toggle-sidebar', handleToggleSidebar);
    };
  }, [isMobile]);

  const activeClass = "!font-semibold !text-green-500 !bg-green-100";
  const hoverClass = "hover:!text-green-500 hover:!bg-green-100";

  const toggleExpanded = (title: string) => {
    setExpandedItems((prev) =>
      prev.includes(title)
        ? prev.filter((item) => item !== title)
        : [...prev, title]
    );
  };

  // Verifica se um item está expandido
  const isExpanded = (title: string) => expandedItems.includes(title);

  // Verifica se um item ou seus subitens estão ativos
  const isActive = (item: any): boolean => {
    if (item.url && pathname === item.url) {
      return true;
    }

    if (item.items) {
      return item.items.some((subItem: any) => {
        if (subItem.url && pathname === subItem.url) {
          return true;
        }
        if (subItem.items) {
          return subItem.items.some(
            (nestedItem: any) => nestedItem.url && pathname === nestedItem.url
          );
        }
        return false;
      });
    }

    return false;
  };

  return (
    <>
      {/* Overlay para dispositivos móveis */}
      {isMobile && !isCollapsed && (
        <div
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setIsCollapsed(true)}
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed top-0 left-0 z-50 h-screen flex flex-col bg-white shadow-lg transition-all duration-300",
          isCollapsed ? (isMobile ? "-ml-80 w-80" : "w-16") : "w-80", // Quando recolhido em mobile, move para fora da tela
          "lg:relative lg:z-auto",
          "md:block" // Garante que a sidebar seja exibida em telas médias e grandes
        )}
      >
        {/* Toggle button - Centralizado verticalmente (apenas visível em desktop ou quando o sidebar está expandido) */}
        {(!isMobile || !isCollapsed) && (
          <div className="absolute -right-3 top-1/2 transform -translate-y-1/2 z-50">
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="bg-white rounded-full p-1 shadow-md hover:shadow-lg transition-all duration-200"
              aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
              title={isCollapsed ? "Expandir" : "Recolher"}
            >
              <div className="flex items-center justify-center w-6 h-6 rounded-full bg-gray-50 hover:bg-gray-100">
                {isCollapsed ? (
                  <ChevronRight className="w-4 h-4 text-green-500" />
                ) : (
                  <ChevronRight className="w-4 h-4 text-green-500 rotate-180" />
                )}
              </div>
            </button>
          </div>
        )}

        {/* Conteúdo da Sidebar - Dividido em 3 partes */}
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-4">
            <Link href="/views/control-panel" className="flex justify-center">
              <Image
                src={logoGreen}
                alt="Logo"
                className={cn("transition-all", isCollapsed ? "w-12" : "w-32")}
                priority
              />
            </Link>
          </div>

          {/* Navigation - Área com scroll */}
          <div className={cn("flex-1 overflow-y-auto", isCollapsed ? "px-1" : "px-2")}>
            <nav className={cn("space-y-2 mt-2", isCollapsed ? "p-0" : "p-1")}>
              {loadingRoutes || status === "loading" ? (
                <div className="text-center text-gray-400 py-8 text-sm">Carregando menu...</div>
              ) : filteredRoutes.length === 0 ? (
                <div className="text-center text-gray-400 py-8 text-sm">Nenhuma rota disponível</div>
              ) : (
                filteredRoutes.map((item) => (
                  <div key={item.title} className="space-y-2 text-gray-600">
                    {item.items ? (
                      <div>
                        {/* Item com subitens */}
                        <button
                          onClick={() => toggleExpanded(item.title)}
                          className={cn(
                            "w-full flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50",
                            isCollapsed ? "justify-center" : "justify-between"
                          )}
                          title={isCollapsed ? item.title : undefined}
                        >
                          {isCollapsed ? (
                            <DropdownMenu>
                              <DropdownMenuTrigger className="flex items-center justify-center cursor-pointer w-full h-full">
                                <item.icon className="w-5 h-5" />
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="start" className="w-64 p-2" sideOffset={5}>
                                <div className="font-medium text-sm px-2 py-1 mb-1 text-gray-500 border-b">{item.title}</div>
                                {item.items.map((subItem) => {
                                  // Verificar se o subitem tem seus próprios subitens
                                  if (subItem.items && subItem.items.length > 0) {
                                    return (
                                      <div key={subItem.url || subItem.title} className="mb-1">
                                        <div className="flex items-center gap-2 px-2 py-1 text-sm font-medium text-gray-700">
                                          <subItem.icon className="w-4 h-4" />
                                          <span>{subItem.title}</span>
                                        </div>
                                        <div className="pl-6 border-l border-gray-200 ml-2 mt-1 space-y-1">
                                          {subItem.items.map((nestedItem) => (
                                            <DropdownMenuItem key={nestedItem.url || nestedItem.title} asChild>
                                              <Link
                                                href={nestedItem.url || "#"}
                                                className="flex items-center gap-2 w-full text-sm py-1"
                                              >
                                                <nestedItem.icon className="w-3 h-3" />
                                                <span>{nestedItem.title}</span>
                                              </Link>
                                            </DropdownMenuItem>
                                          ))}
                                        </div>
                                      </div>
                                    );
                                  }

                                  // Caso seja um subitem normal
                                  return (
                                    <div key={subItem.url || subItem.title}>
                                      {subItem.disableLink ? (
                                        <DropdownMenuItem disabled className="opacity-50 cursor-not-allowed">
                                          <div className="flex items-center gap-2">
                                            <subItem.icon className="w-4 h-4" />
                                            <span>{subItem.title}</span>
                                          </div>
                                        </DropdownMenuItem>
                                      ) : (
                                        <DropdownMenuItem asChild>
                                          <Link href={subItem.url || "#"} className="flex items-center gap-2 w-full">
                                            <subItem.icon className="w-4 h-4" />
                                            <span>{subItem.title}</span>
                                          </Link>
                                        </DropdownMenuItem>
                                      )}
                                    </div>
                                  );
                                })}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          ) : (
                            <div className="flex items-center gap-2">
                              <item.icon className="w-5 h-5" />
                              <span>{item.title}</span>
                            </div>
                          )}

                          {!isCollapsed && (
                            <ChevronRight
                              className={cn(
                                "w-4 h-4 transition-transform",
                                isExpanded(item.title) && "rotate-90"
                              )}
                            />
                          )}
                        </button>

                        {/* Subitens expandidos */}
                        {!isCollapsed && isExpanded(item.title) && (
                          <div className="mt-1 ml-2 pl-2 border-l border-gray-200 space-y-1">
                            {item.items.map((subItem) => (
                              <div key={subItem.url || subItem.title}>
                                {subItem.items ? (
                                  <div>
                                    <button
                                      onClick={() => toggleExpanded(subItem.title)}
                                      className={cn(
                                        "w-full flex items-center justify-between gap-2 px-3 py-2 text-sm rounded-md transition-colors",
                                        isActive(subItem) ? activeClass : hoverClass
                                      )}
                                    >
                                      <div className="flex items-center gap-2">
                                        <subItem.icon className="w-5 h-5" />
                                        <span>{subItem.title}</span>
                                      </div>
                                      <ChevronRight
                                        className={cn(
                                          "w-4 h-4 transition-transform",
                                          isExpanded(subItem.title) && "rotate-90"
                                        )}
                                      />
                                    </button>

                                    {isExpanded(subItem.title) && (
                                      <div className="mt-1 ml-2 pl-2 border-l border-gray-200 space-y-1">
                                        {subItem.items.map((nestedItem) => (
                                          <Link
                                            key={nestedItem.url || nestedItem.title}
                                            href={nestedItem.url || "#"}
                                            className={cn(
                                              "flex items-center gap-2 px-3 py-2 text-sm rounded-md transition-colors",
                                              pathname === nestedItem.url ? activeClass : hoverClass
                                            )}
                                          >
                                            <nestedItem.icon className="w-5 h-5" />
                                            <span
                                              className="w-[80%] truncate"
                                              title={nestedItem.title}
                                            >
                                              {nestedItem.title}
                                            </span>
                                          </Link>
                                        ))}
                                      </div>
                                    )}
                                  </div>
                                ) : (
                                  // Link normal
                                  <Link
                                    href={subItem.url || "#"}
                                    className={cn(
                                      "flex items-center gap-2 px-3 py-2 text-sm rounded-md transition-colors",
                                      pathname === subItem.url ? activeClass : hoverClass
                                    )}
                                  >
                                    <subItem.icon className="w-5 h-5" />
                                    <span
                                      className="w-[80%] truncate"
                                      title={subItem.title}
                                    >
                                      {subItem.title}
                                    </span>
                                  </Link>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ) : (
                      // Item sem subitens
                      item.url ? (
                        isCollapsed ? (
                          <DropdownMenu>
                            <DropdownMenuTrigger className="flex items-center justify-center cursor-pointer w-full h-full">
                              <item.icon className="w-5 h-5" />
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="start" className="w-56 p-2" sideOffset={5}>
                              <DropdownMenuItem asChild>
                                <Link href={item.url} className="flex items-center gap-2 w-full">
                                  <item.icon className="w-4 h-4" />
                                  <span>{item.title}</span>
                                </Link>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        ) : (
                          <Link
                            href={item.url}
                            className={cn(
                              "flex items-center gap-2 px-3 py-2 text-sm rounded-md transition-colors",
                              pathname === item.url ? activeClass : hoverClass
                            )}
                          >
                            <item.icon className="w-5 h-5" />
                            <span>{item.title}</span>
                          </Link>
                        )
                      ) : isCollapsed ? (
                        <div
                          className={cn(
                            "flex items-center justify-center px-3 py-2 text-sm rounded-md transition-colors cursor-pointer",
                            hoverClass
                          )}
                          title={item.title}
                        >
                          <item.icon className="w-5 h-5" />
                        </div>
                      ) : (
                        <div
                          className={cn(
                            "flex items-center gap-2 px-3 py-2 text-sm rounded-md transition-colors cursor-pointer",
                            hoverClass
                          )}
                          onClick={() => toggleExpanded(item.title)}
                        >
                          <item.icon className="w-5 h-5" />
                          <span>{item.title}</span>
                        </div>
                      )
                    )}
                  </div>
                ))
              )}
            </nav>
          </div>

          {/* Footer - Sempre visível na parte inferior */}
          <div className={cn("mt-auto border-t bg-white", isMobile && "mb-4")}>
            {/* Espaço adicional para dispositivos móveis */}
            {isMobile && <div className="h-2"></div>}
            {membership && (
              <div className={cn("border-b", isCollapsed ? "px-2 py-2" : "px-4 py-2")}>
                {membership.role == "OWNER" ? (
                  <OrganizationSwitcher
                    currentOrganizationId={membership.organizationId}
                    currentOrganizationName={membership.organizationName}
                    isCollapsed={isCollapsed}
                  />
                ) : isCollapsed ? (
                  <div className="flex justify-center" title={membership.organizationName}>
                    <Building2 className="h-5 w-5 text-gray-600" />
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Building2 className="h-4 w-4" />
                    <span className="font-medium">Organização:</span>
                    <span className="truncate">{membership.organizationName}</span>
                  </div>
                )}
              </div>
            )}
            <div className={cn("w-full", isMobile ? "p-4 pb-6" : "p-2")}>
              <NavUser user={user} isCollapsed={isCollapsed} />
              {/* Espaço extra no final para dispositivos móveis */}
              {isMobile && <div className="h-4"></div>}
            </div>
          </div>
        </div>
      </aside >
    </>
  );
}
