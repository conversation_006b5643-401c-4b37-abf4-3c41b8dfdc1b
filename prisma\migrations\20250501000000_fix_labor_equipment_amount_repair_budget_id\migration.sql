-- <PERSON><PERSON>, tornamos a coluna repairBudgetId nullable
ALTER TABLE "LaborEquipmentAmount" ALTER COLUMN "repairBudgetId" DROP NOT NULL;

-- <PERSON><PERSON><PERSON>, vamos criar uma consulta para identificar os registros que precisam ser atualizados
-- Vamos associar cada LaborEquipmentAmount a um RepairBudget existente
-- Esta é uma abordagem segura para atualizar os registros existentes

-- Primeiro, vamos verificar se existem RepairBudgets disponíveis
DO $$
DECLARE
    repair_budget_count INTEGER;
    default_repair_budget_id TEXT;
BEGIN
    -- Conta quantos RepairBudgets existem
    SELECT COUNT(*) INTO repair_budget_count FROM "RepairBudget";
    
    IF repair_budget_count > 0 THEN
        -- Se existirem RepairBudgets, pega o ID do primeiro
        SELECT id INTO default_repair_budget_id FROM "RepairBudget" LIMIT 1;
        
        -- Atualiza todos os registros que têm repairBudgetId NULL
        UPDATE "LaborEquipmentAmount"
        SET "repairBudgetId" = default_repair_budget_id
        WHERE "repairBudgetId" IS NULL;
        
        -- Registra a operação
        RAISE NOTICE 'Atualizados % registros com o RepairBudget ID: %', 
                     (SELECT COUNT(*) FROM "LaborEquipmentAmount" WHERE "repairBudgetId" = default_repair_budget_id), 
                     default_repair_budget_id;
    ELSE
        RAISE NOTICE 'Não existem RepairBudgets disponíveis para associação.';
        -- Neste caso, você precisará criar um RepairBudget padrão ou lidar com isso de outra forma
    END IF;
END $$;

-- Depois que todos os registros forem atualizados, podemos tornar a coluna NOT NULL novamente
-- Mas só se todos os registros tiverem um valor
DO $$
DECLARE
    null_count INTEGER;
BEGIN
    -- Verifica se ainda existem registros com repairBudgetId NULL
    SELECT COUNT(*) INTO null_count FROM "LaborEquipmentAmount" WHERE "repairBudgetId" IS NULL;
    
    IF null_count = 0 THEN
        -- Se não houver registros NULL, torna a coluna NOT NULL novamente
        ALTER TABLE "LaborEquipmentAmount" ALTER COLUMN "repairBudgetId" SET NOT NULL;
        RAISE NOTICE 'Coluna repairBudgetId definida como NOT NULL novamente.';
    ELSE
        RAISE NOTICE 'Ainda existem % registros com repairBudgetId NULL. A coluna permanecerá nullable.', null_count;
    END IF;
END $$;
