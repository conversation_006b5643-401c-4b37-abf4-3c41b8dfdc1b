import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";
import { auth } from "@/src/providers/auth";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

export const dynamic = "force-dynamic";

export async function GET(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new NextResponse("Não autorizado", { status: 401 });
    }

    const { organizationId } = await getCurrentOrganization();
    const userId = session.user.id;
    const isOwner = session.membership?.role === "OWNER";
    const { searchParams } = new URL(request.url);
    console.log("URL completa:", request.url);

    // Extrair parâmetros da URL
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const search = searchParams.get("search") || "";
    const customerId = searchParams.get("customerId") || undefined;
    const situation = searchParams.getAll("situation");
    const serviceType = searchParams.getAll("serviceType");
    const ignorePermissions = searchParams.get("ignorePermissions") === "true";
    const onlyWithInspectionParameters = searchParams.get("onlyWithInspectionParameters") === "true";

    console.log("Parâmetros extraídos:", {
      page,
      pageSize,
      search,
      customerId,
      situation,
      serviceType,
      ignorePermissions,
      onlyWithInspectionParameters
    });

    // Validar parâmetros de paginação
    if (isNaN(page) || isNaN(pageSize) || page < 1 || pageSize < 1) {
      return NextResponse.json(
        { error: "Parâmetros de paginação inválidos" },
        { status: 400 }
      );
    }

    // Construir array de condições AND
    const andConditions: any[] = [];
    // Sempre filtrar pela organização
    andConditions.push({ customer: { organizationId } });
    // Situação
    if (situation.length > 0) {
      const allowedSituations = [
        "NEW", "UNDER_ANALYSIS", "PROPOSAL_SENT", "PROPOSAL_ACCEPTED",
        "SIGNED", "SIGN_REQUESTED",
        "PROJECT_IN_PROGRESS", "PROJECT_FINISHED", "LOST"
      ];
      const filteredSituations = situation.filter(s => allowedSituations.includes(s));
      if (filteredSituations.length > 0) {
        andConditions.push({ situation: { in: filteredSituations } });
      } else {
        return NextResponse.json({
          data: [], total: 0, page, pageSize, totalPages: 0
        });
      }
    } else {
      andConditions.push({ situation: { in: ["NEW", "UNDER_ANALYSIS", "PROPOSAL_SENT", "PROPOSAL_ACCEPTED"] } });
    }
    // Tipo de serviço
    if (serviceType.length > 0) {
      andConditions.push({ serviceType: { in: serviceType } });
    }
    // Cliente
    if (customerId) {
      andConditions.push({ customerId });
    }
    // Busca
    if (search) {
      andConditions.push({
        OR: [
          { name: { contains: search, mode: "insensitive" } },
          { customer: { name: { contains: search, mode: "insensitive" } } },
          { customService: { contains: search, mode: "insensitive" } },
        ]
      });
    }
    // Adicionar filtro para propostas que possuem inspeções cadastradas
    if (onlyWithInspectionParameters) {
      andConditions.push({ inspectionParameters: { some: {} } });
    }
    // Permissão
    let where;
    if (!isOwner && !ignorePermissions) {
      where = {
        AND: [
          ...(andConditions as any),
          {
            OR: [
              { permissions: { some: { userId, organizationId } } },
              { createdBy: userId }
            ]
          }
        ]
      };
    } else {
      where = { AND: andConditions as any };
    }
    // Montar query final
    const query: any = {
      where,
      include: {
        customer: { include: { contacts: { orderBy: { date: 'desc' as const } } } },
        plannings: true,
        serviceScopes: true,
        proposalTemplate: true,
        file: true,
        contract: true,
        permissions: true
      },
      orderBy: { createdAt: 'desc' as const },
    };
    // Paginação
    if (!customerId) {
      const skip = (page - 1) * pageSize;
      query.skip = skip;
      query.take = pageSize;
    }

    // Buscar propostas
    console.log("Executando query no banco...");
    const [proposals, total] = await Promise.all([
      prisma.proposal.findMany(query),
      prisma.proposal.count({ where: query.where }),
    ]);

    console.log("Resultados encontrados:", {
      total,
      propostas: proposals.map(p => ({
        id: p.id,
        situation: p.situation,
        serviceType: p.serviceType,
        createdBy: p.createdBy
      }))
    });

    // Verificar se há propostas no banco sem filtros
    const totalProposals = await prisma.proposal.count({
      where: {
        customer: {
          organizationId
        }
      }
    });
    console.log("Total de propostas na organização (sem filtros):", totalProposals);

    return NextResponse.json({
      items: proposals,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    });
  } catch (error) {
    console.error("Error loading proposals:", error);
    return new NextResponse("Erro interno do servidor", { status: 500 });
  }
}
