"use client";

import { removeInspectionParameter } from "@/src/actions/inspection-parameters";
import AppConfirmationDialog from "@/src/components/app-confirmation-dialog";
import { Button } from "@/src/components/ui/button";
import { TableGrid } from "@/src/components/ui/table-grid";
import { TouchTooltip } from "@/src/components/ui/touch-tooltip";
import { toast } from "@/src/hooks/use-toast";
import { formatDate } from "@/src/lib/utils";
import { InspectionParameter } from "@/src/types/core/inspection-paramenters";
import { Eraser, Plus, SquarePen, Trash } from "lucide-react";
import { useRouter } from "next/navigation";
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from "react";

interface InspectionParametersTableProps {
  proposalId: string | null;
  onPageChange?: (page: number) => void;
  onAddClick: () => void;
}

export type InspectionParametersTableRef = {
  refresh: (page?: number, searchTerm?: string) => void;
};

const InspectionParametersTable = forwardRef<InspectionParametersTableRef, InspectionParametersTableProps>(
  function InspectionParametersTable({ proposalId, onPageChange, onAddClick }, ref) {
    const [data, setData] = useState<InspectionParameter[]>([]);
    const [loading, setLoading] = useState(false);
    const [search, setSearch] = useState("");
    const searchInputRef = useRef<HTMLInputElement>(null);
    const router = useRouter();
    const [pagination, setPagination] = useState({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });

    const fetchInspectionParameters = useCallback(async (page = 1, pageSize = 10, searchTerm = "") => {
      try {
        setLoading(true);
        if (!proposalId) return;

        const url = new URL(
          `${window.location.origin}/api/inspection-parameters`
        );
        url.searchParams.append("proposalId", proposalId);
        url.searchParams.append("page", page.toString());
        url.searchParams.append("pageSize", pageSize.toString());
        if (searchTerm) url.searchParams.append("search", searchTerm);

        const response = await fetch(url.toString());
        if (!response.ok) {
          throw new Error("Failed to fetch inspection parameters");
        }

        const result = await response.json();
        setData(result.items || []);
        setPagination(result.pagination);
      } catch (error) {
        console.error("Error fetching inspection parameters:", error);
        toast({
          title: "Erro",
          description: "Erro ao carregar parâmetros de inspeção",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }, [proposalId]);

    const deleteInspectionParameter = async (id: string) => {
      try {
        const result = await removeInspectionParameter(id);
        if (result) {
          // Refresh the data after deletion
          fetchInspectionParameters(pagination.page, pagination.pageSize, search);

          // Exibir toast de sucesso no estilo da tela de edição
          toast({
            title: "Sucesso",
            description: "Parâmetro de inspeção excluído com sucesso!",
            variant: "default"
          });
        }
      } catch (error) {
        console.error("Erro ao excluir parâmetro de inspeção:", error);
        toast({
          title: "Erro",
          description: "Ocorreu um erro ao excluir o parâmetro de inspeção. Tente novamente.",
          variant: "destructive"
        });
      }
    };

    const handleSearch = (value: string) => {
      setSearch(value);
      fetchInspectionParameters(1, pagination.pageSize, value);
    };

    const handleClearFilters = () => {
      setSearch("");
      if (searchInputRef.current) {
        searchInputRef.current.value = "";
      }
      fetchInspectionParameters(1, pagination.pageSize, "");
    };

    useImperativeHandle(ref, () => ({
      refresh: (page?: number, searchTerm?: string) => {
        const newSearch = searchTerm !== undefined ? searchTerm : search;
        const currentPage = page !== undefined ? page : (pagination.page || 1);
        fetchInspectionParameters(currentPage, pagination.pageSize, newSearch);
      }
    }), [fetchInspectionParameters, pagination.page, pagination.pageSize, search]);

    useEffect(() => {
      if (proposalId) {
        fetchInspectionParameters(pagination.page, pagination.pageSize, search);
      }
    }, [proposalId, fetchInspectionParameters]);

    const columns = [
      {
        key: "numberInspection",
        accessorKey: "numberInspection",
        header: "Nº Inspeção"
      },
      {
        key: "technicalData",
        accessorKey: "technicalData",
        header: "Dados técnicos",
        cell: (row: any) => {
          const technicalData = row.technicalData as string;
          const displayText = technicalData.length > 50 ? technicalData.substring(0, 50) + "..." : technicalData;

          return (
            <TouchTooltip
              content={technicalData}
              maxWidth="400px"
              className="max-w-[200px] truncate hover:text-blue-500 transition-colors"
            >
              {displayText}
            </TouchTooltip>
          );
        }
      },
      {
        key: "observation",
        accessorKey: "observation",
        header: "Observações",
        cell: (row: any) => {
          const observation = row.observation as string;
          const displayText = observation.length > 50 ? observation.substring(0, 50) + "..." : observation;

          return (
            <TouchTooltip
              content={observation}
              maxWidth="400px"
              className="max-w-[200px] truncate hover:text-blue-500 transition-colors"
            >
              {displayText}
            </TouchTooltip>
          );
        }
      },
      {
        key: "inspectionDate",
        accessorKey: "inspectionDate",
        header: "Data da inspeção",
        cell: (row: any) => formatDate(row.inspectionDate, "DATE")
      },
      {
        key: "actions",
        accessorKey: "actions",
        header: "Ações",
        cell: (row: any) => {
          return (
            <div className="flex gap-3">
              <SquarePen
                className="size-5 text-green-500 cursor-pointer"
                onClick={() => router.push(
                  `/views/crm/proposals/inspection-parameters/form?proposalId=${proposalId}&id=${row.id}`
                )}
              />
              <AppConfirmationDialog
                title="Deseja realmente excluir o parâmetro de inspeção?"
                description="Esta operação não poderá ser desfeita."
                onConfirmCallback={() => deleteInspectionParameter(row.id)}
              >
                <Trash className="size-5 cursor-pointer" color="#ef4444" />
              </AppConfirmationDialog>
            </div>
          );
        }
      }
    ];

    return (
      <TableGrid
        columns={columns}
        data={data}
        loading={loading}
        pagination={pagination}
        onPaginationChange={(newPage, newPageSize) => {
          fetchInspectionParameters(newPage, newPageSize, search);
          if (onPageChange) onPageChange(newPage);
        }}
        onSearch={handleSearch}
        searchValue={search}
        searchInputRef={searchInputRef}
        buttonsTemplate={
          <>
            <Button
              className="w-full sm:w-auto bg-blue-500 hover:bg-blue-400"
              onClick={handleClearFilters}
            >
              Limpar filtros <Eraser className="ml-2 h-4 w-4" />
            </Button>
            <Button
              className="w-full sm:w-auto bg-green-500 hover:bg-green-400"
              onClick={onAddClick}
              disabled={!proposalId}
            >
              Adicionar <Plus className="ml-2 h-4 w-4" />
            </Button>
          </>
        }
      />
    );
  }
);

export default InspectionParametersTable;
