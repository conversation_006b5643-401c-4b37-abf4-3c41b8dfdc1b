"use client";

import { <PERSON><PERSON> } from "@/src/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog";
import { Checkbox } from "@/src/components/ui/checkbox";
import { Label } from "@/src/components/ui/label";
import { useState } from "react";
import { DateRange } from "react-day-picker";
import { useToast } from "@/src/hooks/use-toast";
import { FileText, Loader2 } from "lucide-react";

interface PdfExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  dateRange?: DateRange;
}

export default function PdfExportDialog({
  open,
  onOpenChange,
  dateRange,
}: PdfExportDialogProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [selectedCharts, setSelectedCharts] = useState({
    revenue: true,
    squareMeter: true,
    conversionRate: true,
    contractsCount: true,
    statusDistribution: true,
  });

  const handleExport = async () => {
    try {
      setLoading(true);

      // Verificar se pelo menos um gráfico foi selecionado
      if (!Object.values(selectedCharts).some(Boolean)) {
        throw new Error("Selecione pelo menos um gráfico para exportar");
      }

      // Importar as bibliotecas dinamicamente
      const html2canvasModule = await import('html2canvas');
      const html2canvas = html2canvasModule.default;

      const jspdfModule = await import('jspdf');
      const jsPDF = jspdfModule.jsPDF;

      // // Criar um novo documento PDF
      const pdf = new jsPDF('p', 'mm', 'a4');
      let yOffset = 10;

      // Adicionar título ao PDF
      pdf.setFontSize(18);
      pdf.setTextColor(0, 128, 0);
      pdf.text("Relatório de Indicadores", 105, yOffset, { align: "center" });
      yOffset += 10;

      // Adicionar período do relatório
      pdf.setFontSize(12);
      pdf.setTextColor(0, 0, 0);
      const periodText = dateRange?.from && dateRange?.to
        ? `Período: ${dateRange.from.toLocaleDateString()} a ${dateRange.to.toLocaleDateString()}`
        : "Período: Todo o período disponível";
      pdf.text(periodText, 105, yOffset, { align: "center" });
      yOffset += 15;

      // Capturar e adicionar cada gráfico selecionado
      const chartIds = [
        { id: "revenue-chart", selected: selectedCharts.revenue, title: "Faturamento Mensal" },
        { id: "square-meter-chart", selected: selectedCharts.squareMeter, title: "Valor por Metro Quadrado" },
        { id: "conversion-rate-chart", selected: selectedCharts.conversionRate, title: "Taxa de Conversão de Propostas" },
        { id: "contracts-count-chart", selected: selectedCharts.contractsCount, title: "Quantidade de Contratos" },
        { id: "status-distribution-chart", selected: selectedCharts.statusDistribution, title: "Distribuição por Status" }
      ];

      // Filtrar apenas os gráficos selecionados
      const selectedChartIds = chartIds.filter(chart => chart.selected);

      // Capturar cada gráfico e adicionar ao PDF
      for (let i = 0; i < selectedChartIds.length; i++) {
        const chart = selectedChartIds[i];
        const element = document.getElementById(chart.id);

        if (element) {
          // Capturar o gráfico como imagem
          const canvas = await html2canvas(element, {
            scale: 2, // Aumentar a qualidade
            logging: false,
            useCORS: true,
            allowTaint: true
          });

          // Converter para imagem
          const imgData = canvas.toDataURL('image/png');

          // Adicionar título do gráfico
          pdf.setFontSize(14);
          pdf.setTextColor(0, 100, 0);
          pdf.text(chart.title, 105, yOffset, { align: "center" });
          yOffset += 8;

          // Calcular dimensões para manter a proporção
          const imgWidth = 180;
          const imgHeight = (canvas.height * imgWidth) / canvas.width;

          // Verificar se precisa adicionar uma nova página
          if (yOffset + imgHeight > 280) {
            pdf.addPage();
            yOffset = 10;
          }

          // Adicionar a imagem ao PDF
          pdf.addImage(imgData, 'PNG', 15, yOffset, imgWidth, imgHeight);
          yOffset += imgHeight + 15;
        }
      }

      // Salvar o PDF
      const today = new Date();
      const fileName = `indicadores_${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}.pdf`;
      pdf.save(fileName);

      toast({
        title: "Sucesso",
        description: "PDF exportado com sucesso!",
        variant: "default",
        duration: 3000 // 3 segundos em vez do padrão
      });
      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao exportar PDF:", error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Erro ao exportar PDF. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-blue-700 flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Exportar PDF
          </DialogTitle>
          <DialogDescription className="text-blue-600">
            Selecione os gráficos que deseja incluir no PDF.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-100 mb-4">
            <h3 className="font-medium text-blue-800 mb-2">Período do relatório</h3>
            <p className="text-sm text-blue-700">
              {dateRange?.from && dateRange?.to
                ? `${dateRange.from.toLocaleDateString()} a ${dateRange.to.toLocaleDateString()}`
                : "Todo o período disponível"}
            </p>
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="revenue-pdf"
                checked={selectedCharts.revenue}
                onCheckedChange={(checked) =>
                  setSelectedCharts({ ...selectedCharts, revenue: !!checked })
                }
                className="border-blue-400 data-[state=checked]:bg-blue-600 data-[state=checked]:text-white"
              />
              <Label htmlFor="revenue-pdf" className="font-medium">
                Faturamento Mensal
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="squareMeter-pdf"
                checked={selectedCharts.squareMeter}
                onCheckedChange={(checked) =>
                  setSelectedCharts({ ...selectedCharts, squareMeter: !!checked })
                }
                className="border-blue-400 data-[state=checked]:bg-blue-600 data-[state=checked]:text-white"
              />
              <Label htmlFor="squareMeter-pdf" className="font-medium">
                Valor por Metro Quadrado
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="conversionRate-pdf"
                checked={selectedCharts.conversionRate}
                onCheckedChange={(checked) =>
                  setSelectedCharts({
                    ...selectedCharts,
                    conversionRate: !!checked,
                  })
                }
                className="border-blue-400 data-[state=checked]:bg-blue-600 data-[state=checked]:text-white"
              />
              <Label htmlFor="conversionRate-pdf" className="font-medium">
                Taxa de Conversão de Propostas
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="contractsCount-pdf"
                checked={selectedCharts.contractsCount}
                onCheckedChange={(checked) =>
                  setSelectedCharts({
                    ...selectedCharts,
                    contractsCount: !!checked,
                  })
                }
                className="border-blue-400 data-[state=checked]:bg-blue-600 data-[state=checked]:text-white"
              />
              <Label htmlFor="contractsCount-pdf" className="font-medium">
                Quantidade de Contratos
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="statusDistribution-pdf"
                checked={selectedCharts.statusDistribution}
                onCheckedChange={(checked) =>
                  setSelectedCharts({
                    ...selectedCharts,
                    statusDistribution: !!checked,
                  })
                }
                className="border-blue-400 data-[state=checked]:bg-blue-600 data-[state=checked]:text-white"
              />
              <Label htmlFor="statusDistribution-pdf" className="font-medium">
                Distribuição por Status
              </Label>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="border-gray-300"
          >
            Cancelar
          </Button>
          <Button
            onClick={handleExport}
            disabled={loading || !Object.values(selectedCharts).some(Boolean)}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Exportando...
              </>
            ) : (
              "Exportar PDF"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
