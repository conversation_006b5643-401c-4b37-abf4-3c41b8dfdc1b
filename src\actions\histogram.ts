"use server";
import { replaceFromFileEditorIdAndVariables } from "@/src/helpers/file-editor";
import { prisma } from "@/src/lib/prisma";
import { formatCurrency, parseObject } from "@/src/lib/utils";

export async function loadHistograms(proposalId: string) {
  try {
    console.log('CRITICAL FIX 3: Loading histograms for proposal:', proposalId);

    // 1. <PERSON><PERSON> todas as atividades (RepairBudget) para esta proposta
    const repairBudgets = await prisma.repairBudget.findMany({
      where: { proposalId },
      include: {
        serviceScope: true,
        planningFrequencyItems: true
      }
    });

    console.log(`CRITICAL FIX 3: Found ${repairBudgets.length} repair budgets for proposal`);

    // 2. <PERSON>car todas as medições (Productivity) para esta proposta
    const allProductivities = await prisma.productivity.findMany({
      where: { proposalId },
      include: {
        service: true,
        periodicity: true,
        repairBudget: {
          include: {
            serviceScope: true
          }
        }
      },
      orderBy: {
        startDate: 'asc' // Ordenar por data para garantir a ordem cronológica
      }
    });

    console.log(`CRITICAL FIX 3: Found ${allProductivities.length} productivity measurements for proposal`);

    // Exibir detalhes de cada medição para depuração
    allProductivities.forEach((prod, index) => {
      console.log(`CRITICAL FIX 3: Measurement ${index + 1}:`, {
        id: prod.id,
        repairBudgetId: prod.repairBudgetId,
        serviceId: prod.serviceId,
        serviceName: prod.service?.name,
        periodicityId: prod.periodicityId,
        periodicityLabel: prod.periodicity?.label,
        periodicityContent: prod.periodicity?.content,
        startDate: prod.startDate,
        predictedPeriodPercentage: prod.predictedPeriodPercentage,
        realPeriodPercentage: prod.realPeriodPercentage
      });
    });

    // 3. Extrair todos os períodos únicos das medições
    const uniquePeriods = new Set<string>();
    allProductivities.forEach(prod => {
      if (prod.periodicity && prod.periodicity.label) {
        uniquePeriods.add(prod.periodicity.label);
      }
    });

    console.log(`CRITICAL FIX 3: Found ${uniquePeriods.size} unique periods:`, Array.from(uniquePeriods));

    // 4. Extrair informações de mês/ano dos períodos
    const periodMonthYearMap = new Map<string, { month: number, year: number, label: string }>();

    allProductivities.forEach(prod => {
      if (prod.periodicity && prod.periodicity.content) {
        const content = prod.periodicity.content;
        const monthMatch = content.match(/month=(\d+)/);
        const yearMatch = content.match(/year=(\d+)/);

        if (monthMatch && yearMatch) {
          const month = parseInt(monthMatch[1], 10);
          const year = parseInt(yearMatch[1], 10);

          // Usar o ID do período como chave
          const key = prod.periodicityId;

          if (!periodMonthYearMap.has(key)) {
            periodMonthYearMap.set(key, {
              month,
              year,
              label: prod.periodicity.label || `${month}/${year}`
            });
          }
        }
      }
    });

    console.log('CRITICAL FIX 3: Period month/year mapping:', Object.fromEntries(periodMonthYearMap));

    // 5. Para cada atividade, garantir que temos dados para todos os períodos
    const result: any[] = [];

    // Processar cada medição e adicionar ao resultado
    for (const productivity of allProductivities) {
      // Verificar se temos todas as informações necessárias
      if (!productivity.service || !productivity.periodicity) {
        console.log('CRITICAL FIX 3: Skipping productivity without service or periodicity:', productivity.id);
        continue;
      }

      // Adicionar informações de cor para o gráfico
      const colorChart = 'rgb(22 163 74)'; // Verde como padrão
      const colorChartPredicted = 'rgb(37 99 235)'; // Azul como padrão

      // Obter informações de mês/ano para este período
      const periodInfo = periodMonthYearMap.get(productivity.periodicityId);

      // Criar o objeto de resultado com todos os dados necessários
      result.push({
        id: productivity.id,
        proposalId: productivity.proposalId,
        repairBudgetId: productivity.repairBudgetId,
        serviceId: productivity.serviceId,
        periodicityId: productivity.periodicityId,
        buildingPercentage: productivity.buildingPercentage,
        predictedPeriodPercentage: productivity.predictedPeriodPercentage,
        realPeriodPercentage: productivity.realPeriodPercentage,
        description: productivity.description,
        workersQuantity: productivity.workersQuantity,
        toolsQuantity: productivity.toolsQuantity,
        toolsDescription: productivity.toolsDescription,
        startDate: productivity.startDate,
        endDate: productivity.endDate,
        service: productivity.service,
        periodicity: productivity.periodicity,
        colorChart: colorChart,
        colorChartPredicted: colorChartPredicted,
        // Adicionar a ordem para garantir a ordenação correta no gráfico
        order: productivity.periodicity.order || 0,
        // Adicionar informações de mês/ano para facilitar o agrupamento
        monthYear: periodInfo ? {
          month: periodInfo.month,
          year: periodInfo.year,
          label: periodInfo.label
        } : null
      });
    }

    console.log(`CRITICAL FIX 3: Returning ${result.length} measurements for histogram`);

    return parseObject(result) as any;
  } catch (error) {
    console.error('CRITICAL FIX 3: Error in loadHistograms:', error);
    throw error;
  }
}

export async function findRepairBudgetByPeriod(proposalId: string, periodicity: string) {
  try {
    console.log('Finding repair budgets for proposal:', proposalId, 'and period:', periodicity);

    // Verificar se o período é um mês/ano (formato "MM-YYYY")
    const isMonthYearPeriod = periodicity.includes('-') && /^\d{1,2}-\d{4}$/.test(periodicity);

    if (isMonthYearPeriod) {
      // Extrair mês e ano do período
      const [monthStr, yearStr] = periodicity.split('-');
      // Converter para números e ajustar o mês (no formato de entrada, janeiro = 1, mas no Date, janeiro = 0)
      const month = parseInt(monthStr, 10) - 1; // Subtrair 1 para ajustar ao formato do Date
      const year = parseInt(yearStr, 10);

      console.log(`Original month-year: ${monthStr}-${yearStr}, adjusted for Date: month=${month}, year=${year}`);

      // Criar datas de início e fim do mês
      const startDate = new Date(year, month, 1);
      const endDate = new Date(year, month + 1, 0); // Último dia do mês

      console.log(`Period is month/year: ${month}/${year}, date range:`, startDate, 'to', endDate);

      // Buscar atividades que ocorrem neste período
      const repairBudgets = await prisma.repairBudget.findMany({
        where: {
          proposalId: proposalId,
          OR: [
            // Atividade começa ou termina dentro do mês
            {
              startDate: {
                gte: startDate,
                lte: endDate
              } as any
            },
            {
              endDate: {
                gte: startDate,
                lte: endDate
              } as any
            },
            // Atividade abrange todo o mês
            {
              startDate: {
                lte: startDate
              } as any,
              endDate: {
                gte: endDate
              } as any
            }
          ]
        },
        include: {
          serviceScope: true
        },
        orderBy: {
          createdAt: "desc"
        }
      });

      // Para cada atividade, buscar todas as medições para esta proposta
      // Isso garante que tenhamos acesso a todas as medições para cada atividade
      const repairBudgetsWithProductivity = await Promise.all(
        repairBudgets.map(async (budget) => {
          // Buscar todas as medições para esta atividade
          const allProductivities = await prisma.productivity.findMany({
            where: {
              repairBudgetId: budget.id,
              proposalId: proposalId
            },
            include: {
              periodicity: true
            },
            orderBy: {
              startDate: 'desc' // Ordenar pela data mais recente
            }
          });

          // Filtrar para encontrar a medição específica para este período
      // IMPORTANTE: month aqui já está ajustado para 0-11 para o Date, mas no content deve ser 1-12
      const realMonth = month + 1; // Converter de 0-11 para 1-12 para o content
      console.log(`CRITICAL: Searching for productivity with content: month=${realMonth};year=${year}`);

      // Buscar a medição pelo conteúdo exato
      let currentPeriodProductivity = allProductivities.find(prod =>
        prod.periodicity &&
        prod.periodicity.content === `month=${realMonth};year=${year}`
      );

      // Se não encontrar, tentar buscar pelo conteúdo com espaços extras (compatibilidade)
      if (!currentPeriodProductivity) {
        console.log(`CRITICAL: No productivity found with exact content, trying with spaces`);
        currentPeriodProductivity = allProductivities.find(prod =>
          prod.periodicity &&
          prod.periodicity.content.replace(/\s+/g, '') === `month=${realMonth};year=${year}`
        );
      }

      // Se ainda não encontrar, tentar buscar pelo mês e ano separadamente
      if (!currentPeriodProductivity) {
        console.log(`CRITICAL: Still no productivity found, trying to match month and year separately`);
        currentPeriodProductivity = allProductivities.find(prod => {
          if (!prod.periodicity || !prod.periodicity.content) return false;

          const content = prod.periodicity.content;
          return content.includes(`month=${realMonth}`) && content.includes(`year=${year}`);
        });
      }

      console.log(`CRITICAL: Found productivity for month=${realMonth}, year=${year}:`, currentPeriodProductivity ? 'YES' : 'NO');

          console.log(`Activity ${budget.id} has ${allProductivities.length} measurements, current period:`, currentPeriodProductivity);

          // CRITICAL FIX: Retornar a atividade com todas as suas medições
          // Marcar a medição do período atual para facilitar a identificação
          if (currentPeriodProductivity) {
            // Usar uma propriedade que não cause erro de tipo
            (currentPeriodProductivity as any).isSelectedPeriod = true;
          }

          // Retornar todas as medições, mas colocando a do período atual em primeiro
          return {
            ...budget,
            productivity: currentPeriodProductivity
              ? [currentPeriodProductivity, ...allProductivities.filter(p => p.id !== currentPeriodProductivity.id)]
              : allProductivities,
            // Adicionar todas as medições em uma propriedade separada para garantir que estejam disponíveis
            allProductivities: allProductivities
          };
        })
      );

      // Log para depuração
      console.log('Productivity data in repairBudgetsWithProductivity:', repairBudgetsWithProductivity.map((rb: any) => rb.productivity));

      console.log('Found repair budgets for month/year period:', repairBudgetsWithProductivity);
      return parseObject(repairBudgetsWithProductivity);
    }

    // Para períodos normais (não mês/ano), usar a abordagem existente com SQL direto
    const data = await prisma.$queryRaw`
      SELECT
        rb.*,
        ss."id" as "serviceScope_id",
        ss."name" as "serviceScope_name",
        p."id" as "productivity_id",
        p."predictedPeriodPercentage" as "productivity_predictedPeriodPercentage",
        p."realPeriodPercentage" as "productivity_realPeriodPercentage",
        p."startDate" as "productivity_startDate"
      FROM "RepairBudget" rb
      LEFT JOIN "ServiceScope" ss ON rb."serviceScopeId" = ss."id"
      LEFT JOIN (
        SELECT DISTINCT ON ("repairBudgetId") *
        FROM "Productivity"
        WHERE "periodicityId" = ${periodicity}
        ORDER BY "repairBudgetId", "startDate" DESC
      ) p ON p."repairBudgetId" = rb."id"
      WHERE rb."proposalId" = ${proposalId}
      AND EXISTS (
        SELECT 1 FROM "_PlanningFrequencyItemToRepairBudget" pf_rb
        JOIN "PlanningFrequencyItem" pf ON pf_rb."A" = pf."id"
        WHERE pf_rb."B" = rb."id" AND pf."id" = ${periodicity}
      )
      ORDER BY rb."createdAt" DESC
    `;

    // Log para depuração
    console.log('Productivity data in SQL query:', Array.isArray(data) ? data.map(item => ({
      productivity_id: item.productivity_id,
      productivity_startDate: item.productivity_startDate,
      productivity_predictedPeriodPercentage: item.productivity_predictedPeriodPercentage,
      productivity_realPeriodPercentage: item.productivity_realPeriodPercentage
    })) : 'Data is not an array');

    console.log('Found repair budgets via SQL:', data);

    // Converter os dados para o formato esperado
    const formattedData: any[] = [];

    if (data && Array.isArray(data)) {
      for (const item of data) {
        // Converter os valores de produtividade para números
        const predictedPeriodPercentage = item.productivity_predictedPeriodPercentage !== null ?
          Number(item.productivity_predictedPeriodPercentage) : 0;
        const realPeriodPercentage = item.productivity_realPeriodPercentage !== null ?
          Number(item.productivity_realPeriodPercentage) : 0;

        // Criar o objeto formatado
        const formattedItem = {
          ...item,
          serviceScope: {
            id: item.serviceScope_id,
            name: item.serviceScope_name
          },
          productivity: item.productivity_id ? [
            {
              id: item.productivity_id,
              predictedPeriodPercentage: predictedPeriodPercentage,
              realPeriodPercentage: realPeriodPercentage,
              startDate: item.productivity_startDate
            }
          ] : []
        };

        // Log para depuração da data de medição
        if (item.productivity_id && item.productivity_startDate) {
          console.log(`Productivity ${item.productivity_id} has startDate:`, item.productivity_startDate);
          console.log('startDate type:', typeof item.productivity_startDate);
          try {
            const date = new Date(item.productivity_startDate);
            console.log('Formatted date:', date.toISOString());
          } catch (e) {
            console.error('Error formatting date:', e);
          }
        }

        // Remover campos duplicados
        delete formattedItem.serviceScope_id;
        delete formattedItem.serviceScope_name;
        delete formattedItem.productivity_id;
        delete formattedItem.productivity_predictedPeriodPercentage;
        delete formattedItem.productivity_realPeriodPercentage;
        delete formattedItem.productivity_startDate;

        formattedData.push(formattedItem);
      }
    }

    console.log('Formatted data:', formattedData);

    return formattedData;
  } catch (e) {
    console.error(e);
    throw new Error("Erro ao buscar RepairBudget por periodicidade.");
  }
}

export async function saveRepairBudgetWithPeriods(
  repairBudget: any,
  planningFrequencyItem: any
) {
  try {
    delete planningFrequencyItem.proposalId;
    delete planningFrequencyItem.percentServiceRelationJob;
    delete planningFrequencyItem.description;
    delete planningFrequencyItem.serviceScopeId;

    const data = {
      ...planningFrequencyItem,
      service: {
        connect: { id: repairBudget.serviceScope.id },
      },
    };

    const res = planningFrequencyItem.id
      ? await prisma.productivity.update({
          where: { id: planningFrequencyItem.id },
          data: data,
          include: {
            service: true,
          },
        })
      : await prisma.productivity.create({
          data: data,
          include: {
            service: true,
          },
        });

    return res;
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function saveRepairBudgetHistogram(repairBudget: any) {
  try {
    // Extrair todas as propriedades, exceto periodicity
    const {
      proposalId,
      buildingPercentage,
      predictedPeriodPercentage,
      realPeriodPercentage,
      description,
      serviceId,
      workersQuantity,
      toolsQuantity,
      toolsDescription,
      repairBudgetId,
      startDate,
      endDate,
    } = repairBudget;

    // Extrair periodicity separadamente como let para permitir reatribuição
    let periodicity = repairBudget.periodicity; // periodicityId no modelo

    // Usar let para permitir reatribuição
    let id = repairBudget.id;

    // Garantir que os valores sejam números
    const predictedPercentage = Number(predictedPeriodPercentage) || 0;
    const realPercentage = Number(realPeriodPercentage) || 0;

    console.log('Saving productivity with values:');
    console.log('- predictedPeriodPercentage:', predictedPercentage);
    console.log('- realPeriodPercentage:', realPercentage);
    console.log('- typeof predictedPeriodPercentage:', typeof predictedPercentage);
    console.log('- typeof realPeriodPercentage:', typeof realPercentage);
    console.log('- startDate:', startDate);

    // Verificar se os valores são válidos
    if (isNaN(predictedPercentage)) {
      console.error('predictedPeriodPercentage is NaN');
    }
    if (isNaN(realPercentage)) {
      console.error('realPeriodPercentage is NaN');
    }

    // Verificar se a data de medição está presente
    if (!startDate) {
      console.error('startDate is required');
      throw new Error('Data da medição é obrigatória');
    }

    const measurementDate = new Date(startDate);

    // Verificar se já existe uma medição para esta combinação de proposta, atividade e período
    // Usamos findFirst com os critérios específicos para garantir que encontramos exatamente a medição correta
    const existingMeasurement = await prisma.productivity.findFirst({
      where: {
        proposalId: proposalId,
        repairBudgetId: repairBudgetId,
        periodicityId: periodicity,
        // Se já temos um ID, excluir esse registro da busca para evitar conflitos
        ...(id ? { NOT: { id } } : {})
      },
      include: {
        periodicity: true
      }
    });

    console.log('Checking for existing measurements for this activity and period using unique constraint:');
    console.log('- proposalId:', proposalId);
    console.log('- repairBudgetId:', repairBudgetId);
    console.log('- periodicityId:', periodicity);
    console.log('- current id:', id);
    console.log('- found existing measurement:', existingMeasurement);

    // Se encontramos uma medição existente para esta combinação, usar o ID dela
    if (existingMeasurement) {
      console.log('Found existing measurement for this combination:', existingMeasurement);
      console.log(`Using existing measurement ID (${existingMeasurement.id}) instead of creating a new one`);

      // Usar o ID da medição existente para atualizá-la em vez de criar uma nova
      id = existingMeasurement.id;

      // Atualizar a data de medição para a nova data, se fornecida
      console.log(`Updating measurement date from ${existingMeasurement.startDate} to ${measurementDate}`);
    } else if (id) {
      // Verificar se o ID fornecido corresponde a uma medição existente
      const measurementWithId = await prisma.productivity.findUnique({
        where: { id }
      });

      if (measurementWithId) {
        console.log('Using provided ID for update:', id);
      } else {
        console.log('Provided ID does not exist, will create a new measurement');
        // Limpar o ID para que uma nova medição seja criada
        id = undefined;
      }
    } else {
      console.log('No existing measurement found for this combination, will create a new one');
    }

    const data = {
      id,
      buildingPercentage: Number(buildingPercentage),
      predictedPeriodPercentage: predictedPercentage,
      realPeriodPercentage: realPercentage,
      description,
      toolsDescription,
      workersQuantity: Number(workersQuantity),
      toolsQuantity: Number(toolsQuantity),
      startDate: measurementDate,
      ...(endDate !== undefined
        ? { endDate: new Date(endDate) }
        : {}),
    };

    // Atualiza RepairBudget
    await prisma.repairBudget.update({
      where: { id: repairBudgetId },
      data: {
        laborAmount: data.workersQuantity,
        equipmentAmount: data.toolsQuantity,
        equipmentDescription: data.toolsDescription,
        description: data.description,
        // Atualizar a relação com PlanningFrequencyItem
        planningFrequencyItems: {
          connect: { id: periodicity }
        }
      },
    });

    // Verificar se estamos atualizando uma medição existente
    if (id) {
      // Se estamos atualizando uma medição existente, verificar se o periodicityId mudou
      console.log('Checking if periodicity changed for existing measurement');

      const existingMeasurement = await prisma.productivity.findUnique({
        where: { id },
        select: { periodicityId: true }
      });

      if (existingMeasurement && existingMeasurement.periodicityId === periodicity) {
        // Se o periodicityId não mudou, não precisamos verificar o PlanningFrequencyItem
        console.log('Periodicity has not changed, using existing PlanningFrequencyItem');
      } else {
        // Se o periodicityId mudou, precisamos verificar se o novo PlanningFrequencyItem existe
        // e usar o ID retornado (que pode ser diferente do original se for no formato mês-ano)
        const validPeriodicityId = await ensurePlanningFrequencyItemExists(periodicity, proposalId);

        // Se o ID retornado for diferente do original, atualizar o periodicity
        if (validPeriodicityId && validPeriodicityId !== periodicity) {
          console.log(`Updating periodicity from ${periodicity} to ${validPeriodicityId} (UUID format)`);
          periodicity = validPeriodicityId;
        }
      }
    } else {
      // Se estamos criando uma nova medição, verificar se o PlanningFrequencyItem existe
      // e usar o ID retornado (que pode ser diferente do original se for no formato mês-ano)
      const validPeriodicityId = await ensurePlanningFrequencyItemExists(periodicity, proposalId);

      // Se o ID retornado for diferente do original, atualizar o periodicity
      if (validPeriodicityId && validPeriodicityId !== periodicity) {
        console.log(`Updating periodicity from ${periodicity} to ${validPeriodicityId} (UUID format)`);
        periodicity = validPeriodicityId;
      }
    }

    // Função auxiliar para garantir que o PlanningFrequencyItem exista
    async function ensurePlanningFrequencyItemExists(periodicityId: string, proposalId: string) {
      console.log('Checking if PlanningFrequencyItem exists with ID:', periodicityId);
      console.log('Using repairBudgetId:', repairBudgetId);

      // Verificar se o período está no formato mês-ano (MM-YYYY)
      let monthYearLabel = 'Período';
      let isMonthYearFormat = false;
      let month = -1;
      let year = -1;

      if (periodicityId.includes('-') && /^\d{1,2}-\d{4}$/.test(periodicityId)) {
        isMonthYearFormat = true;
        // Extrair mês e ano do período
        const [monthStr, yearStr] = periodicityId.split('-');

        // IMPORTANTE: O mês no ID é 1-12 (janeiro=1, fevereiro=2, etc.)
        // Vamos armazenar exatamente esse valor no content
        month = parseInt(monthStr, 10); // Mês real (1-12)
        year = parseInt(yearStr, 10);

        console.log(`CRITICAL FIX: Original month-year from ID: ${monthStr}-${yearStr}`);
        console.log(`CRITICAL FIX: Storing month=${month} (1-12), year=${year}`);

        // Para exibição, precisamos ajustar o mês para o formato do Date (0-11)
        const displayMonth = month - 1; // Mês ajustado (0-11) para usar no Date

        console.log(`CRITICAL FIX: Display month (0-11): ${displayMonth}`);

        // Usar o mês ajustado (0-11) para obter o nome do mês
        const date = new Date(year, displayMonth, 1);
        const monthName = date.toLocaleString('pt-BR', { month: 'long' });

        // Usar o formato com espaço: "Abril / 2025"
        monthYearLabel = `${monthName.charAt(0).toUpperCase() + monthName.slice(1)} / ${year}`;

        console.log(`CRITICAL FIX: Label will be: ${monthYearLabel}`);
      }

      // Se estiver no formato mês-ano, buscar por mês e ano exatos
      let existingPlanningItem: any = null;

      if (isMonthYearFormat) {
        console.log(`Searching for PlanningFrequencyItem with month/year: ${month}-${year} for proposal: ${proposalId}`);

        // Buscar PlanningFrequencyItem pelo conteúdo estruturado
        // Importante: month aqui é o mês real (1-12), não precisa de ajuste
        console.log(`Searching for PlanningFrequencyItem with content: month=${month};year=${year}`);

        const existingItems = await prisma.planningFrequencyItem.findMany({
          where: {
            proposalId: proposalId,
            content: `month=${month};year=${year}` // Formato estruturado para busca exata
          },
          select: {
            id: true,
            content: true,
            label: true,
            order: true,
            repairBudgetId: true,
            proposalId: true
          },
          orderBy: {
            id: 'desc' // Ordenar pelo ID em vez de createdAt
          }
        });

        console.log(`Found ${existingItems.length} PlanningFrequencyItems for this month/year and proposal`);

        // Usar o primeiro item encontrado (o mais recente)
        if (existingItems.length > 0) {
          existingPlanningItem = existingItems[0];
          console.log(`Found existing PlanningFrequencyItem with UUID id: ${existingPlanningItem.id} for month/year: ${month}-${year}`);
        }
      } else {
        // Se não estiver no formato mês-ano, buscar pelo ID diretamente
        existingPlanningItem = await prisma.planningFrequencyItem.findUnique({
          where: { id: periodicityId },
          select: {
            id: true,
            content: true,
            label: true,
            order: true,
            repairBudgetId: true,
            proposalId: true
          }
        });
      }

      // Se o PlanningFrequencyItem não existir, criar um novo com UUID
      if (!existingPlanningItem) {
        console.log(`No PlanningFrequencyItem found. Creating a new one...`);

        try {
          // Criar um novo PlanningFrequencyItem com UUID (não usar o formato mês-ano como ID)
          const newPlanningItem = await prisma.planningFrequencyItem.create({
            data: {
              // Não especificar ID para que o Prisma gere um UUID automaticamente
              content: isMonthYearFormat ? `month=${month};year=${year}` : 'Período', // Formato estruturado para busca exata
              label: isMonthYearFormat ? monthYearLabel : 'Período', // Formato amigável para exibição
              order: 0,
              repairBudgetId: repairBudgetId, // Associar o repairBudgetId
              proposal: {
                connect: { id: proposalId },
              },
            },
          });

          console.log(`Created new PlanningFrequencyItem with UUID id: ${newPlanningItem.id}`);

          // Retornar o ID do novo item criado para ser usado como periodicityId
          return newPlanningItem.id;
        } catch (planningError) {
          console.error('Error creating PlanningFrequencyItem:', planningError);
          // Verificar se planningError é um Error ou tem uma propriedade message
          const errorMessage = planningError instanceof Error ? planningError.message :
                             typeof planningError === 'object' && planningError !== null && 'message' in planningError ?
                             (planningError as { message: string }).message :
                             String(planningError);
          throw new Error(`Erro ao criar item de planejamento: ${errorMessage}`);
        }
      } else {
        console.log(`Using existing PlanningFrequencyItem with id: ${existingPlanningItem.id}`);

        // Verificar se o item existente já tem um repairBudgetId
        if (!existingPlanningItem.repairBudgetId && repairBudgetId) {
          console.log(`Updating PlanningFrequencyItem ${existingPlanningItem.id} with repairBudgetId: ${repairBudgetId}`);

          // Atualizar o item existente para incluir o repairBudgetId
          await prisma.planningFrequencyItem.update({
            where: { id: existingPlanningItem.id },
            data: { repairBudgetId: repairBudgetId }
          });
        }

        // Retornar o ID do item existente para ser usado como periodicityId
        return existingPlanningItem.id;
      }
    }

    // Atualiza ou cria Productivity
    let result: any;

    if (id) {
      // Atualização
      console.log('Updating productivity record with ID:', id);

      // Definir explicitamente os campos que queremos atualizar
      result = await prisma.productivity.update({
        where: { id },
        data: {
          buildingPercentage: data.buildingPercentage,
          predictedPeriodPercentage: data.predictedPeriodPercentage,
          realPeriodPercentage: data.realPeriodPercentage,
          description: data.description,
          toolsDescription: data.toolsDescription,
          workersQuantity: data.workersQuantity,
          toolsQuantity: data.toolsQuantity,
          startDate: data.startDate,
          endDate: data.endDate,
          periodicity: { connect: { id: periodicity } },
          service: { connect: { id: serviceId } },
          proposal: { connect: { id: proposalId } },
          repairBudget: { connect: { id: repairBudgetId } },
        },
        include: { service: true, repairBudget: true, periodicity: true },
      });

      console.log('Updated productivity record:', result);
      console.log('- predictedPeriodPercentage after update:', result.predictedPeriodPercentage);
      console.log('- realPeriodPercentage after update:', result.realPeriodPercentage);
    } else {
      // Criação
      console.log('Creating new productivity record');

      // Gerar cores aleatórias para o gráfico
      const r = Math.floor(Math.random() * 256);
      const g = Math.floor(Math.random() * 256);
      const b = Math.floor(Math.random() * 256);
      const colorChart = `rgba(${r}, ${g}, ${b})`;
      const colorChartPredicted = `rgba(${r}, ${g}, ${b}, 0.7)`;

      // Obter informações sobre o período
      let periodicityLabel = 'Período';

      // Se o período estiver no formato mês-ano (MM-YYYY), criar um rótulo mais descritivo
      if (periodicity.includes('-') && /^\d{1,2}-\d{4}$/.test(periodicity)) {
        // Extrair mês e ano do período
        const [monthStr, yearStr] = periodicity.split('-');

        // IMPORTANTE: O mês no ID é 1-12 (janeiro=1, fevereiro=2, etc.)
        // Vamos usar exatamente esse valor no content
        const month = parseInt(monthStr, 10); // Mês real (1-12)
        const displayMonth = month - 1; // Mês ajustado (0-11) para usar no Date
        const year = parseInt(yearStr, 10);

        console.log(`CRITICAL FIX: Creating label for month-year: ${monthStr}-${yearStr}`);
        console.log(`CRITICAL FIX: month=${month} (1-12), displayMonth=${displayMonth} (0-11), year=${year}`);

        // Usar o mês ajustado (0-11) para obter o nome do mês
        const date = new Date(year, displayMonth, 1);
        const monthName = date.toLocaleString('pt-BR', { month: 'long' });

        // Usar o formato com espaço: "Abril / 2025"
        periodicityLabel = `${monthName.charAt(0).toUpperCase() + monthName.slice(1)} / ${year}`;

        console.log(`CRITICAL FIX: Generated label: ${periodicityLabel}`);
      }

      result = await prisma.productivity.create({
        data: {
          buildingPercentage: data.buildingPercentage,
          predictedPeriodPercentage: data.predictedPeriodPercentage,
          realPeriodPercentage: data.realPeriodPercentage,
          description: data.description,
          toolsDescription: data.toolsDescription,
          workersQuantity: data.workersQuantity,
          toolsQuantity: data.toolsQuantity,
          startDate: data.startDate,
          endDate: data.endDate,
          periodicity: { connect: { id: periodicity } },
          service: { connect: { id: serviceId } },
          proposal: { connect: { id: proposalId } },
          repairBudget: { connect: { id: repairBudgetId } },
          label: periodicityLabel,
          colorChart: colorChart,
          colorChartPredicted: colorChartPredicted,
        },
        include: { service: true, periodicity: true },
      });

      console.log('Created productivity record:', result);
      console.log('- predictedPeriodPercentage after create:', result.predictedPeriodPercentage);
      console.log('- realPeriodPercentage after create:', result.realPeriodPercentage);
    }

    // Função para converter valores Decimal do Prisma para números
    const convertDecimalToNumber = (value: any): number => {
      if (value === null || value === undefined) {
        return 0;
      }

      // Se já for um número, retornar diretamente
      if (typeof value === 'number') {
        return value;
      }

      // Se for uma string, converter para número
      if (typeof value === 'string') {
        const numValue = Number(value);
        return isNaN(numValue) ? 0 : numValue;
      }

      // Se for um objeto Decimal do Prisma
      if (typeof value === 'object') {
        // Tentar usar toString() se disponível
        if (value && typeof value.toString === 'function') {
          try {
            const numValue = Number(value.toString());
            return isNaN(numValue) ? 0 : numValue;
          } catch (e) {
            console.error('Error converting Decimal to number using toString():', e);
          }
        }

        // Tentar extrair o valor diretamente da representação do Decimal
        if (value && 's' in value && 'e' in value && 'd' in value && Array.isArray(value.d)) {
          try {
            const sign = value.s >= 0 ? 1 : -1;
            const digits = parseInt(value.d.join(''), 10);
            let numValue: number;

            if (value.e > 0) {
              numValue = sign * (digits / Math.pow(10, value.e));
            } else if (value.e < 0) {
              numValue = sign * (digits * Math.pow(10, -value.e));
            } else {
              numValue = sign * digits;
            }

            return isNaN(numValue) ? 0 : numValue;
          } catch (e) {
            console.error('Error extracting value from Decimal object:', e);
          }
        }
      }

      // Se todas as tentativas falharem, retornar 0
      return 0;
    };

    // Garantir que os valores sejam convertidos corretamente
    const ensureNumericValues = (data: any) => {
      if (!data) return data;

      // Criar uma cópia para não modificar o objeto original
      const copy = { ...data };

      // Converter predictedPeriodPercentage para número
      copy.predictedPeriodPercentage = convertDecimalToNumber(copy.predictedPeriodPercentage);

      // Converter realPeriodPercentage para número
      copy.realPeriodPercentage = convertDecimalToNumber(copy.realPeriodPercentage);

      return copy;
    };

    // Primeiro fazer o parseObject para converter todos os tipos
    const parsedResult = parseObject(result);

    // Depois garantir que os valores específicos sejam números
    const finalResult = ensureNumericValues(parsedResult);

    console.log('Saved/updated productivity record:', finalResult);
    console.log('- predictedPeriodPercentage:', finalResult.predictedPeriodPercentage);
    console.log('- realPeriodPercentage:', finalResult.realPeriodPercentage);
    console.log('- typeof predictedPeriodPercentage:', typeof finalResult.predictedPeriodPercentage);
    console.log('- typeof realPeriodPercentage:', typeof finalResult.realPeriodPercentage);

    return finalResult;
  } catch (error) {
    console.error("Erro ao salvar o histograma de orçamento de reparo:", error);
    throw error;
  }
}

export async function loadRepairBudgetHistograms(
  repairBudget: any,
  periodSelected: any
) {
  try {
    // Declarar a variável productivity no escopo da função principal
    let productivity: any = null;
    // Verificar se repairBudget e periodSelected existem
    if (!repairBudget) {
      console.error('repairBudget is undefined or null');
      return null;
    }

    if (!periodSelected) {
      console.error('periodSelected is undefined or null');
      return null;
    }

    // Verificar se as propriedades necessárias existem
    const repairBudgetId = repairBudget.id || repairBudget.repairBudgetId;
    if (!repairBudgetId) {
      console.error('repairBudgetId is undefined or null');
      return null;
    }

    const proposalId = repairBudget.proposalId;
    if (!proposalId) {
      console.error('proposalId is undefined or null');
      return null;
    }

    // Verificar se serviceScope existe e tem um id
    if (!repairBudget.serviceScope) {
      console.error('repairBudget.serviceScope is undefined or null');
      return null;
    }

    const serviceScopeId = repairBudget.serviceScope.id;
    if (!serviceScopeId) {
      console.error('serviceScopeId is undefined or null');
      return null;
    }

    // Verificar se periodSelected tem um id
    if (!periodSelected.id) {
      console.error('periodSelected.id is undefined or null');
      return null;
    }

    console.log('Loading productivity data with:');
    console.log('- repairBudgetId:', repairBudgetId);
    console.log('- periodicityId:', periodSelected.id);

    // Verificar se o período é um mês/ano (formato "MM-YYYY")
    const isMonthYearPeriod = periodSelected.id.includes('-') && /^\d{1,2}-\d{4}$/.test(periodSelected.id);

    if (isMonthYearPeriod) {
      // Extrair mês e ano do período
      const [monthStr, yearStr] = periodSelected.id.split('-');

      // IMPORTANTE: O mês no ID é 1-12 (janeiro=1, fevereiro=2, etc.)
      // Vamos armazenar exatamente esse valor no content
      const month = parseInt(monthStr, 10); // Mês real (1-12)
      const displayMonth = month - 1; // Mês ajustado (0-11) para usar no Date
      const year = parseInt(yearStr, 10);

      console.log(`CRITICAL FIX: Original month-year: ${monthStr}-${yearStr}`);
      console.log(`CRITICAL FIX: Storing month=${month} (1-12), year=${year}`);
      console.log(`CRITICAL FIX: Display month (0-11): ${displayMonth}`);

      // Criar datas de início e fim do mês
      const startDate = new Date(year, displayMonth, 1);
      const endDate = new Date(year, displayMonth + 1, 0); // Último dia do mês

      console.log(`Period is month/year: ${month}/${year}, date range:`, startDate, 'to', endDate);

      // Criar um periodicityId no formato MM-YYYY para o mês/ano selecionado
      let periodicityId = `${month}-${year}`;

      // Verificar se já existe um PlanningFrequencyItem para este período e proposta
      // Buscar por conteúdo estruturado com mês e ano exatos
      // const date = new Date(year, month, 1);
      // const monthName = date.toLocaleString('pt-BR', { month: 'long' });
      // Usar o formato com espaço: "Abril / 2025" para o label
      // const monthYearLabel = `${monthName.charAt(0).toUpperCase() + monthName.slice(1)} / ${year}`;

      console.log(`Searching for PlanningFrequencyItem with month/year: ${month}-${year} for proposal: ${proposalId}`);

      // Buscar PlanningFrequencyItem pelo conteúdo estruturado
      // Priorizar itens que já têm o repairBudgetId correto
      // Importante: month aqui é o mês real (1-12), não precisa de ajuste
      console.log(`CRITICAL: Searching for PlanningFrequencyItem with content: month=${month};year=${year}`);

      // Buscar itens com o conteúdo exato ou com variações (para compatibilidade)
      const existingPlanningItems = await prisma.planningFrequencyItem.findMany({
        where: {
          proposalId: proposalId,
          OR: [
            // Buscar pelo conteúdo exato
            { content: `month=${month};year=${year}` },
            // Buscar pelo conteúdo com espaços extras (compatibilidade)
            { content: { contains: `month=${month}` } },
            { content: { contains: `year=${year}` } }
          ]
        },
        select: {
          id: true,
          content: true,
          label: true,
          order: true,
          repairBudgetId: true,
          proposalId: true
        },
        orderBy: [
          // Ordenar primeiro por repairBudgetId (null por último)
          { repairBudgetId: 'desc' },
          // Depois pelo ID mais recente
          { id: 'desc' }
        ]
      });

      console.log(`Found ${existingPlanningItems.length} PlanningFrequencyItems for this month/year and proposal`);

      // Usar o primeiro item encontrado (o mais recente)
      const planningItemToUse = existingPlanningItems.length > 0 ? existingPlanningItems[0] : null;

      // Se não encontrou nenhum item, não criar automaticamente
      // Deixar que a função saveRepairBudgetHistogram crie o item quando necessário
      if (planningItemToUse) {
        console.log(`Using existing PlanningFrequencyItem with id: ${planningItemToUse.id}`);

        // Verificar se o item encontrado tem o repairBudgetId correto
        if (planningItemToUse.repairBudgetId !== repairBudgetId) {
          console.log(`PlanningFrequencyItem ${planningItemToUse.id} has repairBudgetId: ${planningItemToUse.repairBudgetId}, but we need: ${repairBudgetId}`);

          // Se o item não tiver repairBudgetId ou tiver um diferente, atualizá-lo
          if (!planningItemToUse.repairBudgetId) {
            console.log(`Updating PlanningFrequencyItem ${planningItemToUse.id} with repairBudgetId: ${repairBudgetId}`);

            try {
              // Atualizar o item para incluir o repairBudgetId
              await prisma.planningFrequencyItem.update({
                where: { id: planningItemToUse.id },
                data: { repairBudgetId: repairBudgetId }
              });

              // Também atualizar a relação no RepairBudget
              await prisma.repairBudget.update({
                where: { id: repairBudgetId },
                data: {
                  planningFrequencyItems: {
                    connect: { id: planningItemToUse.id }
                  }
                }
              });

              console.log(`Successfully updated PlanningFrequencyItem ${planningItemToUse.id} with repairBudgetId: ${repairBudgetId}`);
            } catch (error) {
              console.error(`Error updating PlanningFrequencyItem ${planningItemToUse.id}:`, error);
            }
          }
        }

        // Atualizar o periodicityId para usar o UUID do item encontrado
        periodicityId = planningItemToUse.id;
      } else {
        console.log(`No PlanningFrequencyItem found for this month/year and proposal. Will use default ID: ${periodicityId}`);
      }

      console.log('Searching for productivity record with:');
      console.log('- repairBudgetId:', repairBudgetId);
      console.log('- proposalId:', proposalId);
      console.log('- periodicityId (month-year):', periodicityId);

      // CRITICAL FIX: Buscar TODAS as medições para este orçamento e proposta
      // Isso garante que temos acesso a todas as medições, não apenas a do período selecionado
      const allProductivities = await prisma.productivity.findMany({
        where: {
          repairBudgetId: repairBudgetId,
          proposalId: proposalId
        },
        include: {
          service: true,
          periodicity: true, // Incluir o período para exibição no formulário
        },
        orderBy: {
          startDate: 'desc' // Ordenar pela data mais recente
        }
      });

      console.log(`CRITICAL FIX: Found ${allProductivities.length} productivity records for this repair budget`);

      // Encontrar a medição específica para o período selecionado
      productivity = allProductivities.find(p =>
        p.periodicityId === periodicityId
      );

      // Se não encontrar pelo periodicityId, tentar encontrar pelo conteúdo
      if (!productivity && isMonthYearPeriod) {
        console.log(`CRITICAL FIX: No productivity found with periodicityId ${periodicityId}, trying by content`);

        productivity = allProductivities.find(p =>
          p.periodicity &&
          p.periodicity.content &&
          p.periodicity.content.includes(`month=${month}`) &&
          p.periodicity.content.includes(`year=${year}`)
        );
      }

      // Adicionar todas as medições ao objeto de retorno
      if (productivity) {
        console.log(`CRITICAL FIX: Found productivity for selected period`);
        // Usar uma propriedade que não cause erro de tipo
        (productivity as any).allProductivities = allProductivities;
        // Marcar a medição do período atual para facilitar a identificação
        (productivity as any).isSelectedPeriod = true;
      }

      // Verificar se o período foi carregado corretamente
      if (productivity && !productivity.periodicity) {
        console.warn('Periodicity not loaded correctly, fetching it separately');

        // Buscar o período separadamente
        const periodicity = await prisma.planningFrequencyItem.findUnique({
          where: { id: periodicityId }
        });

        if (periodicity) {
          console.log('Found periodicity separately:', periodicity);
          productivity.periodicity = periodicity;
        }
      }

      // Se não encontrar com o periodicityId, tentar encontrar pela data dentro do mês
      if (!productivity) {
        console.log('No productivity record found with exact periodicityId, trying by date range');

        productivity = await prisma.productivity.findFirst({
          where: {
            repairBudgetId: repairBudgetId,
            proposalId: proposalId,
            startDate: {
              gte: startDate,
              lt: new Date(year, displayMonth + 1, 1) // Primeiro dia do próximo mês, usando displayMonth (0-11)
            }
          },
          include: {
            service: true,
            periodicity: true,
          },
          orderBy: {
            startDate: 'desc' // Obter a medição mais recente se houver várias
          }
        });

        // Verificar se o período foi carregado corretamente
        if (productivity && !productivity.periodicity) {
          console.warn('Periodicity not loaded correctly for date range search, fetching it separately');

          // Buscar o período separadamente
          const periodicity = await prisma.planningFrequencyItem.findUnique({
            where: { id: productivity.periodicityId }
          });

          if (periodicity) {
            console.log('Found periodicity separately for date range search:', periodicity);
            productivity.periodicity = periodicity;
          } else {
            // Se não encontrar o período, criar um objeto com o label formatado
            const monthNames = [
              'janeiro', 'fevereiro', 'março', 'abril', 'maio', 'junho',
              'julho', 'agosto', 'setembro', 'outubro', 'novembro', 'dezembro'
            ];

            // IMPORTANTE: month aqui já é o mês real (1-12), não precisa de ajuste para o content
            // Mas precisamos usar o displayMonth (0-11) para obter o nome do mês

            console.log(`CRITICAL FIX: Creating fallback periodicity with month=${month} (1-12), year=${year}`);

            productivity.periodicity = {
              id: periodicityId,
              content: `month=${month};year=${year}`,
              label: `${monthNames[displayMonth]} / ${year}`,
              order: 0,
              proposalId: proposalId
            };

            console.log(`CRITICAL FIX: Created fallback periodicity with month=${month}, year=${year}, label=${monthNames[displayMonth]} / ${year}`);

            console.log('Created fallback periodicity object:', productivity.periodicity);
          }
        }
      }

      console.log('Found productivity record for month/year period:', productivity);

      if (productivity) {
        // Converter os valores para números
        const convertedProductivity = convertProductivityValues(productivity);
        return convertedProductivity;
      }
    } else {
      // Para períodos normais (não mês/ano), usar a abordagem existente com SQL direto
      const productivityRecords = await prisma.$queryRaw`
        SELECT
          p.*,
          s."id" as "service_id",
          s."name" as "service_name",
          pf."id" as "periodicity_id",
          pf."content" as "periodicity_content",
          pf."label" as "periodicity_label"
        FROM "Productivity" p
        LEFT JOIN "ServiceScope" s ON p."serviceId" = s."id"
        LEFT JOIN "PlanningFrequencyItem" pf ON p."periodicityId" = pf."id"
        WHERE p."repairBudgetId" = ${repairBudgetId}
        AND p."periodicityId" = ${periodSelected.id}
        LIMIT 1
      `;

      console.log('Found productivity records via SQL:', productivityRecords);

      if (productivityRecords && Array.isArray(productivityRecords) && productivityRecords.length > 0) {
        const record = productivityRecords[0];

        console.log('Raw productivity record:');
        console.log('- predictedPeriodPercentage:', record.predictedPeriodPercentage);
        console.log('- realPeriodPercentage:', record.realPeriodPercentage);

        // Converter os valores para o formato esperado
        productivity = {
          id: record.id,
          order: record.order,
          label: record.label,
          periodicityId: record.periodicityId,
          serviceId: record.serviceId,
          startDate: record.startDate,
          endDate: record.endDate,
          buildingPercentage: Number(record.buildingPercentage),
          predictedPeriodPercentage: Number(record.predictedPeriodPercentage) || 0,
          realPeriodPercentage: Number(record.realPeriodPercentage) || 0,
          description: record.description,
          workersQuantity: record.workersQuantity,
          toolsQuantity: record.toolsQuantity,
          toolsDescription: record.toolsDescription,
          repairBudgetId: record.repairBudgetId,
          proposalId: record.proposalId,
          colorChart: record.colorChart,
          colorChartPredicted: record.colorChartPredicted,
          service: {
            id: record.service_id,
            name: record.service_name
          },
          periodicity: {
            id: record.periodicity_id,
            content: record.periodicity_content,
            label: record.periodicity_label,
            proposalId: proposalId,
            order: 0
          }
        };

        return productivity;
      } else {
        // Buscar usando o Prisma como fallback
        productivity = await prisma.productivity.findFirst({
          where: {
            repairBudgetId: repairBudgetId,
            periodicityId: periodSelected.id,
          },
          include: {
            service: true,
            periodicity: true,
          },
        });

        console.log('Found productivity record via Prisma:', productivity);

        if (productivity) {
          // Converter os valores para números
          const convertedProductivity = convertProductivityValues(productivity);
          return convertedProductivity;
        }
      }
    }

    // Função para converter valores de produtividade
    function convertProductivityValues(productivity: any) {
      console.log('Raw productivity values:');
      console.log('- predictedPeriodPercentage:', productivity.predictedPeriodPercentage);
      console.log('- realPeriodPercentage:', productivity.realPeriodPercentage);
      console.log('- typeof predictedPeriodPercentage:', typeof productivity.predictedPeriodPercentage);
      console.log('- typeof realPeriodPercentage:', typeof productivity.realPeriodPercentage);

      // Função para converter valores Decimal do Prisma para números
      const convertDecimalToNumber = (value: any): number => {
        if (value === null || value === undefined) {
          return 0;
        }

        // Se já for um número, retornar diretamente
        if (typeof value === 'number') {
          return value;
        }

        // Se for uma string, converter para número
        if (typeof value === 'string') {
          const numValue = Number(value);
          return isNaN(numValue) ? 0 : numValue;
        }

        // Se for um objeto Decimal do Prisma
        if (typeof value === 'object') {
          // Tentar usar toString() se disponível
          if (value && typeof value.toString === 'function') {
            try {
              const numValue = Number(value.toString());
              return isNaN(numValue) ? 0 : numValue;
            } catch (e) {
              console.error('Error converting Decimal to number using toString():', e);
            }
          }

          // Tentar extrair o valor diretamente da representação do Decimal
          if (value && 's' in value && 'e' in value && 'd' in value && Array.isArray(value.d)) {
            try {
              const sign = value.s >= 0 ? 1 : -1;
              const digits = parseInt(value.d.join(''), 10);
              let numValue: number;

              if (value.e > 0) {
                numValue = sign * (digits / Math.pow(10, value.e));
              } else if (value.e < 0) {
                numValue = sign * (digits * Math.pow(10, -value.e));
              } else {
                numValue = sign * digits;
              }

              return isNaN(numValue) ? 0 : numValue;
            } catch (e) {
              console.error('Error extracting value from Decimal object:', e);
            }
          }
        }

        // Se todas as tentativas falharem, retornar 0
        return 0;
      };

      // Converter os valores para números
      productivity.predictedPeriodPercentage = convertDecimalToNumber(productivity.predictedPeriodPercentage);
      productivity.realPeriodPercentage = convertDecimalToNumber(productivity.realPeriodPercentage);

      console.log('After conversion:');
      console.log('- predictedPeriodPercentage:', productivity.predictedPeriodPercentage);
      console.log('- realPeriodPercentage:', productivity.realPeriodPercentage);
      console.log('- typeof predictedPeriodPercentage:', typeof productivity.predictedPeriodPercentage);
      console.log('- typeof realPeriodPercentage:', typeof productivity.realPeriodPercentage);

      return productivity;
    }

    // Se não encontrar, retornar null em vez de criar um novo registro
    // Isso evita a criação automática de registros quando o usuário apenas abre o modal
    if (!productivity) {
      console.log('No productivity record found for this period and activity. Returning null.');
      return null;
    }
    // Garantir que os valores sejam convertidos corretamente
    const ensureNumericValues = (data: any) => {
      if (!data) return data;

      // Criar uma cópia para não modificar o objeto original
      const copy = { ...data };

      // Converter predictedPeriodPercentage para número
      if (copy.predictedPeriodPercentage !== undefined && copy.predictedPeriodPercentage !== null) {
        const predictedValue = Number(copy.predictedPeriodPercentage);
        copy.predictedPeriodPercentage = isNaN(predictedValue) ? 0 : predictedValue;
      }

      // Converter realPeriodPercentage para número
      if (copy.realPeriodPercentage !== undefined && copy.realPeriodPercentage !== null) {
        const realValue = Number(copy.realPeriodPercentage);
        copy.realPeriodPercentage = isNaN(realValue) ? 0 : realValue;
      }

      return copy;
    };

    // Primeiro fazer o parseObject para converter todos os tipos
    const parsedProductivity = parseObject(productivity) as any;

    // Depois garantir que os valores específicos sejam números
    const finalResult = ensureNumericValues(parsedProductivity);

    console.log('Parsed productivity record:', finalResult);
    console.log('predictedPeriodPercentage:', finalResult.predictedPeriodPercentage);
    console.log('realPeriodPercentage:', finalResult.realPeriodPercentage);
    console.log('typeof predictedPeriodPercentage:', typeof finalResult.predictedPeriodPercentage);
    console.log('typeof realPeriodPercentage:', typeof finalResult.realPeriodPercentage);

    return finalResult;
  } catch (error) {
    console.error(error);
  }
}

export async function removeRepairBudgetHistogram(id: string) {
  try {
    await prisma.productivity.delete({
      where: { id },
    });
    return { message: "Serviço removido com sucesso!" };
  } catch (error) {
    console.error(error);
  }
}

export async function generateReportHistogram(
  reportTemplateId: string,
  params: any
) {
  try {
    const data = await prisma.productivity.findMany({
      where: {
        proposalId: params.proposalId,
      },
      include: {
        periodicity: true,
        service: true,
        repairBudget: true,
      },
    });

    const variables = {
      items: data
        .filter((item) => item.startDate) // Filtra itens que possuem startDate
        .map((item) => {
          const date = item?.startDate ? new Date(item.startDate) : new Date();
          const mesAbreviado = date
            .toLocaleString("pt-BR", { month: "short" })
            .replace(".", ""); // Formato Jan/2025
          const mesAno = `${mesAbreviado}/${date.getFullYear()}`;

          return {
            date: mesAno,
            service: item.service?.name || "Desconhecido",
            serviceCost: formatCurrency(
              Number(item.repairBudget?.serviceCost) || 0
            ),
            buildingPercentage: `${Number(item.buildingPercentage || 0).toFixed(
              2
            )}%`,
            predictedPeriodPercentage: `${Number(
              item.predictedPeriodPercentage || 0
            ).toFixed(2)}%`,
            realPeriodPercentage: `${Number(
              item.realPeriodPercentage || 0
            ).toFixed(2)}%`,
          };
        }),
    };

    const reportTemplate = await prisma.reportTemplate.findUnique({
      where: { id: reportTemplateId },
    });

    const fileName = `${Date.now()}-histogram-report`;
    const replacedFileEditor = await replaceFromFileEditorIdAndVariables(
      `${reportTemplate?.fileEditorId}`,
      variables,
      fileName
    );

    if (!replacedFileEditor?.id) throw Error("Failed to save file editor");
    const fileEditorId = replacedFileEditor?.id;
    return {
      fileEditorId,
    };
  } catch (error) {
    console.error(error);
  }
}
