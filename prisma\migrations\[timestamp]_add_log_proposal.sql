-- CreateTable
CREATE TABLE "LogProposal" (
    "id" TEXT NOT NULL,
    "proposalId" TEXT NOT NULL,
    "oldStatus" "ProposalSituation" NOT NULL,
    "newStatus" "ProposalSituation" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" TEXT,

    CONSTRAINT "LogProposal_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "LogProposal_proposalId_idx" ON "LogProposal"("proposalId");

-- AddForeignKey
ALTER TABLE "LogProposal" ADD CONSTRAINT "LogProposal_proposalId_fkey" FOREIGN KEY ("proposalId") REFERENCES "Proposal"("id") ON DELETE CASCADE ON UPDATE CASCADE;