import { NextResponse } from "next/server";
import { loadReportTemplates } from "@/src/actions/report-template";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const page = Number(searchParams.get("page")) || 1;
    const pageSize = Number(searchParams.get("pageSize")) || 10;
    const search = searchParams.get("search") || "";
    const type = searchParams.get("type") || undefined;

    // Determinar quais tipos excluir com base no tipo solicitado
    let excludeTypes = ["REPORT", "PROJECT"];

    // Se um tipo específico for solicitado, não o excluímos da listagem
    if (type) {
      excludeTypes = excludeTypes.filter((t) => t !== type);
    }

    const data = await loadReportTemplates(
      page,
      pageSize,
      search,
      type,
      excludeTypes
    );
    return NextResponse.json(data);
  } catch (error) {
    console.log(error);
    return NextResponse.json(
      { error: "Erro ao carregar templates" },
      { status: 500 }
    );
  }
}
