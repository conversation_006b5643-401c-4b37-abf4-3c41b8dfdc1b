"use client";

import React, { useState, useRef, useEffect } from "react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/src/components/ui/tooltip";

interface TouchTooltipProps {
  content: React.ReactNode;
  children: React.ReactNode;
  maxWidth?: string;
  className?: string;
}

export function TouchTooltip({
  content,
  children,
  maxWidth = "300px",
  className = "",
}: TouchTooltipProps) {
  const [isOpen, setIsOpen] = useState(false);
  const triggerRef = useRef<HTMLDivElement>(null);

  // Detecta se é um dispositivo móvel
  const isMobile = () => {
    if (typeof window !== "undefined") {
      return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      );
    }
    return false;
  };

  // Fecha o tooltip quando clicar fora dele
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  const handleTriggerClick = () => {
    if (isMobile()) {
      setIsOpen(!isOpen);
    }
  };

  return (
    <TooltipProvider>
      <Tooltip open={isMobile() ? isOpen : undefined}>
        <TooltipTrigger asChild>
          <div
            ref={triggerRef}
            onClick={handleTriggerClick}
            className={`cursor-pointer ${className}`}
            style={{ touchAction: "manipulation" }}
          >
            {children}
          </div>
        </TooltipTrigger>
        <TooltipContent
          className={`whitespace-pre-wrap bg-white text-black p-3 shadow-lg rounded-md border border-gray-200`}
          style={{ maxWidth }}
          sideOffset={5}
        >
          {content}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
