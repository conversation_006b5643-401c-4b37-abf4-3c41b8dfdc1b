"use client";

import { toast } from "@/src/hooks/use-toast";

// Função para exibir toasts usando a implementação padrão
export function useCenterToast() {
  const centerToast = (props: {
    title: string;
    description?: string;
    variant?: "default" | "destructive";
  }) => {
    const { title, description, variant = "default" } = props;

    return toast({
      title,
      description,
      variant,
    });
  };

  return {
    centerToast,
    success: (title: string, description?: string) =>
      centerToast({ title, description, variant: "default" }),
    error: (title: string, description?: string) =>
      centerToast({ title, description, variant: "destructive" }),
  };
}
