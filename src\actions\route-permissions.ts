"use server";

import { prisma } from "@/src/lib/prisma";
import { auth } from "@/src/providers/auth";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

// Verificar se o usuário atual é OWNER
async function requireOwnerRole() {
  const session = await auth();
  if (!session?.user?.id) {
    throw new Error("User not authenticated");
  }

  const { role } = await getCurrentOrganization();
  if (role !== "OWNER") {
    throw new Error("Only OWNER users can manage route permissions");
  }

  return session;
}

// Listar todas as permissões de rota da organização
export async function getRoutePermissions() {
  try {
    const { organizationId } = await getCurrentOrganization();

    const permissions = await prisma.routePermission.findMany({
      where: {
        organizationId,
      },
      orderBy: {
        route: "asc",
      },
    });

    return permissions;
  } catch (error) {
    console.error("Error fetching route permissions:", error);
    throw error;
  }
}

// Verificar se um usuário tem acesso a uma rota específica
export async function checkRouteAccess(route: string): Promise<boolean> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return false;
    }

    const { organizationId, role } = await getCurrentOrganization();

    // OWNER sempre tem acesso a tudo
    if (role === "OWNER") {
      return true;
    }

    // Buscar permissão do usuário para esta rota
    const userPermission = await prisma.userRoutePermission.findUnique({
      where: {
        userId_route_organizationId: {
          userId: session.user.id,
          route,
          organizationId,
        },
      },
    });

    // Se não há permissão configurada, acesso liberado (padrão)
    if (!userPermission) {
      return true;
    }

    // Retornar o status da permissão do usuário
    return userPermission.enabled;
  } catch (error) {
    console.error("Error checking route access:", error);
    // Em caso de erro, negar acesso por segurança
    return false;
  }
}

// Criar ou atualizar permissão de rota
export async function upsertRoutePermission(route: string) {
  try {
    await requireOwnerRole();
    const { organizationId } = await getCurrentOrganization();

    const permission = await prisma.routePermission.upsert({
      where: {
        route_organizationId: {
          route,
          organizationId,
        },
      },
      update: {
        updatedAt: new Date(),
      },
      create: {
        route,
        organizationId,
      },
    });

    return permission;
  } catch (error) {
    console.error("Error upserting route permission:", error);
    throw error;
  }
}

// Deletar permissão de rota
export async function deleteRoutePermission(route: string) {
  try {
    await requireOwnerRole();
    const { organizationId } = await getCurrentOrganization();

    await prisma.routePermission.delete({
      where: {
        route_organizationId: {
          route,
          organizationId,
        },
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Error deleting route permission:", error);
    throw error;
  }
}

// Função para gerenciar permissões de usuário
export async function upsertUserRoutePermission(
  userId: string,
  route: string,
  enabled: boolean
) {
  try {
    await requireOwnerRole();
    const { organizationId } = await getCurrentOrganization();

    // Agora criar/atualizar a permissão do usuário
    const userPermission = await prisma.userRoutePermission.upsert({
      where: {
        userId_route_organizationId: {
          userId,
          route,
          organizationId,
        },
      },
      update: {
        enabled,
        updatedAt: new Date(),
      },
      create: {
        userId,
        route,
        enabled,
        organizationId,
      },
    });

    return userPermission;
  } catch (error) {
    console.error("Error upserting user route permission:", error);
    throw error;
  }
}

// Função para remover permissão de usuário específico
export async function deleteUserRoutePermission(userId: string, route: string) {
  try {
    await requireOwnerRole();
    const { organizationId } = await getCurrentOrganization();

    await prisma.userRoutePermission.delete({
      where: {
        userId_route_organizationId: {
          userId,
          route,
          organizationId,
        },
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Error deleting user route permission:", error);
    throw error;
  }
}

// Obter todas as rotas disponíveis no sistema
export async function getAllSystemRoutes() {
  // Lista de todas as rotas baseada no routeData
  const routes = [
    // Painel de controle
    "/views/control-panel",

    // CRM
    "/views/crm/proposals/management",
    "/views/crm/customers",
    "/views/crm/customer-projects-history",
    "/views/crm/proposals",
    "/views/crm/proposals/by-period",
    "/views/crm/proposals/to-start",
    "/views/crm/proposals/accepted",
    "/views/crm/proposals/completed",
    "/views/crm/proposals/lost",
    "/views/crm/kpi",

    // Laudos e Relatórios
    "/views/inspection-report",
    "/views/project-report",
    "/views/construction-inspection",
    "/views/consultancy-report",

    // Configuração
    "/views/services-scopes",
    "/views/proposal-templates",
    "/views/labor-equipaments",
    "/views/manage-members",
    "/views/manage-permissions",
  ];

  return Promise.all(
    routes.map(async (route) => ({
      route,
      title: await getRouteTitle(route),
    }))
  );
}

// Função auxiliar para obter o título da rota
async function getRouteTitle(route: string): Promise<string> {
  const routeTitles: Record<string, string> = {
    // Painel de controle
    "/views/control-panel": "Painel de controle",

    // CRM
    "/views/crm/proposals/management": "Kanban de projetos",
    "/views/crm/customers": "Cadastrar cliente",
    "/views/crm/customer-projects-history": "Histórico de projetos por cliente",
    "/views/crm/proposals": "Cadastrar proposta",
    "/views/crm/proposals/by-period": "Propostas por período",
    "/views/crm/proposals/to-start": "Contratos a iniciar",
    "/views/crm/proposals/accepted": "Contratos em andamento",
    "/views/crm/proposals/completed": "Contratos concluídos",
    "/views/crm/proposals/lost": "Contratos perdidos",
    "/views/crm/kpi": "KPI",

    // Laudos e Relatórios
    "/views/inspection-report": "Laudo de Inspeção",
    "/views/project-report": "Relatório de Projeto",
    "/views/construction-inspection": "Fiscalização e Gerenciamento",
    "/views/consultancy-report": "Consultoria",

    // Configuração
    "/views/services-scopes": "Escopo de serviços",
    "/views/proposal-templates": "Templates do Sistema",
    "/views/labor-equipaments": "Equipamentos e mão de obra",
    "/views/manage-members": "Gerenciar membros",
    "/views/manage-permissions": "Permissões de acesso",
  };

  return routeTitles[route] || route;
}

// Buscar permissões do usuário para várias rotas de uma vez
export async function getUserRoutesPermissions(routes: string[]): Promise<Record<string, boolean>> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      // Se não autenticado, nenhuma permissão
      return Object.fromEntries(routes.map(route => [route, false]));
    }
    const { organizationId, role } = await getCurrentOrganization();
    // OWNER tem acesso a tudo
    if (role === "OWNER") {
      return Object.fromEntries(routes.map(route => [route, true]));
    }
    // Buscar todas permissões do usuário para as rotas informadas
    const userPermissions = await prisma.userRoutePermission.findMany({
      where: {
        userId: session.user.id,
        organizationId,
        route: { in: routes },
      },
    });
    // Montar mapa de permissões
    const permissionMap: Record<string, boolean> = {};
    for (const route of routes) {
      const found = userPermissions.find(p => p.route === route);
      // Se não há permissão configurada, acesso liberado (padrão)
      permissionMap[route] = found ? found.enabled : true;
    }
    return permissionMap;
  } catch (error) {
    console.error("Error checking user routes permissions:", error);
    // Em caso de erro, negar acesso por segurança
    return Object.fromEntries(routes.map(route => [route, false]));
  }
}
