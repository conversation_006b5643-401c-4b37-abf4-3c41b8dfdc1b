import { ChartConfig, ChartContainer } from "@/src/components/ui/chart";
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

const WeatherImpactReportChart = ({ chartData }: { chartData: any[] }) => {
  const chartConfig: ChartConfig = {
    acceptedProposalAmount: {
      label: "IGRf",
      color: "rgb(22 163 74)",
    },
    teamPerformance: {
      label: "Custo",
      color: "rgb(37 99 235)",
    },
  };

  return (
    <div className="flex flex-col gap-4 w-full h-full">
      <ChartContainer
        config={chartConfig}
        className="min-h-[40rem] w-full max-h-full"
      >
        <LineChart
          width={500}
          height={300}
          data={chartData}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="data" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Line
            type="monotone"
            dataKey="percentage"
            name="Produtividade (%)"
            stroke="#8884d8"
            activeDot={{ r: 8 }}
          />
          <Line
            type="monotone"
            dataKey="volume"
            name="Volume (m³)"
            stroke="#82ca9d"
          />
        </LineChart>
      </ChartContainer>
    </div>
  );
};

export default WeatherImpactReportChart;
