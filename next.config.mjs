/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  // reactStrictMode: false, // Re-render for dev
  reactStrictMode: true,
  experimental: {
    serverActions: {
      bodySizeLimit: "100mb",
    },
  },
  webpack: (config, { isServer }) => {
    // Adiciona a configuração do canvas do next.config.js
    config.externals = [...(config.externals || []), { canvas: "canvas" }];

    if (!isServer) {
      // Não incluir o nodemailer no bundle do cliente
      config.resolve.fallback = {
        ...config.resolve.fallback,
        nodemailer: false,
      };
    }
    return config;
  },
};

export default nextConfig;
