"use client";

import { motion } from "framer-motion";
import { SetStateAction, useState } from "react";
import { cn } from "../lib/utils";
import { Button } from "./ui/button";

interface StepsProps {
	steps: Step[];
}

export interface Step {
	template: React.ReactNode;

	onContinueCallback: (
		goToStep: (value: SetStateAction<number>) => void
	) => void;
	onBackCallback: (goToStep: (value: SetStateAction<number>) => void) => void;
}

export function AppSteps({ steps }: StepsProps) {
	const [currentStep, setCurrentStep] = useState(1);

	const handleStepClick = (step: number) => {
		if (step <= currentStep) {
			setCurrentStep(step);
		}
	};

	return (
		<div className="w-full p-2">
			<div className="relative  mb-8 pt-6">
				{/* Progress bar */}
				<div className="absolute  top-12 left-0 w-full h-1 bg-gray-300">
					<motion.div
						className="h-full bg-green-500"
						initial={{ width: "0%" }}
						animate={{
							width: `${((currentStep - 1) / (steps.length - 1)) * 100}%`,
						}}
						transition={{ type: "spring", stiffness: 100, damping: 20 }}
					/>
				</div>
				{/* Steps */}
				<div className="relative flex justify-between">
					{steps.map((step, index) => (
						<div
							key={index + 1}
							className={`flex flex-col items-center ${
								index + 1 <= currentStep
									? "cursor-pointer"
									: "cursor-not-allowed"
							}`}
							onClick={() => handleStepClick(index + 1)}
						>
							<motion.div
								className={`z-10 w-12 h-12 rounded-full flex items-center justify-center text-white font-bold ${
									index + 1 < currentStep
										? "bg-green-500"
										: index + 1 === currentStep
										? "bg-blue-500"
										: "bg-gray-300"
								}`}
								initial={false}
								animate={{
									scale: index + 1 === currentStep ? 1.2 : 1,
									transition: { type: "spring", stiffness: 300, damping: 20 },
								}}
							>
								<span>{index + 1}</span>
							</motion.div>
							<motion.div
								className="mt-2 text-sm font-medium text-center"
								initial={false}
								animate={{
									opacity: index + 1 === currentStep ? 1 : 0.5,
								}}
							></motion.div>
						</div>
					))}
				</div>
			</div>
			<div>
				{steps[currentStep - 1].template}
				<div className="mt-6 flex justify-center items-center gap-3 flex-col">
					<Button
						onClick={() => {
							const step = steps[currentStep - 1];
							step.onContinueCallback?.(setCurrentStep);
						}}
						className={cn(
							"bg-blue-500 hover:bg-blue-600 font-bold",
							currentStep === steps.length && "bg-green-500 hover:bg-green-600"
						)}
					>
						{currentStep === steps.length ? "Finalizar" : "Continuar"}
					</Button>
					<Button
						variant="link"
						onClick={() => {
							const step = steps[currentStep - 1];
							step.onBackCallback?.(setCurrentStep);
						}}
					>
						Voltar
					</Button>
				</div>
			</div>
		</div>
	);
}

export const  StepContainer = ({ children }: { children: React.ReactNode }) => {
	return (
		<div className="h-[60vh] lg:h-[53vh] overflow-auto p-4 border rounded-sm">
			{children}
		</div>
	);
};