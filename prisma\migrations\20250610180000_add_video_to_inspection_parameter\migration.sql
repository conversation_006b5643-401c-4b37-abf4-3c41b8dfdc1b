-- Adiciona a tabela Video e a relação com InspectionParameter

CREATE TABLE "Video" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "description" TEXT NOT NULL,
    "fileId" TEXT,
    "inspectionParameterId" TEXT,
    "order" INTEGER,
    CONSTRAINT "Video_fileId_fkey" FOREIGN KEY ("fileId") REFERENCES "File" ("id") ON DELETE SET NULL,
    CONSTRAINT "Video_inspectionParameterId_fkey" FOREIGN KEY ("inspectionParameterId") REFERENCES "InspectionParameter" ("id") ON DELETE SET NULL
); 