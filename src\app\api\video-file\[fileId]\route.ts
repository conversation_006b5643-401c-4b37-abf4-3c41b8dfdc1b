"use server";
import { prisma } from "@/src/lib/prisma";
import { storageProvider } from "@/src/lib/storage";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: { fileId: string } }
) {
  try {
    const fileId = decodeURIComponent(params.fileId);
    // Buscar o arquivo no banco de dados
    const fileRecord = await prisma.file.findUnique({
      where: { id: fileId },
    });
    if (!fileRecord) {
      return NextResponse.json({ error: "File record not found." }, { status: 404 });
    }
    // Buscar o arquivo no storage
    const file = await storageProvider.get(fileRecord.path);
    if (!file || !file.stream) {
      return NextResponse.json({ error: "File not found in storage." }, { status: 404 });
    }
    // Verificar se é vídeo
    if (!file.contentType || !file.contentType.startsWith("video/")) {
      return NextResponse.json({ error: "File is not a video." }, { status: 415 });
    }
    // Servir como inline
    return new Response(file.stream, {
      headers: {
        "Content-Type": file.contentType,
        "Content-Disposition": `inline; filename=\"${file.name}\"`,
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0"
      },
    });
  } catch (error) {
    let errorMessage = "Failed to fetch the video file.";
    if (error instanceof Error) {
      errorMessage = `Error: ${error.message}`;
    }
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
} 