/*
  Warnings:

  - Added the required column `organizationId` to the `FileEditor` table without a default value. This is not possible if the table is not empty.
  - Added the required column `organizationId` to the `Labor` table without a default value. This is not possible if the table is not empty.
  - Added the required column `organizationId` to the `ProposalTemplate` table without a default value. This is not possible if the table is not empty.
  - Added the required column `organizationId` to the `ReportTemplate` table without a default value. This is not possible if the table is not empty.
  - Added the required column `organizationId` to the `ServiceScope` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "FileEditor" ADD COLUMN     "organizationId" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "Labor" ADD COLUMN     "organizationId" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "ProposalTemplate" ADD COLUMN     "organizationId" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "ReportTemplate" ADD COLUMN     "organizationId" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "ServiceScope" ADD COLUMN     "organizationId" TEXT NOT NULL;

-- AddForeignKey
ALTER TABLE "ServiceScope" ADD CONSTRAINT "ServiceScope_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProposalTemplate" ADD CONSTRAINT "ProposalTemplate_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReportTemplate" ADD CONSTRAINT "ReportTemplate_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FileEditor" ADD CONSTRAINT "FileEditor_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Labor" ADD CONSTRAINT "Labor_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
